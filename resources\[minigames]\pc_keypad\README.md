
# DUI Keypad

**NOTE - This is a dev resource and is not setup to do anything on its own. It is up to you to decide what do with it !**

This serves as a template / guide on how to create DUI's to be used to replace textures on props and interact with the DUI's. Not all DUI's need to be interacted in the way that I did, but for more complex instances this may be useful.

I do not like how every FiveM resource has become a cash grab, feel free to use however you want, but please do not charge for any resource where this is used.

### Usage
`/createkeypad` - Will create a demo keypad

### Resmon
Idle usage is 0.00ms, while keypad is in use:

![Resmon](https://i.imgur.com/nnPPT1d.png)

### Preview

https://streamable.com/e/g9st9h
