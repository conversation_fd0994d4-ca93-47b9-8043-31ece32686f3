-- CONFIG À FAIRE --
Config = {}

Config.GlobalCooldown = 45 -- 10 minutes de cooldown global

Config.General = {
    Stores = {
        minPolice = 1,
        requiredItemRegister = 'lockpick',
        storeCooldown = 300, -- 5 minutes de cooldown par magasin
        registerReward = {
            name = 'cash',
            minAmount = 200,
            maxAmount = 500
        },
        requiredItemSafe = 'drill',  -- Item requis pour braquer les coffres-forts
        rewardSafe = {name = 'cash', minAmount = 10000, maxAmount = 15000},  -- Item et quantités pour les coffres-forts
    }
}

Config.Stores = {
    {coords = vector3(24.129, -1346.156, 29.497), registers = {vector3(24.912860870361, -1344.9533691406, 29.674966812134), vector3(25.101343154907, -1347.1037597656, 29.693027496338)}, safe = vector3(28.211114883423, -1338.8779296875, 28.952461242676)},
    {coords = vector3(-47.522, -1756.857, 29.421), registers = {vector3(24.************, -1342.**********, 29.************), vector3(-706.***********, -915.***********, 19.************)}, safe = vector3(-710.***********, -904.***********, 19.************)},
    {coords = vector3(1165.05, -324.49, 69.2), registers = {vector3(144.***********, -1045.**********, 29.************), vector3(144.***********, -1045.**********, 29.************)}, safe = vector3(144.***********, -1045.**********, 29.************)},
    -- Ajoutez les autres dépanneurs ici
}

Config.General.bank = {
    pedItem = {name = 'water', minAmount = 1, maxAmount = 1},
    requiredItemDoor = "water",
    requiredItemVaultDoor = "thermite",
    requiredItemVault = 'drill',
    reward = {name = 'cash', minAmount = 10000, maxAmount = 15000},
    fleecaCooldown = 30,
    minPolice = 2,
}
Config.Banks = {
    [1] = {
        pedCoords = vec4(146.76, -1043.75, 29.37, 176.14),
        thermiteHack = vector3(148.***********, -1046.**********, 29.************),
        thermitePlayerPos = vec4(149.3, -1046.81, 29.35, 159.02), -- Position du joueur pour l'animation thermite
        cardPlayerPos = vec4(146.79, -1045.82, 29.37, 250.3), -- Position du joueur pour l'animation d'insertion de carte
        vaultDoorCoords = vector3(148.***********, -1044.**********, 29.************),
        doorId = 10, -- ID de la porte ox_doorlock trouvée (porte principale)
        doorName = "test", -- Nom de la porte ox_doorlock trouvée
        secondaryDoors = {9}, -- REMPLACEZ {} par l'ID de test2 (ex: {11})
        minPolice = 1,
        cooldown = 600,
        securitySystemCoords = vector3(147.***********, -1046.**********, 29.********),
        policeReset = vector3(147.***********, -1046.**********, 29.********),
        object = 'v_ilev_gb_vauldr',  -- Nom de l'objet de la porte sous forme de chaîne
        heading = {
            closed = 250.0,
            open = 160.0
        },
        vaultCoords = {
            vector3(149.90960693359, -1044.6368408203, 29.674205780029),
            vector3(151.42459106445, -1046.7478027344, 29.610012054443),
            vector3(150.81745910645, -1050.0551757812, 29.645708084106),
            vector3(147.83428955078, -1050.9426269531, 29.678211212158),
            vector3(146.60095214844, -1048.2825927734, 29.668821334839),
                        },

    },
    -- [2] = {  
    --     pedCoords = vec4(145.4, -1045.27, 29.37, 322.64),
    --     thermiteHack = vec4(145.4, -1045.27, 29.37, 322.64),
    --     vaultDoorCoords = vector3(148.***********, -1044.**********, 29.************),
    --     minPolice = 1,
    --     cooldown = 30,
    --     securitySystemCoords = vec4(145.4, -1045.27, 29.37, 322.64),
    --     policeReset = vector3(147.***********, -1046.**********, 29.********),
    --     object = 'v_ilev_gb_vauldr',  -- Nom de l'objet de la porte sous forme de chaîne
    --     heading = {
    --         closed = 250.0,
    --         open = 160.0
    --     },
    --     vaultCoords = {
    --                     vector3(150.***********, -1049.**********, 29.***********),
    --                     vector3(147.***********, -1050.**********, 29.************),
    --                     vector3(146.***********, -1048.25, 29.************),
    --                     vector3(146.***********, -1048.25, 29.************),
    --                     vector3(146.***********, -1048.25, 29.************),
                        
    --                     },

    -- },
    }
    
Config.Paletobank = {
    cutWireCoords = vector3(-109.***********, 6483.**********, 31.************),
    requiredItemsCutWire = 'water',
    mainDoorCoords = vec4(-104.60, 6473.44, 31.80, 134.18), -- Coordonnées corrigées
    mainDoorObject = 'v_ilev_cbankvauldoor01',
    mainDoorHack = vector3(-106.***********, 6470.********, 31.************), -- Coordonnées du hack
    mainDoorPlayerPos = vec4(-104.60, 6473.44, 31.80, 134.18), -- Position du joueur pour l'animation (coordonnées corrigées)
    requiredItemMainDoor = 'water', -- Item requis pour ouvrir la porte principale
    doorId = 11, -- ID de la porte ox_doorlock trouvée (porte principale)
    heading = {
        closed = 45.0,
        open = 134.18
    },
    c4DoorCoords = vector3(-105.***********, 6474.**********, 32.************), -- Coordonnées de la porte C4 (corrigées avec heading)
    c4DoorObject = 'v_ilev_cbankvaulgate02', -- Objet de la porte C4 à déplacer
    c4DoorHiddenCoords = vector3(-105.***********, 6478.**********, 30.************), -- Coordonnées où cacher la porte après explosion
    c4DoorOriginalCoords = vec4(-106.***********, 6476.**********, 31.**********, 315.0), -- Coordonnées originales pour le reset
    requiredItemC4Door = 'water', -- Item requis pour faire exploser la deuxième porte
    c4DoorPlayerPos = vec4(-104.60, 6473.44, 31.80, 134.18), -- Position du joueur pour l'animation (coordonnées corrigées)
    minPolice = 1,
    cooldown = 300,
    reward = {name = 'cash', minAmount = 10000, maxAmount = 15000},
    vaultRewardRequiredItem = 'drill',
    vaultRewardCoords = {
        vector3(-107.***********, 6473.8046875, 31.************),
        vector3(-107.***********, 6475.**********, 31.************),
        vector3(-106.***********, 6478.**********, 31.************),
        vector3(-102.***********, 6478.**********, 31.************),
        vector3(-102.78022003174, 6475.2504882812, 31.962873458862)
},
}

Config.Vangelico = {
    minPolice = 1,
    cooldown = 120,
    hackCoords = vector3(-631.08758544922, -230.60554504395, 38.005035400391),
    requiredItem = 'water',
    requiredItemVitrine = 'weapon_crowbar',
    mainDoorID = 1,
    mainDoorPoliceUnlock = vector3(-630.71545410156, -238.74420166016, 38.521175384521),
    mainDoorCooldown = 30,
    rewards = {
        {name = 'water', min = 1, max = 3, chance = 30},
        {name = 'burger', min = 1, max = 3, chance = 60}
    },
    vitrines = {
    [1] = {coords = vec3(-626.83, -235.35, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 36.17},
    [2] = {coords = vec3(-625.81, -234.7, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 36.17},
    [3] = {coords = vec3(-626.95, -233.14, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 216.17},
    [4] = {coords = vec3(-628.0, -233.86, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 216.17},
    [5] = {coords = vec3(-625.7, -237.8, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 216.17},
    [6] = {coords = vec3(-626.7, -238.58, 38.05), isOpened = false, isBusy = false,  rayFire = 'DES_Jewel_Cab2', heading = 216.17},
    [7] = {coords = vec3(-624.55, -231.06, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 305.0},
    [8] = {coords = vec3(-623.13, -232.94, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 305.0},
    [9] = {coords = vec3(-620.29, -234.44, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 216.17},
    [10] = {coords = vec3(-619.15, -233.66, 38.05), isOpened = false, isBusy = false,  rayFire = 'DES_Jewel_Cab3', heading = 216.17},
    [11] = {coords = vec3(-620.19, -233.44, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 36.17},
    [12] = {coords = vec3(-617.63, -230.58, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab2', heading = 305.0},
    [13] = {coords = vec3(-618.33, -229.55, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 305.0},
    [14] = {coords = vec3(-619.7, -230.33, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 125.0},
    [15] = {coords = vec3(-620.95, -228.6, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 125.0},
    [16] = {coords = vec3(-619.79, -227.6, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab2', heading = 305.0},
    [17] = {coords = vec3(-620.42, -226.6, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 305.0},
    [18] = {coords = vec3(-623.94, -227.18, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 36.17},
    [19] = {coords = vec3(-624.91, -227.87, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 36.17},
    [20] = {coords = vec3(-623.94, -228.05, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab2', heading = 216.17},
},
}

Config.Pacific = {
    minPolice = 1,
    cooldown = 120,
    pedCoords = vec4(234.81, 226.58, 105.29, 235.53),
    pedChargeAmount = 1000,
    secondaryDoorId = 4,
    secondaryDoorCoords = vector3(255.87326049805, 222.1806640625, 106.66357421875),
    keypadDoorId = 12,
    keypadCoords = {
        x = 236.382,
        y = 228.216,
        z = 106.651,
        rot = {x = -0.097, y = -1.829, z = -20.165}
    },
    hackCoords = vector3(261.55975341797, 205.58793640137, 110.21620941162),
    c4DoorCoords = vector3(261.73654174805, 221.32722473145, 106.47142028809),
    c4DoorId = 6,
    c4DoorRequiredItem = 'water',
    cardCoords = {
        x = 264.377,
        y = 220.598,
        z = 101.594,
        rot = {x = -99.697, y = 1.392, z = -0.804}
    },
    rewardCardCoords = 'water',
    vaultDoorObject = 'v_ilev_bk_vaultdoor',
    vaultDoorHeading = {
        closed = 160.0,
        open = 60.0
    },
    vaultDoorCoords = vector3(252.91778564453, 228.52996826172, 101.994972229),
    secondVaultDoorId = 2,
    secondVaultDoorCoords = vector3(252.98129272461, 220.70207214355, 101.84303283691),
    secondVaultDoorRequiredItem = 'water',
    -- Configuration des guards
    guards = {
        weapon = 'WEAPON_SMG', -- Arme des guards
        model = 's_m_y_swat_01', -- Modèle des guards
        coords = {
            vec4(261.15, 216.7, 101.68, 96.71),
            vec4(259.99, 214.09, 101.68, 38.18),
            vec4(249.62, 217.62, 101.68, 339.5),
            vec4(254.46, 216.44, 101.68, 4.58),
            vec4(255.65, 218.91, 101.68, 152.47)
        }
    },
    trolleys = {
        [1] = {
            coords = vec4(265.96, 214.94, 100.68, 70.56),
            model = GetHashKey("hei_prop_hei_cash_trolly_01"),
            reward = {
                money = {min = 5000, max = 15000},
                items = {
                    {name = "water", min = 1, max = 3, chance = 30},
                    {name = "burger", min = 1, max = 2, chance = 20}
                }
            }
        },
        [2] = {
            coords = vec4(265.01, 212.43, 100.68, 68.64),
            model = GetHashKey("hei_prop_hei_cash_trolly_01"),
            emptyModel = GetHashKey("hei_prop_hei_cash_trolly_03"),
            reward = {
                money = {min = 3000, max = 10000},
                items = {
                    {name = "water", min = 1, max = 3, chance = 30},
                    {name = "burger", min = 1, max = 2, chance = 20}
                }
            }
        },
        [3] = {
            coords = vec4(262.38, 213.28, 100.68, -20.57),
            model = GetHashKey("hei_prop_hei_cash_trolly_01"),
            emptyModel = GetHashKey("hei_prop_hei_cash_trolly_03"),
            reward = {
                money = {min = 4000, max = 12000},
                items = {
                    {name = "water", min = 1, max = 3, chance = 30},
                    {name = "burger", min = 1, max = 2, chance = 20}
                }
            }
        },
        [4] = {
            coords = vec4(263.33, 215.96, 100.68, 161.58),
            model = GetHashKey("hei_prop_hei_cash_trolly_01"),
            emptyModel = GetHashKey("hei_prop_hei_cash_trolly_03"),
            reward = {
                money = {min = 8000, max = 20000},
                items = {
                    {name = "water", min = 1, max = 3, chance = 30},
                    {name = "burger", min = 1, max = 2, chance = 20}
                }
            }
        },
    }
}