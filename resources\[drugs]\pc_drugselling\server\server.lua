local QBCore = exports['qb-core']:GetCoreObject()

-- Table pour stocker l'état des transactions
local activeTransactions = {}
local usedLocations = {}

-- Code serveur ici
RegisterServerEvent('asc_drugselling:usePhone')
AddEventHandler('asc_drugselling:usePhone', function()
    local src = source
    -- Envoyer un message au client pour ouvrir le téléphone
    TriggerClientEvent('asc_drugselling:client:openPhone', src)
end)

-- Événement pour fermer le téléphone
RegisterServerEvent('asc_drugselling:closePhone')
AddEventHandler('asc_drugselling:closePhone', function()
    local src = source
    TriggerClientEvent('asc_drugselling:client:closePhone', src)
end)

lib.callback.register('asc_drugselling:server:getCops', function(source)
    local cops = 0
    local players = exports.qbx_core:GetQBPlayers()
    
    for _, player in pairs(players) do
        if player.PlayerData.job.type == 'leo' and player.PlayerData.job.onduty then
            cops = cops + 1
        end
    end
    return cops
end)

RegisterNetEvent('asc_drugselling:server:completeDrugDelivery')
AddEventHandler('asc_drugselling:server:completeDrugDelivery', function(drugData)
    local src = source
    local transaction = activeTransactions[src]
    if not transaction or transaction.completed then
        TriggerClientEvent('asc_drugselling:client:transactionComplete', src, false)
        return
    end
    -- Vérifier si le joueur a la drogue
    local hasItem = exports.ox_inventory:Search(src, 'count', drugData.name) >= drugData.amount
    if hasItem then
        if exports.ox_inventory:RemoveItem(src, drugData.name, drugData.amount) then
            exports.ox_inventory:AddItem(src, 'black_money', drugData.price)
            transaction.completed = true
            TriggerClientEvent('asc_drugselling:client:transactionComplete', src, true)
        else
            TriggerClientEvent('asc_drugselling:client:transactionComplete', src, false)
        end
    else
        TriggerClientEvent('asc_drugselling:client:transactionComplete', src, false)
    end
end)

-- Ajouter cette fonction pour compter les policiers
lib.callback.register('asc_drugselling:server:getCops', function(source)
    local cops = 0
    local players = exports.qbx_core:GetQBPlayers()
    
    for _, player in pairs(players) do
        if player.PlayerData.job.type == 'leo' and player.PlayerData.job.onduty then
            cops = cops + 1
        end
    end
    return cops
end)

RegisterNetEvent('asc_drugselling:server:theftDrug')
AddEventHandler('asc_drugselling:server:theftDrug', function(drugName, amount)
    local source = source
    -- Retirer l'item de l'inventaire
    exports.ox_inventory:RemoveItem(source, drugName, amount) -- Retirer l'item
end)

RegisterNetEvent('asc_drugselling:server:recoverStolenDrug')
AddEventHandler('asc_drugselling:server:recoverStolenDrug', function(drugName, amount)
    local src = source
    -- Ajoutez la logique pour récupérer la drogue ici
    if drugName and amount then
        exports.ox_inventory:AddItem(src, drugName, amount)
        TriggerClientEvent('asc_drugselling:client:notify', src, {
            title = 'Récupération',
            description = 'Vous avez récupéré la drogue volée.',
            type = 'success'
        })
    else
    end
end)

-- Fonction pour créer le ped de livraison
RegisterNetEvent('asc_drugselling:server:createDeliveryPed')
AddEventHandler('asc_drugselling:server:createDeliveryPed', function(coords, initiatorId)
    local src = source
    -- Vérifier que les coordonnées sont valides
    if not coords or not coords.x or not coords.y or not coords.z then
        return
    end
    local model = joaat(Config.DeliveryPed.model)
    local ped = CreatePed(4, model, coords.x, coords.y, coords.z, coords.w, true, true)
    if not DoesEntityExist(ped) then return end
    local netId = NetworkGetNetworkIdFromEntity(ped)
    if not netId or netId == 0 then
        DeleteEntity(ped)
        return
    end
    -- Associer la transaction à ce joueur
    activeTransactions[src] = {
        netId = netId,
        completed = false,
        coords = coords
    }
    -- Synchroniser le ped avec tous les clients (pour qu'il soit visible par tous)
    TriggerClientEvent('asc_drugselling:client:syncDeliveryPed', -1, netId, coords, src)
end)

-- Fonction pour vérifier si la transaction est déjà complétée
lib.callback.register('asc_drugselling:server:checkTransactionStatus', function(source, netId)
    if activeTransactions[source] then
        return activeTransactions[source].completed
    end
    return false
end)

-- Fonction pour marquer la transaction comme complétée
RegisterNetEvent('asc_drugselling:server:markTransactionComplete')
AddEventHandler('asc_drugselling:server:markTransactionComplete', function(netId)
    local src = source
    if activeTransactions[src] then
        activeTransactions[src].completed = true
        -- Informer uniquement le client concerné que la transaction est complétée
        TriggerClientEvent('asc_drugselling:client:transactionComplete', src, true)
    end
end)

-- Fonction pour supprimer le ped de livraison
RegisterNetEvent('asc_drugselling:server:deleteDeliveryPed')
AddEventHandler('asc_drugselling:server:deleteDeliveryPed', function(netId)
    local src = source
    -- Supprimer le ped
    local ped = NetworkGetEntityFromNetworkId(netId)
    if ped and DoesEntityExist(ped) then
        DeleteEntity(ped)
    end
    -- Nettoyer la transaction du joueur
    activeTransactions[src] = nil
end)

lib.callback.register('asc_drugselling:server:getAvailableDeliveryLocation', function(source)
    for i, loc in ipairs(Config.DeliveryLocations) do
        if not usedLocations[i] then
            usedLocations[i] = source
            return loc, i
        end
    end
    return nil, nil -- Aucun lieu dispo
end)

RegisterNetEvent('asc_drugselling:server:releaseDeliveryLocation')
AddEventHandler('asc_drugselling:server:releaseDeliveryLocation', function(index)
    if index then
        usedLocations[index] = nil
    end
end)

AddEventHandler('playerDropped', function(reason)
    local src = source
    activeTransactions[src] = nil
    for i, v in pairs(usedLocations) do
        if v == src then
            usedLocations[i] = nil
        end
    end
end) 