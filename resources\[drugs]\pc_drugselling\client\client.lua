local isPhoneOpen = false
local isDeliveryInProgress = false
local currentDeliveryLocation = nil
local currentPed = nil
local currentDeliveryBlip = nil
local currentDrugData = nil
local stolenDrugs = {} -- Tableau pour stocker les données de drogue volée par transaction
local hasCompletedTransaction = false -- Variable pour suivre si la transaction a été complétée
local transactionTargetId = nil -- Variable pour stocker l'ID de l'option ox_target
local transactionInitiator = nil -- Ajouter une variable pour stocker l'ID du joueur qui a initié la transaction

-- Variables d'état pour le debug
local isPhonePoweredOn = false
local selectedDrugs = {} -- Initialisé à une table vide
local hasEnoughCops = false
local deliveryLocationIndex = nil

-- Gestion optimisée de la génération de messages d'acheteur sans thread constant
local buyerMessageTimer = nil
local buyerMessageBuffer = {}

-- Fonction utilitaire pour envoyer un message d'acheteur (buffer si UI fermée)
local function sendBuyerMessageToNUI(messageData)
    if isPhoneOpen then
        SendNUIMessage(messageData)
    else
        table.insert(buyerMessageBuffer, messageData)
    end
end

local function canGenerateBuyerMessage()
    local can = isPhonePoweredOn
        and selectedDrugs ~= nil and #selectedDrugs > 0
        and hasEnoughCops
        and not isDeliveryInProgress
    return can
end

local function cancelBuyerMessageTimer()
    if buyerMessageTimer then
        ClearTimeout(buyerMessageTimer)
        buyerMessageTimer = nil
    end
end

-- Fonction utilitaire pour vérifier côté NUI si un contact a une demande en cours
function hasUnprocessedRequestNUI(contact)
    local result = false
    local finished = false
    SendNUIMessage({
        action = "hasUnprocessedRequest",
        contact = contact
    })
    RegisterNUICallback('hasUnprocessedRequestResult', function(data, cb)
        result = data.hasUnprocessed
        finished = true
        if cb then cb() end
    end)
    local timeout = GetGameTimer() + 500
    while not finished and GetGameTimer() < timeout do
        Wait(5)
    end
    return result
end

function tryStartBuyerMessageTimer()
    cancelBuyerMessageTimer()
    if canGenerateBuyerMessage() then
        local interval = math.random(Config.MessageInterval.min, Config.MessageInterval.max)
        buyerMessageTimer = SetTimeout(interval, function()
            if canGenerateBuyerMessage() then
                local contacts = {
                    'Marcus Rodriguez',
                    'Sarah Chen',
                    'Antoine Dubois',
                    'Liam O\'Connor',
                    'Isabella Santos',
                    'Viktor Petrov',
                    'Jasmine Williams',
                    'Mohammed Al-Sayed',
                    'Elena Kovač'
                }
                local availableContacts = {}
                for _, contact in ipairs(contacts) do
                    if not hasUnprocessedRequestNUI(contact) then
                        table.insert(availableContacts, contact)
                    end
                end
                if #availableContacts == 0 then
                    return
                end
                local contact = availableContacts[math.random(#availableContacts)]
                -- Choisir une drogue aléatoire parmi celles sélectionnées
                local drugName = selectedDrugs[math.random(#selectedDrugs)]
                local drug
                for _, d in ipairs(Config.Drugs) do
                    if d.name == drugName then
                        drug = d
                        break
                    end
                end
                if drug then
                    local amount = math.random(drug.minAmount, drug.maxAmount)
                    local totalPrice = amount * drug.pricePerUnit
                    local baseMessage = drug.messages[math.random(#drug.messages)]
                    local fullMessage = string.format("%s Il me faut %d %s pour %d$", baseMessage, amount, drug.label, totalPrice)
                    -- Envoyer le message à la NUI (ou buffer)
                    sendBuyerMessageToNUI({
                        action = "receiveMessage",
                        contact = contact,
                        message = fullMessage,
                        drugData = {
                            name = drug.name,
                            label = drug.label,
                            amount = amount,
                            price = totalPrice,
                            pricePerUnit = drug.pricePerUnit,
                            isProcessed = false
                        }
                    })
                end
                -- Relancer le timer pour le prochain message
                tryStartBuyerMessageTimer()
            else
                buyerMessageTimer = nil
            end
        end)
    else
    end
end

-- Fonction pour charger l'animation
local function loadAnimDict(dict)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(5)
    end
end

local function QuestionPed()
    if currentPed then
        -- Choisir un message aléatoire parmi les réponses du dealer
        local randomIndex = math.random(1, #Config.DealerResponses)
        local message = Config.DealerResponses[randomIndex]

        -- Envoyer une notification à la police
        lib.notify({
            title = 'Réponse de l\'individu',
            description = message,
            type = 'inform'
        })

    else
    end
end 

-- Event pour ouvrir le téléphone
RegisterNetEvent('asc_drugselling:client:openPhone')
AddEventHandler('asc_drugselling:client:openPhone', function()
    if not isPhoneOpen then
        local cops = lib.callback.await('asc_drugselling:server:getCops', false) or 0
        local minCops = Config.PoliceAlert and Config.PoliceAlert.minCops or 0
        hasEnoughCops = (cops >= minCops)
        
        -- Activer le focus NUI avec le curseur
        SetNuiFocus(true, true)
        SendNUIMessage({
            action = "openPhone",
            config = {
                drugs = Config.Drugs,
                messageInterval = Config.MessageInterval,
                hasEnoughCops = hasEnoughCops
            }
        })
        
        isPhoneOpen = true
        
        -- Après ouverture, envoyer les messages bufferisés
        if #buyerMessageBuffer > 0 then
            for _, msg in ipairs(buyerMessageBuffer) do
                SendNUIMessage(msg)
            end
            buyerMessageBuffer = {}
            -- Demander la mise à jour des indicateurs de nouveaux messages
            SendNUIMessage({ action = "updateMessageIndicators" })
        end
        
        -- Thread pour gérer les contrôles pendant que le téléphone est ouvert
        Citizen.CreateThread(function()
            while isPhoneOpen do
                -- Désactiver les contrôles de combat
                DisableControlAction(0, 24, true) -- Désactiver le tir
                DisableControlAction(0, 257, true) -- Désactiver le tir (secondaire)
                DisableControlAction(0, 25, true) -- Désactiver le clic droit
                DisableControlAction(0, 47, true) -- Désactiver l'arme
                DisableControlAction(0, 58, true) -- Désactiver l'arme
                DisableControlAction(0, 263, true) -- Désactiver l'attaque
                DisableControlAction(0, 264, true) -- Désactiver l'attaque
                DisableControlAction(0, 257, true) -- Désactiver l'attaque
                DisableControlAction(0, 140, true) -- Désactiver l'attaque
                DisableControlAction(0, 141, true) -- Désactiver l'attaque
                DisableControlAction(0, 142, true) -- Désactiver l'attaque
                DisableControlAction(0, 143, true) -- Désactiver l'attaque
                
                -- Permettre le mouvement
                EnableControlAction(0, 30, true) -- Permettre le mouvement horizontal
                EnableControlAction(0, 31, true) -- Permettre le mouvement vertical
                EnableControlAction(0, 36, true) -- Permettre le mouvement latéral
                
                Wait(0)
            end
        end)
        
        if cops < minCops then
            lib.notify({
                title = 'Information',
                description = 'Pas assez de policiers en service pour le moment',
                type = 'error'
            })
        end
        
        -- Animation du téléphone
        local ped = PlayerPedId()
        local dict = "cellphone@"
        local anim = "cellphone_text_read_base"
        local prop_name = 'prop_npc_phone_02'
        local prop = nil
        
        -- Charger l'animation
        loadAnimDict(dict)
        
        -- Créer et attacher le prop du téléphone
        local x,y,z = table.unpack(GetEntityCoords(ped))
        prop = CreateObject(GetHashKey(prop_name), x, y, z + 0.2, true, true, true)
        local boneIndex = GetPedBoneIndex(ped, 28422)
        
        AttachEntityToEntity(prop, ped, boneIndex, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, true, true, false, true, 1, true)
        
        -- Animation modifiée pour permettre le mouvement
        TaskPlayAnim(ped, dict, anim, 8.0, -8.0, -1, 49, 0, false, false, false)
        
        -- Permettre au joueur de se déplacer
        SetPedCanPlayAmbientAnims(ped, true)
        SetPedCanPlayGestureAnims(ped, true)
        SetPedCanPlayVisemeAnims(ped, true)
        SetPedCanPlayInjuredAnims(ped, true)
        
        -- Stocker le prop pour le supprimer plus tard
        SetTimeout(500, function()
            prop = prop
        end)
    end
end)

-- Callback pour fermer le téléphone
RegisterNUICallback('closePhone', function(data, cb)
    if isPhoneOpen then
        isPhoneOpen = false
        -- Arrêter l'animation
        local ped = PlayerPedId()
        StopAnimTask(ped, "cellphone@", "cellphone_text_read_base", 1.0)
        -- Supprimer le prop
        local prop = GetClosestObjectOfType(GetEntityCoords(ped), 1.0, GetHashKey('prop_npc_phone_02'), false, false, false)
        if prop ~= 0 then
            DeleteEntity(prop)
        end
        SetNuiFocus(false, false) -- Désactiver complètement le focus NUI
        -- Ne plus toucher à isPhonePoweredOn ni selectedDrugs ici !
    end
    cb('ok')
end)

-- Nettoyer l'animation si le joueur meurt ou autre
AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        if isPhoneOpen then
            local ped = PlayerPedId()
            StopAnimTask(ped, "cellphone@", "cellphone_text_read_base", 1.0)
            local prop = GetClosestObjectOfType(GetEntityCoords(ped), 1.0, GetHashKey('prop_npc_phone_02'), false, false, false)
            if prop ~= 0 then
                DeleteEntity(prop)
            end
        end
    end
end)

RegisterNUICallback('receivedMessage', function(data, cb)
    local contact = data.contact
    local message = data.message
    local drug = data.drug
    
    -- Notification du nouveau message avec ox_lib
    lib.notify({
        title = 'Nouveau message',
        description = string.format('Message de %s', contact),
        type = 'inform'
    })
    
    cb('ok')
end)

local function AlertPolice()
    
    if currentPed and DoesEntityExist(currentPed) then
        local pedCoords = GetEntityCoords(currentPed)
        exports["ps-dispatch"]:DrugSale2(pedCoords)
    else
    end
end

RegisterNUICallback('drugResponse', function(data, cb)
    local contact = data.contact
    local response = data.response
    
    if response == "Oui" then
        if data.drugData then
            currentDrugData = data.drugData
            
            if isDeliveryInProgress then
                lib.notify({
                    title = 'Erreur',
                    description = 'Une livraison est déjà en cours.',
                    type = 'error'
                })
                cb('ok')
                return
            end
            
            isDeliveryInProgress = true
            
            -- Vérifier si la police doit être alertée
            if math.random(100) <= (Config.PoliceAlert.chance or 35) then
                SetTimeout(math.random(10000, 30000), function()
                    AlertPolice()
                end)
            end
            
            lib.notify({
                title = 'Livraison acceptée',
                description = string.format('Livraison de %d %s pour %d$', 
                    currentDrugData.amount, 
                    currentDrugData.name, 
                    currentDrugData.price
                ),
                type = 'success'
            })
            
            -- Timer pour afficher le temps écoulé
            local startTime = GetGameTimer()
            
            local timeInterval = nil
            local function checkTimeout()
                if not isDeliveryInProgress then
                    if timeInterval then
                        ClearInterval(timeInterval)
                        timeInterval = nil
                    end
                    return
                end

                local currentTime = GetGameTimer()
                local elapsedTime = (currentTime - startTime) / 1000
                
                if elapsedTime >= Config.DeliveryTimeout then
                    if timeInterval then
                        ClearInterval(timeInterval)
                        timeInterval = nil
                    end
                    
                    -- Annuler la livraison
                    if currentPed and DoesEntityExist(currentPed) then
                        local netId = NetworkGetNetworkIdFromEntity(currentPed)
                        if netId then
                            TriggerServerEvent('asc_drugselling:server:deleteDeliveryPed', netId)
                        end
                    end
                    
                    -- Nettoyer
                    if currentDeliveryBlip then
                        RemoveBlip(currentDeliveryBlip)
                        currentDeliveryBlip = nil
                    end
                    
                    -- Réinitialiser les variables
                    isDeliveryInProgress = false
                    currentDrugData = nil
                    transactionInitiator = nil
                    currentPed = nil
                    currentDeliveryLocation = nil
                    
                    -- Notification
                    lib.notify({
                        title = 'Livraison annulée',
                        description = 'Le temps est écoulé, la livraison a été annulée',
                        type = 'error'
                    })
                    
                    -- Redémarrer les messages
                    SendNUIMessage({
                        action = "deliveryComplete"
                    })
                    
                    -- Forcer le nettoyage des options ox_target
                    if currentPed and DoesEntityExist(currentPed) then
                        exports.ox_target:removeLocalEntity(currentPed)
                    end
                    
                    -- Redémarrer les messages automatiques
                    Wait(2000)
                    startAutomaticMessages()
                end
            end

            -- Démarrer l'intervalle
            timeInterval = SetInterval(checkTimeout, 1000)

            StartDeliveryMission(currentDrugData)
        else
            lib.notify({
                title = 'Erreur',
                description = 'Impossible de récupérer les détails de la commande',
                type = 'error'
            })
        end
    else
        lib.notify({
            title = 'Livraison refusée',
            description = string.format('Vous avez refusé la demande de %s', contact),
            type = 'error'
        })
    end
    
    cb('ok')
end)

-- Remplacer la fonction getRandomDeliveryLocation par une demande serveur
local function requestAvailableDeliveryLocation()
    local location, index = lib.callback.await('asc_drugselling:server:getAvailableDeliveryLocation', false)
    if location and index then
        deliveryLocationIndex = index
        return location
    else
        lib.notify({
            title = 'Erreur',
            description = 'Aucun lieu de livraison disponible pour le moment.',
            type = 'error'
        })
        return nil
    end
end

-- Fonction pour créer le ped de livraison
local function CreateDeliveryPed(coords)
    -- Demander au serveur de créer le ped
    TriggerServerEvent('asc_drugselling:server:createDeliveryPed', coords)
end

-- Événement pour synchroniser le ped de livraison
RegisterNetEvent('asc_drugselling:client:syncDeliveryPed')
AddEventHandler('asc_drugselling:client:syncDeliveryPed', function(netId, coords, initiatorId)
    
    -- Attendre que l'entité soit disponible
    local timeout = 0
    while not NetworkDoesEntityExistWithNetworkId(netId) and timeout < 50 do
        Wait(100)
        timeout = timeout + 1
    end
    
    if NetworkDoesEntityExistWithNetworkId(netId) then
        currentPed = NetworkGetEntityFromNetworkId(netId)
        
        -- Stocker l'ID de l'initiateur
        transactionInitiator = initiatorId
        
        -- Configurer le ped
        FreezeEntityPosition(currentPed, true)
        SetEntityInvincible(currentPed, true)
        SetBlockingOfNonTemporaryEvents(currentPed, true)
        
        -- Jouer le scénario
        if Config.DeliveryPed.scenario then
            TaskStartScenarioInPlace(currentPed, Config.DeliveryPed.scenario, 0, true)
        end
        
        -- Vérifier si le joueur actuel est l'initiateur
        local myId = GetPlayerServerId(PlayerId())
        
        -- Options pour le ped
        local options = {}
        
        -- Option de transaction pour l'initiateur
        if myId == transactionInitiator then
            table.insert(options, {
                name = 'drug_delivery',
                icon = 'fas fa-handshake',
                label = 'Faire la transaction',
                onSelect = function()
                    CompleteDrugDelivery()
                end
            })
        end
        
        -- Option de discussion pour la police
        table.insert(options, {
            name = 'question',
            icon = 'fas fa-question',
            label = 'Discuter avec l\'individu',
            groups = 'police',
            onSelect = function()
                QuestionPed()
            end
        })
        
        -- Ajouter toutes les options au ped
        exports.ox_target:addLocalEntity(currentPed, options)
    else
    end
end)

-- Modifier la fonction CleanupDelivery
local function CleanupDelivery()
    if deliveryLocationIndex then
        TriggerServerEvent('asc_drugselling:server:releaseDeliveryLocation', deliveryLocationIndex)
        deliveryLocationIndex = nil
    end
    if currentPed then
        
        -- Vérifier si le ped est en train de se déplacer
        local pedCoords = GetEntityCoords(currentPed)
        local exitCoords = nil
        for _, location in pairs(Config.DeliveryLocations) do
            if location.coordsexit then
                exitCoords = location.coordsexit
                break
            end
        end
        
        if exitCoords then
            local distance = #(vector3(pedCoords.x, pedCoords.y, pedCoords.z) - vector3(exitCoords.x, exitCoords.y, exitCoords.z))
            if distance > 2.0 then
                return
            end
        end
        
        -- Arrêter toutes les animations précédentes
        FreezeEntityPosition(currentPed, false)
        SetEntityInvincible(currentPed, false)
        ClearPedTasks(currentPed)
        Wait(1000)

        -- Demander au serveur de supprimer le ped
        local netId = NetworkGetNetworkIdFromEntity(currentPed)
        TriggerServerEvent('asc_drugselling:server:deleteDeliveryPed', netId)
        currentPed = nil
    else
    end
    
    if currentDeliveryBlip then
        RemoveBlip(currentDeliveryBlip)
        currentDeliveryBlip = nil
    end
end

-- Modifier la fonction MovePedToExit
local function MovePedToExit(ped, shouldRun)
    if not ped or not DoesEntityExist(ped) then 
        return 
    end
    
    -- Débloquer le ped
    FreezeEntityPosition(ped, false)
    SetEntityInvincible(ped, false)
    ClearPedTasks(ped)
    
    -- Obtenir les coordonnées de sortie
    local exitCoords = nil
    for _, location in pairs(Config.DeliveryLocations) do
        if location.coordsexit then
            exitCoords = location.coordsexit
            break
        end
    end
    
    if not exitCoords then
        return
    end
    
    -- Définir la vitesse de déplacement et l'animation
    if shouldRun then
        -- Configurer le ped pour qu'il coure
        SetPedMoveRateOverride(ped, 1.49)
        SetPedDesiredMoveBlendRatio(ped, 3.0)
        
        -- Forcer l'animation de course
        RequestAnimSet("move_m@quick")
        while not HasAnimSetLoaded("move_m@quick") do
            Wait(0)
        end
        SetPedMovementClipset(ped, "move_m@quick", 1.0)
    else
        -- Configurer le ped pour qu'il marche normalement
        SetPedMoveRateOverride(ped, 1.0)
        SetPedDesiredMoveBlendRatio(ped, 1.0)
    end
    
    -- Variable pour suivre l'état du déplacement
    local isMoving = true
    local hasReachedDestination = false
    local lastCoords = GetEntityCoords(ped)
    local stuckCount = 0
    local maxStuckAttempts = 5
    
    -- Fonction pour déplacer le ped
    local function movePedToDestination()
        if shouldRun then
            TaskGoStraightToCoord(ped, exitCoords.x, exitCoords.y, exitCoords.z, 3.0, -1, 0.0, 0.0)
        else
            TaskGoStraightToCoord(ped, exitCoords.x, exitCoords.y, exitCoords.z, 1.0, -1, 0.0, 0.0)
        end
    end
    
    -- Démarrer le déplacement
    movePedToDestination()
    
    -- Thread principal pour gérer le déplacement
    Citizen.CreateThread(function()
        local timeout = 0
        
        while timeout < 200 and not hasReachedDestination and isMoving do
            if not DoesEntityExist(ped) then 
                break 
            end
            
            local pedCoords = GetEntityCoords(ped)
            local distance = #(vector3(pedCoords.x, pedCoords.y, pedCoords.z) - vector3(exitCoords.x, exitCoords.y, exitCoords.z))
            
            -- Vérifier si le ped est bloqué
            if timeout > 0 and timeout % 10 == 0 then
                local currentCoords = GetEntityCoords(ped)
                local movementDistance = #(vector3(currentCoords.x, currentCoords.y, currentCoords.z) - vector3(lastCoords.x, lastCoords.y, lastCoords.z))
                
                if movementDistance < 0.1 then
                    stuckCount = stuckCount + 1
                    
                    if stuckCount >= maxStuckAttempts then
                        SetEntityCoords(ped, exitCoords.x, exitCoords.y, exitCoords.z, false, false, false, false)
                        hasReachedDestination = true
                        break
                    end
                    
                    -- Réessayer de déplacer le ped
                    movePedToDestination()
                else
                    stuckCount = 0
                end
                
                lastCoords = currentCoords
            end
            
            if distance < 2.0 then
                hasReachedDestination = true
                isMoving = false
                break
            end
            
            timeout = timeout + 1
            Citizen.Wait(100)
        end
        
        -- Si le timeout est atteint, téléporter le ped
        if timeout >= 200 and not hasReachedDestination and DoesEntityExist(ped) then
            SetEntityCoords(ped, exitCoords.x, exitCoords.y, exitCoords.z, false, false, false, false)
            hasReachedDestination = true
        end
        
        -- Une fois que le ped a atteint sa destination
        if hasReachedDestination and DoesEntityExist(ped) then
            -- Geler le ped en place
            -- Attendre une minute complète
            for i = 1, 60 do
                if not DoesEntityExist(ped) then
                    return
                end
                Citizen.Wait(1000) -- Attendre 1 seconde
            end
            
            if DoesEntityExist(ped) then
                local netId = NetworkGetNetworkIdFromEntity(ped)
                if netId then
                    TriggerServerEvent('asc_drugselling:server:deleteDeliveryPed', netId)
                    
                    -- Attendre un peu pour s'assurer que la suppression est bien effectuée
                    Citizen.Wait(1000)
                    
                    if DoesEntityExist(ped) then
                        DeleteEntity(ped)
                    end
                end
            end
            
            -- Appeler CompleteDelivery seulement après la suppression du ped
            CompleteDelivery()
        end
    end)
end

-- Modifier la fonction CompleteDrugDelivery
function CompleteDrugDelivery()
    if not currentPed then return end
    
    local netId = NetworkGetNetworkIdFromEntity(currentPed)
    if not netId then return end

    -- Vérifier l'état de la transaction côté serveur
    local isCompleted = lib.callback.await('asc_drugselling:server:checkTransactionStatus', false, netId)
    if isCompleted then
        lib.notify({
            title = 'Erreur',
            description = 'La transaction a déjà été complétée.',
            type = 'error'
        })
        return
    end

    -- Vérifiez si currentDrugData est une chaîne JSON ou une table
    if not currentDrugData then
        lib.notify({
            title = 'Erreur',
            description = 'La transaction a déjà été effectué',
            type = 'error'
        })
        return
    end

    local drugData
    if type(currentDrugData) == "string" then
        drugData = json.decode(currentDrugData)
    else
        drugData = currentDrugData
    end

    -- Utiliser la fonction Search pour obtenir les slots contenant la drogue demandée
    local drugSlots = exports.ox_inventory:Search('slots', drugData.name)
    local count = 0

    if type(drugSlots) == "table" then
        for _, v in pairs(drugSlots) do
            count = count + v.count
        end
    else
        return
    end

    local hasItem = (count >= drugData.amount)

    if not hasItem then
        lib.notify({
            title = 'Erreur',
            description = 'Vous n\'avez pas la drogue requise pour cette transaction.',
            type = 'error'
        })
        return
    end

    -- Vérifier si le ped vole la drogue
    local theftChance = Config.DrugTheftChance
    
    -- Forcer le vol si la chance est à 100%
    local willSteal = theftChance >= 100 or math.random(100) <= theftChance
    
    if willSteal then
        -- Déterminer l'action du ped
        local action = math.random(100)

        if action <= Config.PedActions.knifeChance then
            
            -- Débloquer le ped
            FreezeEntityPosition(currentPed, false)
            SetEntityInvincible(currentPed, false)
            ClearPedTasks(currentPed)
            
            -- Donner un couteau au ped
            GiveWeaponToPed(currentPed, GetHashKey("WEAPON_KNIFE"), 0, false, true)
            
            -- Charger l'animation
            RequestAnimDict("anim@mp_player_intmenu@key_fob@")
            while not HasAnimDictLoaded("anim@mp_player_intmenu@key_fob@") do
                Wait(0)
            end
            
            -- Jouer l'animation du couteau
            TaskPlayAnim(currentPed, "anim@mp_player_intmenu@key_fob@", "fob_click", 8.0, -8.0, -1, 48, 0, false, false, false)
            Wait(1000)
            
            -- Attaquer le joueur
            local playerPed = PlayerPedId()
            TaskCombatPed(currentPed, playerPed, 0, 16)
            
            -- Configurer le ped pour qu'il soit plus agressif
            SetPedCombatAttributes(currentPed, 46, true)
            SetPedCombatRange(currentPed, 2)
            SetPedCombatMovement(currentPed, 3)
            SetPedCombatAbility(currentPed, 100)
            SetPedFleeAttributes(currentPed, 0, false)
            SetPedAsEnemy(currentPed, true)
            
            lib.notify({
                title = 'Vol de Drogue',
                description = 'Le ped a volé la drogue et vous attaque !',
                type = 'error'
            })
            
            local transactionId = tostring(GetGameTimer()) .. math.random(1000, 9999)
            stolenDrugs[transactionId] = { 
                name = drugData.name, 
                amount = drugData.amount,
                recovered = false
            }
            
            TriggerServerEvent('asc_drugselling:server:theftDrug', drugData.name, drugData.amount)
            TriggerServerEvent('asc_drugselling:server:markTransactionComplete', netId)
            
            -- Supprimer l'option de transaction car la transaction est réussie (vol)
            exports.ox_target:removeLocalEntity(currentPed, 'drug_delivery')
            
            -- Ajouter l'option pour récupérer la drogue
            exports.ox_target:addLocalEntity(currentPed, {
                {
                    name = 'recover_drug',
                    icon = 'fas fa-recycle',
                    label = 'Récupérer la drogue volée',
                    distance = 2.0,
                    onSelect = function() 
                        if stolenDrugs[transactionId] and not stolenDrugs[transactionId].recovered then
                            if lib.progressBar({
                                duration = 5000,
                                label = 'Récupération de la drogue en cours...',
                                useWhileDead = false,
                                canCancel = false,
                                disable = {
                                    car = true,
                                    move = true,
                                },
                            }) then
                                TriggerServerEvent('asc_drugselling:server:recoverStolenDrug', stolenDrugs[transactionId].name, stolenDrugs[transactionId].amount) 
                                stolenDrugs[transactionId].recovered = true
                            end
                        else
                            lib.notify({
                                title = 'Erreur',
                                description = 'Vous avez déjà récupéré la drogue.',
                                type = 'error'
                            })
                        end
                    end
                }
            })
            
            -- Ne pas faire courir le ped, il va attaquer le joueur
            CompleteDelivery()
        elseif action <= Config.PedActions.runAwayChance then
            lib.notify({
                title = 'Vol de Drogue',
                description = 'Le ped a volé la drogue !',
                type = 'error'
            })
            
            local transactionId = tostring(GetGameTimer()) .. math.random(1000, 9999)
            stolenDrugs[transactionId] = { 
                name = drugData.name, 
                amount = drugData.amount,
                recovered = false
            }
            
            TriggerServerEvent('asc_drugselling:server:theftDrug', drugData.name, drugData.amount)
            TriggerServerEvent('asc_drugselling:server:markTransactionComplete', netId)
            
            -- Supprimer l'option de transaction car la transaction est réussie (vol)
            exports.ox_target:removeLocalEntity(currentPed, 'drug_delivery')
            
            exports.ox_target:addLocalEntity(currentPed, {
                {
                    name = 'recover_drug',
                    icon = 'fas fa-recycle',
                    label = 'Récupérer la drogue volée',
                    distance = 2.0,
                    onSelect = function() 
                        if stolenDrugs[transactionId] and not stolenDrugs[transactionId].recovered then
                            if lib.progressBar({
                                duration = 5000,
                                label = 'Récupération de la drogue en cours...',
                                useWhileDead = false,
                                canCancel = false,
                                disable = {
                                    car = true,
                                    move = true,
                                },
                            }) then
                                TriggerServerEvent('asc_drugselling:server:recoverStolenDrug', stolenDrugs[transactionId].name, stolenDrugs[transactionId].amount) 
                                stolenDrugs[transactionId].recovered = true
                            end
                        else
                            lib.notify({
                                title = 'Erreur',
                                description = 'Vous avez déjà récupéré la drogue.',
                                type = 'error'
                            })
                        end
                    end
                }
            })
            
            -- Faire courir le ped vers la sortie
            MovePedToExit(currentPed, true)
            CompleteDelivery()
        else
            if math.random(100) <= (Config.PoliceAlert.transactionChance or 25) then
                AlertPolice()
            end

            if lib.progressBar({
                duration = 5000,
                label = 'Transaction en cours...',
                useWhileDead = false,
                canCancel = true,
                disable = {
                    car = true,
                    move = true,
                },
                anim = {
                    dict = 'mp_common',
                    clip = 'givetake1_a'
                },
            }) then
                TriggerServerEvent('asc_drugselling:server:completeDrugDelivery', drugData)
                TriggerServerEvent('asc_drugselling:server:markTransactionComplete', netId)
                
                -- Supprimer l'option de transaction car la transaction est réussie
                exports.ox_target:removeLocalEntity(currentPed, 'drug_delivery')
                
                -- Faire marcher le ped vers la sortie
                MovePedToExit(currentPed, false)
                CompleteDelivery()
            end
        end
        return
    end

    if math.random(100) <= (Config.PoliceAlert.transactionChance or 25) then
        AlertPolice()
    end

    if lib.progressBar({
        duration = 5000,
        label = 'Transaction en cours...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
        },
        anim = {
            dict = 'mp_common',
            clip = 'givetake1_a'
        },
    }) then
        TriggerServerEvent('asc_drugselling:server:completeDrugDelivery', drugData)
        TriggerServerEvent('asc_drugselling:server:markTransactionComplete', netId)
        
        -- Supprimer l'option de transaction car la transaction est réussie
        exports.ox_target:removeLocalEntity(currentPed, 'drug_delivery')
        
        -- Faire marcher le ped vers la sortie
        MovePedToExit(currentPed, false)
        CompleteDelivery()
    end
end

-- Modifier la fonction CompleteDelivery
function CompleteDelivery()
    isDeliveryInProgress = false
    currentDrugData = nil -- Réinitialiser les données de la drogue
    transactionInitiator = nil -- Réinitialiser l'initiateur de la transaction
    
    -- Supprimer le blip
    if currentDeliveryBlip then
        RemoveBlip(currentDeliveryBlip)
        currentDeliveryBlip = nil
    end
    
    -- Informer le NUI que la livraison est terminée pour redémarrer les messages
    Wait(1000) -- Petit délai pour s'assurer que tout est bien nettoyé
    
    SendNUIMessage({
        action = "deliveryComplete"
    })
    
    -- Redémarrer les messages automatiques
    startAutomaticMessages()
end

-- Modifier l'event de transaction complétée
RegisterNetEvent('asc_drugselling:client:transactionComplete')
AddEventHandler('asc_drugselling:client:transactionComplete', function(success)
    if success then
    else
        lib.notify({
            title = 'Erreur',
            description = 'La transaction a échoué.',
            type = 'error'
        })
    end
    isDeliveryInProgress = false -- Réinitialiser l'état de la livraison
    currentDrugData = nil -- Réinitialiser les données de la drogue
    CleanupDelivery() -- Nettoyer le ped et le blip
end)

RegisterNUICallback('requestDeliveryLocation', function(data, cb)
    local contact = data.contact
    
    -- Sélectionner une localisation aléatoire
    currentDeliveryLocation = requestAvailableDeliveryLocation()
    
    -- Créer le ped
    currentPed = CreateDeliveryPed(currentDeliveryLocation.coords)
    
    -- Créer le blip
    currentDeliveryBlip = AddBlipForCoord(
        currentDeliveryLocation.coords.x,
        currentDeliveryLocation.coords.y,
        currentDeliveryLocation.coords.z
    )
    SetBlipSprite(currentDeliveryBlip, 1)
    SetBlipDisplay(currentDeliveryBlip, 4)
    SetBlipScale(currentDeliveryBlip, 0.8)
    SetBlipColour(currentDeliveryBlip, 5)
    SetBlipAsShortRange(currentDeliveryBlip, false)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Point de livraison")
    EndTextCommandSetBlipName(currentDeliveryBlip)
    
    -- Envoyer la localisation au NUI
    SendNUIMessage({
        action = "sendLocation",
        contact = contact,
        location = currentDeliveryLocation
    })
    
    cb('ok')
end)

-- Modifier la fonction StartDeliveryMission
function StartDeliveryMission(contact)
    
    if isDeliveryInProgress then
        return
    end
    
    -- Stocker l'ID du joueur qui initie la mission
    local myId = GetPlayerServerId(PlayerId())
    transactionInitiator = myId
    
    -- Sélectionner une localisation aléatoire
    currentDeliveryLocation = requestAvailableDeliveryLocation()
    if not currentDeliveryLocation then
        return
    end
    
    -- Créer le ped avec l'ID de l'initiateur
    TriggerServerEvent('asc_drugselling:server:createDeliveryPed', currentDeliveryLocation.coords, myId)
    
    -- Marquer la mission comme active
    isDeliveryInProgress = true

    -- Créer le blip
    if currentDeliveryBlip then
        RemoveBlip(currentDeliveryBlip)
    end
    
    currentDeliveryBlip = AddBlipForCoord(
        currentDeliveryLocation.coords.x,
        currentDeliveryLocation.coords.y,
        currentDeliveryLocation.coords.z
    )
    
    if currentDeliveryBlip then
        SetBlipSprite(currentDeliveryBlip, 1)
        SetBlipDisplay(currentDeliveryBlip, 4)
        SetBlipScale(currentDeliveryBlip, 0.8)
        SetBlipColour(currentDeliveryBlip, 5)
        SetBlipAsShortRange(currentDeliveryBlip, false)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString("Point de livraison")
        EndTextCommandSetBlipName(currentDeliveryBlip)
    else
    end
    
    -- Définir l'itinéraire GPS
    SetNewWaypoint(currentDeliveryLocation.coords.x, currentDeliveryLocation.coords.y)
    
    -- Notification
    lib.notify({
        title = 'Livraison',
        description = 'Rendez-vous au point marqué sur votre GPS',
        type = 'inform'
    })
end

-- Ajouter ce nouveau callback
RegisterNUICallback('showNotification', function(data, cb)
    lib.notify({
        title = data.title,
        description = data.message,
        type = data.type
    })
    cb('ok')
end)

-- Ajouter le callback pour définir le GPS
RegisterNUICallback('setGPS', function(data, cb)
    local coords = data.coords
    
    -- Supprimer l'ancien blip s'il existe
    if currentDeliveryBlip then
        RemoveBlip(currentDeliveryBlip)
    end
    
    -- Créer un nouveau blip
    currentDeliveryBlip = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(currentDeliveryBlip, 1)
    SetBlipDisplay(currentDeliveryBlip, 4)
    SetBlipScale(currentDeliveryBlip, 0.8)
    SetBlipColour(currentDeliveryBlip, 5)
    SetBlipAsShortRange(currentDeliveryBlip, false)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Point de livraison")
    EndTextCommandSetBlipName(currentDeliveryBlip)
    
    -- Définir l'itinéraire GPS
    SetNewWaypoint(coords.x, coords.y)
    
    -- Notification
    lib.notify({
        title = 'GPS',
        description = 'Point de livraison marqué sur votre GPS',
        type = 'inform'
    })
    
    cb('ok')
end)

-- Modifier le callback pour vérifier le nombre de policiers
RegisterNUICallback('checkPoliceCount', function(data, cb)
    local cops = lib.callback.await('asc_drugselling:server:getCops', 100) or 0
    local minCops = (Config.PoliceAlert and Config.PoliceAlert.minCops) or 0
    -- Retourner un objet avec la propriété enoughCops
    cb({
        enoughCops = (cops >= minCops)
    })
end)

RegisterNetEvent('asc_drugselling:client:notify')
AddEventHandler('asc_drugselling:client:notify', function(data)
    lib.notify({
        title = data.title,
        description = data.description,
        type = data.type
    })
end)

function startAutomaticMessages()
    -- Si on ne peut pas envoyer de messages, ne rien faire
    if not canSendMessages then
        return
    end

    -- Délai aléatoire avant d'envoyer le premier message
    Citizen.SetTimeout(math.random(10000, 30000), function()
        sendRandomMessage()

        messageInterval = SetInterval(function()
            if isDeliveryInProgress or not canSendMessages then
                stopAutomaticMessages()
                return
            end
            sendRandomMessage()
        end, math.random(Config.MessageInterval.min, Config.MessageInterval.max))
    end)
end

function TaskAttack(ped, targetPed, weaponHash, damage)
    -- Assurez-vous que le ped a une arme
    GiveWeaponToPed(ped, GetHashKey(weaponHash), 100, false, true)
    -- Attaque le joueur
    TaskCombatPed(ped, targetPed, 0, 16) -- 0 pour aucune option spéciale, 16 pour attaquer
end

-- Relancer la logique à chaque changement d'état pertinent
-- 1. Quand le téléphone est allumé/éteint ou la sélection de drogue change
RegisterNUICallback('powerButton', function(data, cb)
    isPhonePoweredOn = data.power == true
    selectedDrugs = data.selectedDrugs or {}
    tryStartBuyerMessageTimer()
    cb('success')
end)

-- 2. Quand la livraison commence ou se termine
local oldSetDeliveryInProgress = isDeliveryInProgress
CreateThread(function()
    while true do
        Wait(500)
        if isDeliveryInProgress ~= oldSetDeliveryInProgress then
            tryStartBuyerMessageTimer()
            oldSetDeliveryInProgress = isDeliveryInProgress
        end
    end
end)

-- 3. Quand le nombre de policiers change (thread déjà existant)
-- Remplacer la partie qui relançait la NUI par un appel à tryStartBuyerMessageTimer
-- ...

-- Thread pour rafraîchir hasEnoughCops toutes les 5 minutes
CreateThread(function()
    local lastHasEnoughCops = hasEnoughCops
    while true do
        Wait(Config.CopRefreshTime * 60 * 1000)
        if isPhonePoweredOn then
            local cops = lib.callback.await('asc_drugselling:server:getCops', 100) or 0
            local minCops = Config.PoliceAlert and Config.PoliceAlert.minCops or 0
            hasEnoughCops = (cops >= minCops)
            if not lastHasEnoughCops and hasEnoughCops then
                tryStartBuyerMessageTimer()
            end
            lastHasEnoughCops = hasEnoughCops
        end
    end
end)