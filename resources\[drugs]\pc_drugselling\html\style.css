.hidden {
    display: none !important;
}

#phone {
    position: fixed;
    left: auto;
    right: 40px;
    bottom: 40px;
    width: 360px;
    height: 640px;
    margin: 0;
    background-color: #000000;
    border-radius: 45px;
    padding: 15px;
    color: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    border: 15px solid #1a1a1a;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: transform 0.5s cubic-bezier(0.4, 0.2, 0.2, 1), opacity 0.5s cubic-bezier(0.4, 0.2, 0.2, 1);
    transform: translateY(100vh);
    opacity: 0;
    will-change: transform, opacity;
    box-sizing: border-box;
    pointer-events: auto;
    z-index: 9999;
}

#phone.show {
    transform: translateY(0);
    opacity: 1;
}

.phone-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: url('img/background.png') center/cover;
    border-radius: 30px;
    position: relative;
}

.phone-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    border-radius: 20px 20px 0 0;
}

.phone-header .time {
    font-weight: bold;
    font-size: 16px;
}

.phone-header .battery {
    display: flex;
    align-items: center;
    gap: 5px;
}

.phone-header .battery::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 10px;
    border: 2px solid white;
    border-radius: 3px;
    position: relative;
}

.phone-header .battery::after {
    content: '';
    width: 2px;
    height: 4px;
    background: white;
    position: absolute;
    right: -4px;
    border-radius: 0 2px 2px 0;
}

.phone-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
}

.phone-content > div {
    width: 100%;
    height: 100%;
}

.power-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.icon-bar {
    display: flex;
    justify-content: space-around;
    margin-top: 0px;
    padding-top: 10px;
}

.icon {
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: transform 0.2s;
}

.icon:hover {
    transform: scale(1.1);
}

.button-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.main-button {
    padding: 15px 30px;
    font-size: 18px;
    color: white;
    background-color: #1a1a1a;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.main-button:hover {
    background-color: #333333;
}

.app-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    padding: 10px;
}

.app {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    transition: transform 0.2s;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: 15px;
}

.app:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.2);
}

.app img {
    width: 40px;
    height: 40px;
    margin-bottom: 5px;
    border-radius: 10px;
}

.app span {
    font-size: 11px;
    text-align: center;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Notch du téléphone */
.notch {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 30px;
    background: #000;
    border-radius: 0 0 20px 20px;
    z-index: 1000;
}

.icon-button {
    width: 60px;
    height: 60px;
    cursor: pointer;
    transition: transform 0.2s;
}

.icon-button:hover {
    transform: scale(1.1);
}

#closePhoneButton {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

#closePhoneButton:hover {
    transform: translateX(-50%) scale(1.1);
}


.contacts-screen {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    max-height: 100%;
    margin-bottom: 60px;
}

.contact-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    transition: background-color 0.3s;
}

.contact-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.contact-name {
    font-weight: bold;
}

.contact-number {
    color: #888;
}

.black-screen {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.contacts-container {
    padding: 20px;
    height: calc(100% - 150px);
    scrollbar-width: thin;
    scrollbar-color: #888 #333;
}

/* Pour les navigateurs WebKit (Chrome, Safari) */
.contacts-container::-webkit-scrollbar {
    width: 8px;
}

.contacts-container::-webkit-scrollbar-track {
    background: #333;
    border-radius: 10px;
}

.contacts-container::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
}

.contacts-container::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

/* Styles pour l'écran de messages */
.message-contact {
    cursor: pointer;
    transition: background-color 0.3s;
    position: relative;
}

.message-contact:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.chat-screen {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.chat-header {
    position: sticky;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
    z-index: 10;
    display: flex;
    align-items: center;
}

#currentContact {
    flex: 1;
    margin-left: 60px;
    font-size: 18px;
    font-weight: bold;
    color: white;
}

.chat-messages {
    overflow-y: auto;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    position: static;
    height: 400px;
    margin-bottom: 0px;
    scrollbar-width: thin;
    scrollbar-color: #888 #333;
    display: flex;
    flex-direction: column;
}

/* Pour les navigateurs WebKit (Chrome, Safari) */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #333;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

.chat-input {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    width: 90%;
    padding: 10px;
}

#messageInput {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    background-color: #333;
    color: white;
}

.message {
    margin: 5px 0;
    padding: 10px 15px;
    border-radius: 10px;
    max-width: 70%;
    color: white;
}

.sent {
    background-color: #0084ff;
    margin-left: auto;
}

.received {
    background-color: #333;
    margin-right: auto;
}

.chat-response {
    display: flex;
    justify-content: space-around;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 0 0 10px 10px;
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    height: 40px;
    pointer-events: auto;
    transform: translateY(0); /* Forcer la position */
}

.response-button {
    flex: 1;
    margin: 0 5px;
    padding: 10px;
    border: none;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-bottom: 5px;
}

.yes-button {
    background-color: #28a745;
}

.yes-button:hover {
    background-color: #218838;
}

.no-button {
    background-color: #dc3545;
}

.no-button:hover {
    background-color: #c82333;
}

.chat-screen .black-screen {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Ajouter un style pour les messages système */
.message.system {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 10px;
    margin: 10px 0;
    max-width: 100%; /* S'assure que le message ne dépasse pas le cadre */
}

/* Ajouter ou modifier le style pour le bouton back dans la conversation */
#powerButton {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

#powerButton:hover {
    transform: translateX(-50%) scale(1.1);
}

#backToMessages {
    position: absolute;
    top: 0px;
    left: 10px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s;
}

#backToMessages:hover {
    transform: scale(1.1);
}

#backFromMessages {
    position: absolute;
    top: 60px;
    left: 10px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s;
}

#backFromMessages:hover {
    transform: scale(1.1);
}

#backButton {
    position: absolute;
    top: 0px;
    left: 10px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s;
}

#backButton:hover {
    transform: scale(1.1);
}

#backfromProduct {
    position: absolute;
    top: 60px;
    left: 10px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s;
}

#backfromProduct:hover {
    transform: scale(1.1);
}


.gps-button {
    display: block;
    margin-top: 5px;
    padding: 5px 10px;
    background-color: #444;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s;
}

.gps-button:hover {
    background-color: #555;
}

/* Ajuster le style des messages de localisation */
.message.received.isLocation {
    background-color: #333;
    padding: 10px;
}

/* Ajouter les styles pour l'indicateur de nouveau message */
.message-contact {
    position: relative;
}

.new-message-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: #ff3333;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: translateY(-50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-50%) scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: translateY(-50%) scale(1);
        opacity: 1;
    }
}

/* Ajouter un style pour le compteur de messages */
.unread-count {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff3333;
    font-size: 12px;
    font-weight: bold;
}

/* Styles pour la section produit */
.product-screen {
    display: none; /* Masqué par défaut */
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 10px;
    margin: 0px;
}

/* Styles pour la liste des drogues */
#drugList {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 100px;
    max-height: 400px;
    overflow-y: auto;
    width: 80%;
    padding-right: 15px;
    margin-right: 5px;
}

#drugList div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: background-color 0.3s;
    width: 100%;
    min-width: 200px;
    box-sizing: border-box;
}

#drugList div:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

#drugList div label {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 10px;
    font-weight: bold;
}

/* Styles pour les cases à cocher */
input[type="checkbox"] {
    display: none; /* Cacher la case à cocher par défaut */
}

/* Styles pour le label qui agit comme une case à cocher */
label {
    position: relative;
    padding-left: 35px; /* Espacement pour le faux checkbox */
    cursor: pointer; /* Changer le curseur pour indiquer que c'est cliquable */
    font-size: 16px; /* Taille de la police pour le label */
    color: white; /* Couleur du texte */
}

/* Créer un faux checkbox */
label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px; /* Largeur du faux checkbox */
    height: 20px; /* Hauteur du faux checkbox */
    border: 2px solid white; /* Bordure blanche */
    border-radius: 5px; /* Coins arrondis */
    background-color: transparent; /* Fond transparent */
    transition: background-color 0.3s; /* Transition pour l'effet de survol */
}

/* Styles pour le faux checkbox lorsqu'il est coché */
input[type="checkbox"]:checked + label::before {
    background-color: #0084ff; /* Couleur de fond lorsque coché */
    border-color: #0084ff; /* Couleur de bordure lorsque coché */
}

/* Styles pour le faux checkbox lorsqu'il est coché */
input[type="checkbox"]:checked + label::after {
    content: '✔'; /* Ajouter une coche */
    position: absolute;
    left: 5px; /* Position de la coche */
    top: 50%;
    transform: translateY(-50%);
    color: white; /* Couleur de la coche */
    font-size: 16px; /* Taille de la coche */
}

/* Styles pour les images des drogues */
.drug-image {
    width: 40px; /* Ajustez la largeur selon vos besoins */
    height: 40px; /* Conserver le ratio d'aspect */
    margin-left: 10px; /* Espacement entre le texte et l'image */
    vertical-align: middle; /* Aligner l'image avec le texte */
}

/* Styles pour la barre de défilement */
#drugList {
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Aligner les éléments à gauche */
    margin-top: 20px;
    max-height: 300px; /* Hauteur maximale pour le conteneur */
    overflow-y: auto; /* Activer le défilement vertical */
    width: 80%; /* Largeur de la liste */
    padding-left: 15px;
    padding-right: 5px; /* Augmenter l'espacement à droite pour la barre de défilement */
    margin-right: 0px; /* Ajouter un décalage à droite pour la barre de défilement */
}

/* Personnalisation de la barre de défilement */
#drugList::-webkit-scrollbar {
    width: 10px; /* Largeur de la barre de défilement */
}

#drugList::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1); /* Couleur de fond de la piste */
    border-radius: 10px; /* Coins arrondis */
}

#drugList::-webkit-scrollbar-thumb {
    background: #0084ff; /* Couleur de la barre de défilement */
    border-radius: 10px; /* Coins arrondis */
}

#drugList::-webkit-scrollbar-thumb:hover {
    background: #0056b3; /* Couleur de la barre de défilement au survol */
}

