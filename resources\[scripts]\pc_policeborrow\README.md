# Bloody-policeborrow
Enhance Your RP By making the Police borrow vehicle from NPCs 

**🚔 Police Vehicle Borrowing Script Features**

**1. Realistic Police Badge Presentation**
- Officer must present police badge to commandeer vehicle
- Animated badge display with progress bar
- Configurable badge item requirement

**2. NPC Interaction System**
- Realistic NPC driver behavior
- Proper vehicle exiting animation
- Respectful salute animation when acknowledging police authority
- Smooth approach sequence to officer's position

**3. Key Handover Mechanics**
- Synchronized key transfer animations (officer & NPC)
- Proper facing mechanics ensure realistic interaction
- Distance control maintains appropriate spacing
- Vehicle keys automatically assigned to officer

**4. Vehicle Access Control**
- Automatic unlocking after key transfer
- Hotwiring requirement removed for police
- Configurable cooldown period between uses
- Job restriction (police only)

**5. Immersive Animations**
- Badge presentation animation
- NPC salute animation
- Dual key handover animation
- Natural NPC walk-away sequence

**6. Configurable Options**
- Police badge item name configurable
- Cooldown timer adjustable
- Distance checks customizable
- Animation durations tunable

**7. Error Handling & Feedback**
- Clear notifications for all actions
- Proper checks for NPC presence
- Cooldown timer feedback
- Job requirement enforcement

**8. Performance Optimized**
- Proper animation dictionary loading/unloading
- Mission entity management
- Clean task clearing
- No memory leaks

**9. Realistic Police Protocol Simulation**
- NPCs properly acknowledge police authority
- Respectful distance maintained
- Professional interaction sequence
- Natural conclusion to encounter

**10. QB-Core Integration**
- Fully integrated with QB inventory system
- Works with QB target system
- Compatible with QB vehicle keys
- Uses QB notification system

This script creates a realistic police procedure for commandeering civilian vehicles while maintaining game immersion and proper roleplay standards. All features work together to simulate authentic police-civilian interactions during vehicle borrowing scenarios.


preview: https://streamable.com/63lw7s

My discord server: https://discord.gg/procode
