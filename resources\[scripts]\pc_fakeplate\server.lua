local QBCore = exports['qb-core']:GetCoreObject()

-- Fonction pour vérifier si un joueur est propriétaire d'un véhicule (avec fausse plaque)
function IsVehicleOwner(plate, citizenid, callback)
    exports.oxmysql:query('SELECT * FROM player_vehicles WHERE plate = ? OR fakeplate = ?', {plate, plate}, function(result)
        if result and result[1] then
            callback(result[1].citizenid == citizenid)
        else
            callback(false)
        end
    end)
end

-- Fonction pour obtenir la plaque originale d'un véhicule
function GetOriginalPlate(fakePlate, callback)
    exports.oxmysql:query('SELECT plate FROM player_vehicles WHERE fakeplate = ?', {fakePlate}, function(result)
        if result and result[1] then
            callback(result[1].plate)
        else
            callback(fakePlate)
        end
    end)
end

-- Fonction pour vérifier si une plaque est une fausse plaque
function IsFakePlate(plate, callback)
    
    exports.oxmysql:query('SELECT fakeplate FROM player_vehicles WHERE fakeplate = ?', {plate}, function(result)
        
        if result and result[1] and result[1].fakeplate then
            callback(true)
        else
            callback(false)
        end
    end)
end

RegisterNetEvent('pc_fakeplate:tryInstall', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if Player and Player.Functions.GetItemByName(Config.FakePlateItem) then
        TriggerClientEvent('pc_fakeplate:client:installPlate', src)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Fausse Plaque',
            description = _U('no_fakeplate_item'),
            type = 'error'
        })
    end
end)

RegisterNetEvent('pc_fakeplate:checkVehicleOwnership', function(plate, vehicle)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if Player then
        -- Vérifier si le véhicule appartient à un joueur dans la base de données
        -- Chercher soit par plaque d'origine, soit par fausse plaque
        exports.oxmysql:query('SELECT * FROM player_vehicles WHERE plate = ? OR fakeplate = ?', {plate, plate}, function(result)
            if result and result[1] then
                -- Vérifier si le véhicule a déjà une fausse plaque
                if result[1].fakeplate and result[1].fakeplate ~= "" then
                    TriggerClientEvent('ox_lib:notify', src, {
                        title = 'Fausse Plaque',
                        description = _U('vehicle_already_has_fakeplate'),
                        type = 'error'
                    })
                    return
                end
                
                -- Le véhicule appartient à un joueur et n'a pas de fausse plaque, permettre l'installation
                TriggerClientEvent('pc_fakeplate:startInstallation', src, vehicle, plate)
            else
                -- Le véhicule n'appartient à personne
                TriggerClientEvent('ox_lib:notify', src, {
                    title = 'Fausse Plaque',
                    description = _U('vehicle_not_owned'),
                    type = 'error'
                })
            end
        end)
    end
end)

RegisterNetEvent('pc_fakeplate:removeItem', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if Player then
        Player.Functions.RemoveItem(Config.FakePlateItem, 1)
        TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[Config.FakePlateItem], "remove")
    end
end)

RegisterNetEvent('pc_fakeplate:saveFakePlate', function(originalPlate, fakePlate)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if Player then
        -- Sauvegarder la fausse plaque dans la base de données
        exports.oxmysql:query('UPDATE player_vehicles SET fakeplate = ? WHERE plate = ?', {fakePlate, originalPlate}, function(affectedRows)
            local count = type(affectedRows) == 'table' and affectedRows.affectedRows or affectedRows
            if count and count > 0 then
                -- Mettre le poids max du coffre et de la boîte à gants à 0
                local trunkInv = 'trunk:' .. fakePlate
                local gloveboxInv = 'glovebox:' .. fakePlate
                exports.ox_inventory:SetMaxWeight(trunkInv, 0)
                exports.ox_inventory:SetMaxWeight(gloveboxInv, 0)
            else
            end
        end)
    end
end)

-- Event pour vérifier si une plaque est une fausse plaque
RegisterNetEvent('pc_fakeplate:checkIfFakePlate', function(plate)
    local src = source
    
    IsFakePlate(plate, function(isFakePlate)
        TriggerClientEvent('pc_fakeplate:fakePlateResponse', src, isFakePlate)
    end)
end)

-- Event pour vérifier si le joueur a des véhicules avec des fausses plaques
RegisterNetEvent('pc_fakeplate:checkPlayerFakePlates', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if Player then
        local citizenid = Player.PlayerData.citizenid
        exports.oxmysql:query('SELECT COUNT(*) as count FROM player_vehicles WHERE citizenid = ? AND fakeplate IS NOT NULL AND fakeplate != ""', {citizenid}, function(result)
            if result and result[1] and result[1].count > 0 then
                TriggerClientEvent('pc_fakeplate:playerFakePlatesResponse', src, true)
            else
                TriggerClientEvent('pc_fakeplate:playerFakePlatesResponse', src, false)
            end
        end)
    end
end)

-- Event pour retirer une fausse plaque et rétablir le poids d'origine
RegisterNetEvent('pc_fakeplate:removeFakePlate', function(originalPlate, fakePlate)
    -- Récupérer le modèle du véhicule à partir de la plaque d'origine
    exports.oxmysql:query('SELECT vehicle FROM player_vehicles WHERE plate = ?', {originalPlate}, function(result)
        if result and result[1] and result[1].vehicle then
            local model = result[1].vehicle
            -- Charger la table des classes et poids d'ox_inventory
            local vehiclesData = LoadResourceFile('ox_inventory', 'data/vehicles.lua')
            local vehicles = load(vehiclesData)()
            -- Déterminer la classe du véhicule
            local class = vehicles.Storage[model] or 1 -- 1 = Sedan par défaut si inconnu
            -- Récupérer le poids d'origine
            local trunkWeight = vehicles.trunk[class] and vehicles.trunk[class][2] or 328000
            local gloveboxWeight = vehicles.glovebox[class] and vehicles.glovebox[class][2] or 88000
            -- Remettre la colonne fakeplate à NULL
            exports.oxmysql:query('UPDATE player_vehicles SET fakeplate = NULL WHERE plate = ?', {originalPlate}, function(affectedRows)
                if affectedRows and affectedRows > 0 then
                    local trunkInv = 'trunk:' .. fakePlate
                    local gloveboxInv = 'glovebox:' .. fakePlate
                    exports.ox_inventory:SetMaxWeight(trunkInv, trunkWeight)
                    exports.ox_inventory:SetMaxWeight(gloveboxInv, gloveboxWeight)
                else
                end
            end)
        else
        end
    end)
end)

-- Event serveur appelé par l'event client pour ox_inventory
RegisterNetEvent('pc_fakeplate:useRemoveFakePlateItem', function(netId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if not vehicle or vehicle == 0 then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Fausse Plaque',
            description = 'Impossible de détecter le véhicule.',
            type = 'error'
        })
        return
    end
    local plate = QBCore.Functions.GetPlate(vehicle)
    if not plate then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Fausse Plaque',
            description = 'Impossible de détecter la plaque du véhicule.',
            type = 'error'
        })
        return
    end
    exports.oxmysql:query('SELECT plate, fakeplate FROM player_vehicles WHERE fakeplate = ?', {plate}, function(result)
        if result and result[1] and result[1].plate then
            local originalPlate = result[1].plate
            local fakePlate = plate
            SetVehicleNumberPlateText(vehicle, originalPlate)
            -- Synchroniser le retrait de la fausse plaque avec tous les joueurs
            TriggerClientEvent('pc_fakeplate:syncFakePlateToClient', -1, netId, originalPlate)
            TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[Config.RemoveFakePlateItem], "remove")
            TriggerEvent('pc_fakeplate:removeFakePlate', originalPlate, fakePlate)
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Fausse Plaque',
                description = 'La fausse plaque a été retirée et la plaque d\'origine remise.',
                type = 'success'
            })
        else
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Fausse Plaque',
                description = 'Ce véhicule n\'a pas de fausse plaque.',
                type = 'error'
            })
        end
    end)
end)

-- Event pour synchroniser la fausse plaque avec tous les joueurs
RegisterNetEvent('pc_fakeplate:syncFakePlate', function(netId, newPlate)
    -- Envoyer la fausse plaque à tous les joueurs
    TriggerClientEvent('pc_fakeplate:syncFakePlateToClient', -1, netId, newPlate)
end)

-- Event pour démarrer le retrait de fausse plaque côté serveur
RegisterNetEvent('pc_fakeplate:startRemoveFakePlate', function(netId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player then 
        return 
    end
    
    -- Vérifier si le joueur a l'item de retrait
    if not Player.Functions.GetItemByName(Config.RemoveFakePlateItem) then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Fausse Plaque',
            description = 'Vous n\'avez pas l\'outil nécessaire pour retirer la fausse plaque.',
            type = 'error'
        })
        return
    end
    
    -- Retirer l'item
    Player.Functions.RemoveItem(Config.RemoveFakePlateItem, 1)
    TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[Config.RemoveFakePlateItem], "remove")
    
    -- Procéder au retrait
    TriggerEvent('pc_fakeplate:useRemoveFakePlateItem', netId)
end)

-- Event pour forcer la mise à jour du poids d'un inventaire
RegisterNetEvent('pc_fakeplate:forceWeightUpdate', function(inventoryId)
    local src = source
    if inventoryId then
        -- Appliquer le poids à 0
        exports.ox_inventory:SetMaxWeight(inventoryId, 0)
        -- Forcer la mise à jour côté client
        TriggerClientEvent('ox_inventory:updateInventory', src, inventoryId, {maxWeight = 0})
    end
end)

-- Export pour que d'autres scripts puissent vérifier la propriété
exports('IsVehicleOwner', IsVehicleOwner)
exports('GetOriginalPlate', GetOriginalPlate)
exports('IsFakePlate', IsFakePlate)
