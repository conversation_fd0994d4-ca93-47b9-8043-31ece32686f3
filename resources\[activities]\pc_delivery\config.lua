Config = {}

-- Configuration générale
Config.Debug = false
Config.MinimumPlayers = 1 -- Nombre minimum de joueurs pour commencer l'activité
Config.MaximumPlayers = 4 -- Nombre maximum de joueurs par groupe

-- Configuration des multiplicateurs de groupe
Config.GroupMultipliers = {
    [1] = 1.0,    -- 1 joueur = récompense normale
    [2] = 1.1,    -- 2 joueurs = 10% de bonus
    [3] = 1.2,    -- 3 joueurs = 20% de bonus
    [4] = 1.25     -- 4 joueurs = 25% de bonus
}

-- Configuration du ped et du point de départ
Config.StartPoint = {
    ped = {
        model = "s_m_m_postal_01",
        coords = vec4(69.06, 127.56, 79.21, 163.63),
    },
    blip = {
        sprite = 478,
        color = 5,
        scale = 0.8,
        label = "Livreur"
    }
}

-- Configuration des véhicules
Config.Vehicles = {
    {
        model = "boxville2",
        coords = vec4(61.29, 124.51, 79.22, 163.73)
    }
}

-- Configuration des jobs autorisés
Config.AllowedJobs = {} -- Table vide pour permettre à tous les joueurs d'accéder à l'activité

-- Configuration des différentes runs possibles
Config.Runs = {
    {
        name = "Livraison Standard",
        chance = 100, -- 100% de chance
        deliveryPoints = {
            {
                label = "Point de livraison",
                coords = vector3(53.252552032471, 114.12822723389, 80.192817687988)
            },
            {
                label = "Point de livraison",
                coords = vector3(55.357364654541, 113.50793457031, 79.809669494629)
            },
        },
        rewards = {
            complete = {
                money = 1500,
                -- items = {
                --     {
                --         name = 'plastic',
                --         amount = {
                --             min = 3,
                --             max = 5
                --         }
                --     },
                -- }
            }
        }
    },
    -- {
    --     name = "Livraison Premium",
    --     chance = 0, -- 10% de chance
    --     deliveryPoints = {
    --         {
    --             label = "Point de livraison",
    --             coords = vector3(-350.90905761719, -1664.2722167969, 17.744537353516)
    --         },
    --         {
    --             label = "Point de livraison",
    --             coords = vector3(-374.33047485352, -1538.2844238281, 27.139883041382)
    --         },
    --     },
    --     rewards = {
    --         complete = {
    --             money = 2500,
    --         }
    --     }
    -- },
} 