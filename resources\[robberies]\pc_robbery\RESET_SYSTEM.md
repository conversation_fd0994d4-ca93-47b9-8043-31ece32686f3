# Système de Réinitialisation - ASC Robbery

## Vue d'ensemble

Le script ASC Robbery dispose maintenant d'un système de réinitialisation automatique et manuel complet qui gère tous les états de braquage, cooldowns et verrous.

## Réinitialisation Automatique

### Quand la réinitialisation se déclenche automatiquement :

1. **Au démarrage du script** : Tous les états sont réinitialisés
2. **À l'arrêt du script** : Nettoyage complet des états
3. **À la fin de tous les cooldowns** : Réinitialisation automatique des flags d'état côté client

### États réinitialisés automatiquement :

**Côté Serveur :**
- `registerLocks` : Verrous des caisses
- `safeLocks` : Verrous des coffres
- `registerCooldowns` : Cooldowns des caisses
- `safeCooldowns` : Cooldowns des coffres
- `bankCooldowns` : Cooldowns des banques
- `globalCooldown` : Cooldown global
- `cooldownTimers` : Tous les timers

**Côté Client :**
- `hasThreatenedGuard` : Flag de menace du garde
- `hasInsertedCard` : Flag d'insertion de carte
- `hasUsedThermite` : Flag d'utilisation de thermite
- `drilledVaults` : Liste des coffres percés

## Commandes Administrateur

### `/resetrobbery`
- **Permission requise** : `command.resetrobbery` ou console serveur
- **Description** : Réinitialise immédiatement tous les états de braquage
- **Usage** : Tapez `/resetrobbery` en jeu ou dans la console

### `/robberyStatus`
- **Permission requise** : `command.resetrobbery` ou console serveur
- **Description** : Affiche le statut actuel de tous les cooldowns
- **Usage** : Tapez `/robberyStatus` en jeu ou dans la console

### `/testBankDoors`
- **Permission requise** : `command.resetrobbery` ou console serveur
- **Description** : Teste et affiche les portes ox_doorlock associées aux banques
- **Usage** : Tapez `/testBankDoors` en jeu ou dans la console

### `/resetrobberyclient`
- **Description** : Réinitialise les états côté client (commande joueur)
- **Usage** : Tapez `/resetrobberyclient` en jeu

## Configuration des Permissions

Ajoutez ces lignes à votre `server.cfg` ou système de permissions :

```
# Permissions pour les commandes de braquage
add_ace group.admin command.resetrobbery allow
add_ace identifier.steam:XXXXXXXXXXXXXXX command.resetrobbery allow
```

## Fonctionnement Technique

### Réinitialisation en Cascade
1. Quand un cooldown se termine, le système vérifie si tous les autres cooldowns sont également terminés
2. Si oui, il déclenche automatiquement la réinitialisation des états côté client
3. Cela permet aux joueurs de recommencer les braquages sans redémarrer le script

### Sécurité
- Les verrous sont maintenus pendant les actions pour éviter les exploits
- La réinitialisation ne supprime pas les cooldowns actifs
- Seuls les administrateurs peuvent forcer une réinitialisation complète

## Messages de Debug

Le système affiche des messages colorés dans la console :
- `^2[ASC_ROBBERY]^7` : Messages de succès (vert)
- `^3[ASC_ROBBERY]^7` : Messages d'information (jaune)
- `^1[ASC_ROBBERY]^7` : Messages d'erreur (rouge)

## Dépannage

### Problème : Les joueurs ne peuvent plus braquer après un cooldown
**Solution** : Utilisez `/resetrobbery` pour forcer la réinitialisation

### Problème : Les états ne se réinitialisent pas automatiquement
**Solution** : Vérifiez que tous les cooldowns sont bien terminés avec `/robberyStatus`

### Problème : Erreurs de permissions
**Solution** : Vérifiez la configuration des ACE permissions dans server.cfg

## Intégration avec d'autres Scripts

Le système envoie des événements que d'autres scripts peuvent écouter :
- `asc_robbery:resetClientStates` : Réinitialisation côté client
- `asc_robbery:resetAllStates` : Réinitialisation complète

## Maintenance

Pour maintenir le système en bon état :
1. Surveillez les logs pour détecter les erreurs
2. Utilisez `/robberyStatus` régulièrement pour vérifier l'état
3. Redémarrez le script périodiquement si nécessaire
