local QBCore = exports['qb-core']:GetCoreObject()

-- Variables locales
local policeCount = 0

-- Événement pour recevoir le nombre de policiers
RegisterNetEvent('asc_robbery:receivePoliceCount')
AddEventHandler('asc_robbery:receivePoliceCount', function(count)
    policeCount = count
end)

-- Fonction pour vérifier le nombre de policiers
local function checkPolice()
    policeCount = 0 -- Réinitialiser le compteur
    TriggerServerEvent('asc_robbery:checkPolice')
    Wait(100) -- Attendre la réponse du serveur
    
    if policeCount < Config.General.Stores.minPolice then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Pas assez de policiers en ville.',
            type = 'error'
        })
        return false
    end
    return true
end

-- Fonction pour vérifier l'item requis pour la caisse
local function checkRegisterItem()
    local hasItem = exports.ox_inventory:Search('count', Config.General.Stores.requiredItemRegister)
    
    if hasItem < 1 then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Vous avez besoin d\'un ' .. Config.General.Stores.requiredItemRegister .. ' pour braquer la caisse.',
            type = 'error'
        })
        return false
    end
    return true
end

-- Fonction pour vérifier l'item requis pour le coffre
local function checkSafeItem()
    local hasItem = exports.ox_inventory:Search('count', Config.General.Stores.requiredItemSafe)
    
    if hasItem < 1 then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Vous avez besoin d\'un ' .. Config.General.Stores.requiredItemSafe .. ' pour braquer le coffre.',
            type = 'error'
        })
        return false
    end
    return true
end

-- Fonction pour tenter de braquer une caisse
function attemptRobbery(storeCoords, registerIndex)
    
    -- Vérifier le nombre de policiers
    TriggerServerEvent('asc_robbery:checkPolice')
    local policeCount = 0
    RegisterNetEvent('asc_robbery:receivePoliceCount')
    AddEventHandler('asc_robbery:receivePoliceCount', function(count)
        policeCount = count
    end)
    Wait(100) -- Attendre la réponse du serveur
    
    if policeCount < Config.General.Stores.minPolice then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Pas assez de policiers en ville',
            type = 'error'
        })
        return
    end
    
    -- Vérifier si le joueur a l'item requis
    local hasItem = exports.ox_inventory:Search('count', Config.General.Stores.requiredItemRegister) > 0
    if not hasItem then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Vous n\'avez pas l\'outil nécessaire',
            type = 'error'
        })
        return
    end
    
    -- Vérifier si la caisse peut être braquée
    TriggerServerEvent('asc_robbery:checkRegister', storeCoords, registerIndex)
    local canRob = false
    RegisterNetEvent('asc_robbery:registerCheckResult')
    AddEventHandler('asc_robbery:registerCheckResult', function(result)
        canRob = result
    end)
    Wait(100) -- Attendre la réponse du serveur
    
    if not canRob then
        return
    end
    
    -- Démarrer le mini-jeu
    local success = lib.skillCheck({'easy', 'easy', 'easy'})
    if not success then
        exports['ox_lib']:notify({
            title = 'Échec',
            description = 'Vous avez échoué à crocheter la caisse. Réessayez...',
            type = 'error'
        })
        -- Libérer le verrou en cas d'échec
        TriggerServerEvent('asc_robbery:releaseRegisterLock', storeCoords, registerIndex)
        return
    end
    
    -- Appeler la police avant la barre de progression
    local camId = 1
    for i, store in ipairs(Config.Stores) do
        if store.coords == storeCoords then
            camId = i
            break
        end
    end
    exports['ps-dispatch']:StoreRobbery(camId)
    
    -- Démarrer la barre de progression
    if lib.progressBar({
        duration = 10000,
        label = 'Braquage en cours...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
        anim = {
            dict = 'anim@heists@ornate_bank@grab_cash',
            clip = 'grab'
        },
    }) then
        -- Si la barre de progression est complétée avec succès
        TriggerServerEvent('asc_robbery:giveRegisterReward', storeCoords, registerIndex)
    else
        exports['ox_lib']:notify({
            title = 'Échec',
            description = 'Braquage annulé',
            type = 'error'
        })
        -- Libérer le verrou en cas d'annulation
        TriggerServerEvent('asc_robbery:releaseRegisterLock', storeCoords, registerIndex)
    end
end

-- Fonction pour tenter de braquer un coffre
function attemptSafeRobbery(storeCoords)
    
    -- Vérifier le nombre de policiers
    TriggerServerEvent('asc_robbery:checkPolice')
    local policeCount = 0
    RegisterNetEvent('asc_robbery:receivePoliceCount')
    AddEventHandler('asc_robbery:receivePoliceCount', function(count)
        policeCount = count
    end)
    Wait(100) -- Attendre la réponse du serveur
    
    if policeCount < Config.General.Stores.minPolice then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Pas assez de policiers en ville',
            type = 'error'
        })
        return
    end
    
    -- Vérifier si le joueur a l'item requis
    local hasItem = exports.ox_inventory:Search('count', Config.General.Stores.requiredItemSafe) > 0
    if not hasItem then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Vous n\'avez pas l\'outil nécessaire',
            type = 'error'
        })
        return
    end
    
    -- Vérifier si le coffre peut être braqué
    TriggerServerEvent('asc_robbery:checkSafe', storeCoords)
    local canRob = false
    RegisterNetEvent('asc_robbery:safeCheckResult')
    AddEventHandler('asc_robbery:safeCheckResult', function(result)
        canRob = result
    end)
    Wait(100) -- Attendre la réponse du serveur
    
    if not canRob then
        return
    end
    
    -- Démarrer le mini-jeu
    local success = lib.skillCheck({'medium', 'medium', 'hard'})
    if not success then
        exports['ox_lib']:notify({
            title = 'Échec',
            description = 'Vous avez échoué à percer le coffre. Réessayez...',
            type = 'error'
        })
        -- Libérer le verrou en cas d'échec
        TriggerServerEvent('asc_robbery:releaseSafeLock', storeCoords)
        return
    end
    
    exports['ps-dispatch']:StoreRobbery(camId)
    
    -- Démarrer la barre de progression
    if lib.progressBar({
        duration = 15000,
        label = 'Percage du coffre en cours...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
        anim = {
            dict = 'anim@heists@ornate_bank@grab_cash',
            clip = 'grab'
        },
    }) then
        -- Si la barre de progression est complétée avec succès
        TriggerServerEvent('asc_robbery:giveSafeReward', storeCoords)
    else
        exports['ox_lib']:notify({
            title = 'Échec',
            description = 'Braquage annulé',
            type = 'error'
        })
        -- Libérer le verrou en cas d'annulation
        TriggerServerEvent('asc_robbery:releaseSafeLock', storeCoords)
    end
end

-- Initialisation des points d'interaction
Citizen.CreateThread(function()
    for i, store in ipairs(Config.Stores) do
        -- Points d'interaction pour les caisses
        for j, register in ipairs(store.registers) do
            exports.ox_target:addBoxZone({
                coords = vector3(register.x, register.y, register.z),
                size = vector3(1, 1, 2),
                rotation = 0,
                debug = false,
                options = {
                    {
                        name = 'rob_register_' .. i .. '_' .. j,
                        icon = 'fas fa-cash-register',
                        label = 'Braquer la caisse',
                        onSelect = function()
                            if checkPolice() and checkRegisterItem() then
                                attemptRobbery(store.coords, j)
                            end
                        end
                    }
                }
            })
        end
        
        -- Point d'interaction pour le coffre
        exports.ox_target:addBoxZone({
            coords = vector3(store.safe.x, store.safe.y, store.safe.z),
            size = vector3(1, 1, 2),
            rotation = 0,
            debug = false,
            options = {
                {
                    name = 'rob_safe_' .. i,
                    icon = 'fas fa-vault',
                    label = 'Braquer le coffre',
                    onSelect = function()
                        if checkPolice() and checkSafeItem() then
                            attemptSafeRobbery(store.coords)
                        end
                    end
                }
            }
        })
    end
end)
