Citizen.CreateThread(function()
    while Core == nil do
        Citizen.Wait(0)
    end
    RegisterCallback('tworst-garages:server:getHouseGarage', function(source, cb, house)
        local src = source
        local Player = GetPlayer(src)
        if not Player then return end
        local houseGarage = ExecuteSql('SELECT * FROM properties WHERE property_id = ?',
            { house })
        if houseGarage and houseGarage[1] then
            cb(houseGarage)
        end
    end)

    RegisterCallback('tworst-garages:server:canDeposit', function(source, cb, data)
        local src = source
        local Player = GetPlayer(src)
        local identifier = GetIdentifier(src)
        local isOwned
        local sharedKeys = {}

        GetOriginalPlateFromFake(data.plate, function(actualPlate)
            if Config.vehicleSQL == 'player_vehicles' then
                isOwned = ExecuteSql('SELECT citizenid, shared FROM player_vehicles WHERE plate = ? LIMIT 1', { actualPlate })
            elseif Config.vehicleSQL == 'owned_vehicles' then
                isOwned = ExecuteSql('SELECT owner AS citizenid, shared FROM owned_vehicles WHERE plate = ? LIMIT 1', { actualPlate })
            end

            if next(isOwned) == nil then
                cb(false)
                return
            end

            if isOwned[1].shared then
                sharedKeys = json.decode(isOwned[1].shared) or {}
            end

            local isAllowed = isOwned[1].citizenid == identifier
            if not isAllowed then
                for _, sharedKey in ipairs(sharedKeys) do
                    if sharedKey.identifier == identifier then
                        isAllowed = true
                        break
                    end
                end
            end

            if not Config.AllowParkingAnyonesVehicle and not isAllowed then
                cb(false)
                return
            end

            if isAllowed then
                local exportresult
                if Config.Framework == "qb" or Config.Framework == "qbx" or Config.Framework == "oldqb" then
                    if GetResourceState("qb-houses") == "started" then
                        exportresult = exports['qb-houses']:hasKey(Player.PlayerData.license, identifier, Config.Garages[data.indexgarage].houseName)
                    else
                        exportresult = false
                    end
                else
                    exportresult = false
                end

                if data.type == 'house' and not exportresult then
                    cb(false)
                    return
                end

                if data.state == 1 then
                    if Config.vehicleSQL == 'player_vehicles' then
                        ExecuteSql('UPDATE player_vehicles SET state = ?, garage = ? WHERE plate = ?', { data.state, data.garage, actualPlate })
                    elseif Config.vehicleSQL == 'owned_vehicles' then
                        ExecuteSql('UPDATE owned_vehicles SET stored = ?, parking = ? WHERE plate = ?', { data.state, data.garage, actualPlate })
                    end
                    cb(true)
                else
                    cb(false)
                end
            else
                cb(false)
            end
        end)
    end)
end)


RegisterNetEvent('tworst-garages:server:syncGarage', function(updatedGarages)
    Config.Garages = updatedGarages
end)

local function getAllGarages()
    local garages = {}
    for k, v in pairs(Config.Garages) do
        garages[#garages + 1] = {
            name = k,
            label = v.label,
            type = v.type,
            takeVehicle = v.takeVehicle,
            putVehicle = v.putVehicle,
            spawnPoint = v.spawnPoint,
            showBlip = v.showBlip,
            blipName = v.blipName,
            blipNumber = v.blipNumber,
            blipColor = v.blipColor,
            vehicle = v.vehicle
        }
    end
    return garages
end
exports('getAllGarages', getAllGarages)
