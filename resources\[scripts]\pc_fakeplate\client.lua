local QBCore = exports['qb-core']:GetCoreObject()

-- SUPPRESSION DU SYSTEME DE BLOCAGE D'INVENTAIRE LORS D'UNE FAUSSE PLAQUE
-- Toutes les variables, threads et events liés à inventoryBlocked, currentVehicleWithFakePlate, et LocalPlayer.state.invBusy sont supprimés.

-- Les events et threads qui interceptaient l'ouverture de l'inventaire ou du coffre sont supprimés.

-- Il ne reste que la logique d'installation de la fausse plaque et la gestion de l'item.

RegisterNetEvent('pc_fakeplate:client:installPlate', function()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local vehicle = QBCore.Functions.GetClosestVehicle(playerCoords)
    
    if vehicle ~= 0 then
        -- Vérifier si le joueur est derrière le véhicule
        local vehicleCoords = GetEntityCoords(vehicle)
        local vehicleHeading = GetEntityHeading(vehicle)
        local behindVehicle = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, -3.0, 0.0)
        local distanceToBehind = #(playerCoords - behindVehicle)
        
        if distanceToBehind <= 2.0 then
            -- Récupérer la plaque du véhicule
            local plate = QBCore.Functions.GetPlate(vehicle)
            if plate then
                -- Vérifier si le véhicule appartient à un joueur
                TriggerServerEvent('pc_fakeplate:checkVehicleOwnership', plate, vehicle)
            else
                lib.notify({
                    title = 'Fausse Plaque',
                    description = _U('vehicle_not_owned'),
                    type = 'error'
                })
            end
        else
            lib.notify({
                title = 'Fausse Plaque',
                description = _U('not_behind_vehicle'),
                type = 'error'
            })
        end
    else
        lib.notify({
            title = 'Fausse Plaque',
            description = _U('no_vehicle_nearby'),
            type = 'error'
        })
    end
end)

RegisterNetEvent('pc_fakeplate:startInstallation', function(vehicle, originalPlate)
    
    -- Animation de réparation/installation
    local playerPed = PlayerPedId()
    local animDict = "mini@repair"
    local animName = "fixing_a_player"
    
    -- Charger l'animation
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    -- Jouer l'animation
    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, -1, 1, 0, false, false, false)
    
    if lib.progressBar({
        duration = Config.InstallTime,
        label = _U('installing_fakeplate'),
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
    }) then
        -- Arrêter l'animation
        ClearPedTasks(playerPed)
        
        local newPlate = 'FAKE'..math.random(1000,9999)
        
        -- Synchroniser la fausse plaque avec tous les joueurs
        local netId = NetworkGetNetworkIdFromEntity(vehicle)
        TriggerServerEvent('pc_fakeplate:syncFakePlate', netId, newPlate)
        
        TriggerServerEvent('pc_fakeplate:saveFakePlate', originalPlate, newPlate)
        -- Retirer l'item
        TriggerServerEvent('pc_fakeplate:removeItem')
        lib.notify({
            title = 'Fausse Plaque',
            description = _U('fakeplate_installed'),
            type = 'success'
        })
    else
        -- Arrêter l'animation si annulé
        ClearPedTasks(playerPed)
        
        lib.notify({
            title = 'Fausse Plaque',
            description = _U('installation_cancelled'),
            type = 'error'
        })
    end
end)

-- Event pour synchroniser la fausse plaque avec tous les joueurs
RegisterNetEvent('pc_fakeplate:syncFakePlateToClient', function(netId, newPlate)
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if vehicle and vehicle ~= 0 then
        SetVehicleNumberPlateText(vehicle, newPlate)
    end
end)

-- Utilisation de l'item fakeplate
RegisterNetEvent('pc_fakeplate:useItem', function()
    TriggerServerEvent('pc_fakeplate:tryInstall')
end) 

-- Event pour recevoir la réponse de vérification de fausse plaque
RegisterNetEvent('pc_fakeplate:fakePlateResponse', function(isFakePlate)
    print('[FAKEPLATE DEBUG CLIENT] Réponse vérification fausse plaque:', isFakePlate)
    
    if not isFakePlate then
        -- Pas de fausse plaque, notification directe
        lib.notify({
            title = 'Fausse Plaque',
            description = 'Ce véhicule n\'a pas de fausse plaque.',
            type = 'error'
        })
        return
    end
    
    -- Le véhicule a une fausse plaque, on peut procéder au retrait
    local playerPed = PlayerPedId()
    local vehicle = QBCore.Functions.GetClosestVehicle(GetEntityCoords(playerPed))
    local netId = NetworkGetNetworkIdFromEntity(vehicle)
    
    -- Animation de réparation/retrait
    local animDict = "mini@repair"
    local animName = "fixing_a_player"
    
    -- Charger l'animation
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    -- Jouer l'animation
    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, -1, 1, 0, false, false, false)
    
    if lib.progressBar({
        duration = Config.InstallTime, -- Même durée que l'installation
        label = _U('removing_fakeplate'),
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
    }) then
        -- Arrêter l'animation
        ClearPedTasks(playerPed)
        
        print('[FAKEPLATE DEBUG CLIENT] ProgressBar terminée, retrait réussi')
        -- Procéder au retrait de la fausse plaque
        TriggerServerEvent('pc_fakeplate:useRemoveFakePlateItem', netId)
    else
        -- Arrêter l'animation si annulé
        ClearPedTasks(playerPed)
        
        print('[FAKEPLATE DEBUG CLIENT] ProgressBar annulée')
        lib.notify({
            title = 'Fausse Plaque',
            description = _U('removal_cancelled'),
            type = 'error'
        })
    end
end) 

-- Event client pour retirer la fausse plaque (pour ox_inventory)
RegisterNetEvent('pc_fakeplate:client:removePlate', function()
    print('[FAKEPLATE DEBUG CLIENT] Event removePlate déclenché')
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local vehicle = QBCore.Functions.GetClosestVehicle(playerCoords)
    print('[FAKEPLATE DEBUG CLIENT] Véhicule trouvé:', vehicle)
    
    if vehicle ~= 0 then
        local vehicleCoords = GetEntityCoords(vehicle)
        local vehicleHeading = GetEntityHeading(vehicle)
        local behindVehicle = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, -3.0, 0.0)
        local distanceToBehind = #(playerCoords - behindVehicle)
        print('[FAKEPLATE DEBUG CLIENT] Distance derrière véhicule:', distanceToBehind)
        
        if distanceToBehind <= 2.0 then
            print('[FAKEPLATE DEBUG CLIENT] Position OK, vérification fausse plaque')
            -- On est bien derrière le véhicule, vérifier si il y a une fausse plaque
            local plate = QBCore.Functions.GetPlate(vehicle)
            TriggerServerEvent('pc_fakeplate:checkIfFakePlate', plate)
        else
            print('[FAKEPLATE DEBUG CLIENT] Position incorrecte')
            lib.notify({
                title = 'Fausse Plaque',
                description = 'Vous devez être derrière le véhicule pour retirer la fausse plaque.',
                type = 'error'
            })
        end
    else
        print('[FAKEPLATE DEBUG CLIENT] Aucun véhicule trouvé')
        lib.notify({
            title = 'Fausse Plaque',
            description = 'Aucun véhicule à proximité.',
            type = 'error'
        })
    end
end) 