return {
    electrical = vec4(-624.295, -215.22, 52.54, 118.0),
    doorlock = {
        name = 'vangelico_jewellery',
        hackTime = {
            min = 20,
            max = 60
        },
        requiredItem =  'electronickit',
        loseItemOnUse = true,
    },

    vitrines = {
        [1] = {coords = vec3(-626.83, -235.35, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 36.17},
        [2] = {coords = vec3(-625.81, -234.7, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 36.17},
        [3] = {coords = vec3(-626.95, -233.14, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 216.17},
        [4] = {coords = vec3(-628.0, -233.86, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 216.17},
        [5] = {coords = vec3(-625.7, -237.8, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 216.17},
        [6] = {coords = vec3(-626.7, -238.58, 38.05), isOpened = false, isBusy = false,  rayFire = 'DES_Jewel_Cab2', heading = 216.17},
        [7] = {coords = vec3(-624.55, -231.06, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 305.0},
        [8] = {coords = vec3(-623.13, -232.94, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 305.0},
        [9] = {coords = vec3(-620.29, -234.44, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 216.17},
        [10] = {coords = vec3(-619.15, -233.66, 38.05), isOpened = false, isBusy = false,  rayFire = 'DES_Jewel_Cab3', heading = 216.17},
        [11] = {coords = vec3(-620.19, -233.44, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 36.17},
        [12] = {coords = vec3(-617.63, -230.58, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab2', heading = 305.0},
        [13] = {coords = vec3(-618.33, -229.55, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 305.0},
        [14] = {coords = vec3(-619.7, -230.33, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 125.0},
        [15] = {coords = vec3(-620.95, -228.6, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 125.0},
        [16] = {coords = vec3(-619.79, -227.6, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab2', heading = 305.0},
        [17] = {coords = vec3(-620.42, -226.6, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab', heading = 305.0},
        [18] = {coords = vec3(-623.94, -227.18, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab4', heading = 36.17},
        [19] = {coords = vec3(-624.91, -227.87, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab3', heading = 36.17},
        [20] = {coords = vec3(-623.94, -228.05, 38.05), isOpened = false, isBusy = false, rayFire = 'DES_Jewel_Cab2', heading = 216.17},

        -- K4AMBI
        --[[[1] = {coords = vec3(-453.66, -76.95, 41.29), isOpened = false, isBusy = false, heading = 130.0},
        [2] = {coords = vec3(-454.91, -75.42, 41.29), isOpened = false, isBusy = false, heading = 130.0},
        [3] = {coords = vec3(-456.1, -73.9, 41.29), isOpened = false, isBusy = false, heading = 130.0},
        [4] = {coords = vec3(-457.38, -72.39, 41.29), isOpened = false, isBusy = false, heading = 130.0},
        [5] = {coords = vec3(-458.6, -70.87, 41.29), isOpened = false, isBusy = false, heading = 130.0},
        [6] = {coords = vec3(-459.84, -69.35, 41.29), isOpened = false, isBusy = false, heading = 130.0},
        [7] = {coords = vec3(-448.37, -72.7, 41.29), isOpened = false, isBusy = false, heading = 310.0},
        [8] = {coords = vec3(-449.61, -71.16, 41.29), isOpened = false, isBusy = false, heading = 310.0},
        [9] = {coords = vec3(-450.84, -69.67, 41.29), isOpened = false, isBusy = false, heading = 310.0},
        [10] = {coords = vec3(-452.13, -68.11, 41.29), isOpened = false, isBusy = false, heading = 310.0},
        [11] = {coords = vec3(-453.3, -66.62, 41.29), isOpened = false, isBusy = false, heading = 310.0},
        [12] = {coords = vec3(-454.63, -65.08, 41.29), isOpened = false, isBusy = false, heading = 310.0},
        [13] = {coords = vec3(-451.44, -73.17, 41.29), isOpened = false, isBusy = false, heading = 40.0},
        [14] = {coords = vec3(-452.7, -74.22, 41.29), isOpened = false, isBusy = false, heading = 40.0},
        [15] = {coords = vec3(-455.05, -73.11, 41.29), isOpened = false, isBusy = false, heading = 310.0},
        [16] = {coords = vec3(-455.5, -70.56, 41.29), isOpened = false, isBusy = false, heading = 220.0},
        [17] = {coords = vec3(-454.35, -69.64, 41.29), isOpened = false, isBusy = false, heading = 220.0},]]--
    },
}