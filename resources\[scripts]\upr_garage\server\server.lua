Core = nil
vehiclesData = {}

-- Fonction pour vérifier si un joueur est propriétaire d'un véhicule (avec fausse plaque)
function IsVehicleOwnerWithFakePlate(plate, citizenid, callback)
    if GetResourceState('pc_fakeplate') == 'started' then
        exports['pc_fakeplate']:IsVehicleOwner(plate, citizenid, callback)
    else
        -- Fallback si pc_fakeplate n'est pas disponible
        local result = ExecuteSql('SELECT * FROM player_vehicles WHERE plate = ? AND citizenid = ?', {plate, citizenid})
        callback(result and result[1] ~= nil)
    end
end

-- Fonction pour obtenir la plaque originale d'un véhicule
function GetOriginalPlateFromFake(fakePlate, callback)
    if GetResourceState('pc_fakeplate') == 'started' then
        exports['pc_fakeplate']:GetOriginalPlate(fakePlate, callback)
    else
        -- Fallback si pc_fakeplate n'est pas disponible
        callback(fakePlate)
    end
end

Citizen.CreateThread(function()
    Core = GetCore()
    while Core == nil do
        Citizen.Wait(0)
    end
    if Config.Framework == "qb" or Config.Framework == "qbx" or Config.Framework == "oldqb" then
        vehiclesData = Core.Shared.Vehicles

        if Config.AutoRespawn then
            ExecuteSql('UPDATE player_vehicles SET state = 1 WHERE state = 0', {})
        else
            ExecuteSql('UPDATE player_vehicles SET depotprice = 500 WHERE state = 0', {})
        end
    elseif Config.Framework == "esx" or Config.Framework == "oldesx" then
        vehiclesData = Vehicles
        if Config.AutoRespawn then
            ExecuteSql('UPDATE owned_vehicles SET stored = 1 WHERE stored = 0', {})
        end
    end
end)

Citizen.CreateThread(function()
    Core = GetCore()
    while Core == nil do
        Citizen.Wait(0)
    end
end)

local vehicleClasses = {
    compacts = 0,
    sedans = 1,
    suvs = 2,
    coupes = 3,
    muscle = 4,
    sportsclassics = 5,
    sports = 6,
    super = 7,
    motorcycles = 8,
    offroad = 9,
    industrial = 10,
    utility = 11,
    vans = 12,
    cycles = 13,
    boats = 14,
    helicopters = 15,
    planes = 16,
    service = 17,
    emergency = 18,
    military = 19,
    commercial = 20,
    trains = 21,
    openwheel = 22,
}

function arrayToSet(array)
    local set = {}
    for _, item in ipairs(array) do
        set[item] = true
    end
    return set
end

function filterVehiclesByCategory(vehicles, category)
    local filtered = {}
    local categorySet = arrayToSet(category)

    for _, vehicle in pairs(vehicles) do
        local vehicleData = vehiclesData[vehicle.vehicle]
        local vehicleCategoryString = vehicleData and vehicleData.category or 'compacts'
        local vehicleCategoryNumber = vehicleClasses[vehicleCategoryString]

        if vehicleCategoryNumber and categorySet[vehicleCategoryNumber] then
            filtered[#filtered + 1] = vehicle
        end
    end

    return filtered
end

Citizen.CreateThread(function()
    while Core == nil do
        Citizen.Wait(0)
    end
    RegisterCallback('tworst-garbage:server:getPlayerData', function(source, cb)
        local src = source
        local identifier = GetIdentifier(src)
        local dataInfo = {
            playerIdentifier = identifier,
            source = src,
        }
        cb(dataInfo)
    end)
    RegisterCallback('tworst-garages:server:changeVehicleName', function(source, cb, data)
        local src = source
        local Player = GetPlayer(src)
        if not Player then return end
        local identifier = GetIdentifier(src)
        if Config.vehicleSQL == 'player_vehicles' then
            ExecuteSql('UPDATE player_vehicles SET vehicleLabel = ? WHERE plate = ? AND citizenid = ?',
                { data.newName, data.plate, identifier })
            local vehicleData = ExecuteSql('SELECT vehicleLabel FROM player_vehicles WHERE plate = ? AND citizenid = ?',
                { data.plate, identifier })
            if vehicleData[1].vehicleLabel then
                cb(vehicleData[1].vehicleLabel)
            else
                cb('false')
            end
        else
            ExecuteSql('UPDATE owned_vehicles SET vehicleLabel = ? WHERE plate = ? AND owner = ?',
                { data.newName, data.plate, identifier })
            local vehicleData = ExecuteSql('SELECT vehicleLabel FROM owned_vehicles WHERE plate = ? AND owner = ?',
                { data.plate, identifier })
            if vehicleData[1].vehicleLabel then
                cb(vehicleData[1].vehicleLabel)
            else
                cb('false')
            end
        end
    end)
    RegisterCallback('tworst-garages:server:shareVehicleKeys', function(source, cb, data)
        local src = source
        local targetID = tonumber(data.id)
        local Player = GetPlayer(src)
        if not Player then return end
        local identifier = GetIdentifier(src)
        if src == targetID then
            TriggerClientEvent('tworst-garage:client:sendNotification', src,
                Config.NotificationText['cannotshareyourself'].text,
                Config.NotificationText['cannotshareyourself'].type)
            return
        end
        local targetPlayer = GetPlayer(targetID)
        if not targetPlayer then return end
        local distance = #(GetEntityCoords(GetPlayerPed(src)) - GetEntityCoords(GetPlayerPed(targetID)))
        if math.floor(distance) > 8 then
            TriggerClientEvent('tworst-garage:client:sendNotification', src, Config.NotificationText['playerfar'].text,
                Config.NotificationText['playerfar'].type)
            return
        end
        local targetPlayerName = GetName(targetID)
        local targetPlayerIdentifier = GetIdentifier(targetID)
        local targetPlayerTable = {
            name = targetPlayerName,
            identifier = targetPlayerIdentifier
        }

        local sharedKeys
        if Config.vehicleSQL == 'player_vehicles' then
            local result = ExecuteSql('SELECT shared FROM player_vehicles WHERE plate = ?', { data.plate })
            if result[1] and result[1].shared then
                sharedKeys = json.decode(result[1].shared)
                if not sharedKeys then sharedKeys = {} end
            else
                sharedKeys = {}
            end
        elseif Config.vehicleSQL == 'owned_vehicles' then
            local result = ExecuteSql('SELECT shared FROM owned_vehicles WHERE plate = ?', { data.plate })
            if result[1] and result[1].shared then
                sharedKeys = json.decode(result[1].shared)
                if not sharedKeys then sharedKeys = {} end
            else
                sharedKeys = {}
            end
        end

        for _, sharedKey in ipairs(sharedKeys) do
            if sharedKey.identifier == targetPlayerIdentifier then
                TriggerClientEvent('tworst-garage:client:sendNotification', src,
                    Config.NotificationText['alreadysharing'].text,
                    Config.NotificationText['alreadysharing'].type)
                return
            end
        end

        table.insert(sharedKeys, targetPlayerTable)
        if Config.vehicleSQL == 'player_vehicles' then
            ExecuteSql('UPDATE player_vehicles SET shared = ? WHERE plate = ? AND citizenid = ?',
                { json.encode(sharedKeys), data.plate, identifier })
        elseif Config.vehicleSQL == 'owned_vehicles' then
            ExecuteSql('UPDATE owned_vehicles SET shared = ? WHERE plate = ? AND owner = ?',
                { json.encode(sharedKeys), data.plate, identifier })
        end

        cb(sharedKeys)
    end)
    RegisterCallback('tworst-garages:server:removeSharedPlayer', function(source, cb, data)
        local src = source
        local Player = GetPlayer(src)
        if not Player then return end
        local identifier = GetIdentifier(src)

        -- Retrieve shared keys based on the configured vehicle SQL table
        local sharedKeys
        if Config.vehicleSQL == 'player_vehicles' then
            local result = ExecuteSql('SELECT shared FROM player_vehicles WHERE plate = ?', { data.plate })
            sharedKeys = result[1] and result[1].shared and json.decode(result[1].shared) or {}
        elseif Config.vehicleSQL == 'owned_vehicles' then
            local result = ExecuteSql('SELECT shared FROM owned_vehicles WHERE plate = ?', { data.plate })
            sharedKeys = result[1] and result[1].shared and json.decode(result[1].shared) or {}
        end

        -- Ensure sharedKeys is a table to prevent errors in the for loop
        if not sharedKeys then sharedKeys = {} end

        -- Iterate through sharedKeys to remove the specified identifier
        for index, sharedKey in ipairs(sharedKeys) do
            if sharedKey.identifier == data.identifier then
                table.remove(sharedKeys, index)
                break
            end
        end

        -- Update the database with the modified shared keys
        if Config.vehicleSQL == 'player_vehicles' then
            ExecuteSql('UPDATE player_vehicles SET shared = ? WHERE plate = ? AND citizenid = ?',
                { json.encode(sharedKeys), data.plate, identifier })
        elseif Config.vehicleSQL == 'owned_vehicles' then
            ExecuteSql('UPDATE owned_vehicles SET shared = ? WHERE plate = ? AND owner = ?',
                { json.encode(sharedKeys), data.plate, identifier })
        end

        cb(sharedKeys)
    end)
    RegisterCallback('tworst-garages:server:GetGarageVehicles', function(source, cb, data)
        local src = source
        local Player = GetPlayer(src)
        if not Player then return end
        local identifier = GetIdentifier(src)
        local vehicleTable
        if data.type == 'depot' then
            if Config.vehicleSQL == 'player_vehicles' then
                vehicleTable = ExecuteSql('SELECT * FROM player_vehicles WHERE citizenid = ?', { identifier })
                -- Ajouter les véhicules avec fausses plaques
                local fakePlateVehicles = ExecuteSql('SELECT * FROM player_vehicles WHERE fakeplate IS NOT NULL AND fakeplate != "" AND citizenid = ?', { identifier })
                for _, vehicle in ipairs(fakePlateVehicles) do
                    -- Créer une copie du véhicule avec la fausse plaque comme plaque principale pour l'affichage
                    local fakeVehicle = {
                        plate = vehicle.fakeplate,
                        vehicle = vehicle.vehicle,
                        vehicleLabel = vehicle.vehicleLabel,
                        garage = vehicle.garage,
                        state = vehicle.state,
                        fuel = vehicle.fuel,
                        engine = vehicle.engine,
                        body = vehicle.body,
                        mods = vehicle.mods,
                        shared = vehicle.shared,
                        depotprice = vehicle.depotprice,
                        citizenid = vehicle.citizenid,
                        originalPlate = vehicle.plate -- Garder la plaque originale en référence
                    }
                    table.insert(vehicleTable, fakeVehicle)
                end
            elseif Config.vehicleSQL == 'owned_vehicles' then
                vehicleTable = ExecuteSql('SELECT * FROM owned_vehicles WHERE owner = ?', { identifier })
            end
        elseif Config.SharedGarages then
            if Config.vehicleSQL == 'player_vehicles' then
                vehicleTable = ExecuteSql('SELECT * FROM player_vehicles WHERE citizenid = ?', { identifier })
                -- Ajouter les véhicules avec fausses plaques
                local fakePlateVehicles = ExecuteSql('SELECT * FROM player_vehicles WHERE fakeplate IS NOT NULL AND fakeplate != "" AND citizenid = ?', { identifier })
                for _, vehicle in ipairs(fakePlateVehicles) do
                    local fakeVehicle = {
                        plate = vehicle.fakeplate,
                        vehicle = vehicle.vehicle,
                        vehicleLabel = vehicle.vehicleLabel,
                        garage = vehicle.garage,
                        state = vehicle.state,
                        fuel = vehicle.fuel,
                        engine = vehicle.engine,
                        body = vehicle.body,
                        mods = vehicle.mods,
                        shared = vehicle.shared,
                        depotprice = vehicle.depotprice,
                        citizenid = vehicle.citizenid,
                        originalPlate = vehicle.plate
                    }
                    table.insert(vehicleTable, fakeVehicle)
                end
                
                local sharedVehicles = ExecuteSql(
                    'SELECT * FROM player_vehicles WHERE shared IS NOT NULL AND shared != ?', { '[]' })

                for _, vehicle in ipairs(sharedVehicles) do
                    local sharedData = json.decode(vehicle.shared)
                    for _, sharedKey in ipairs(sharedData) do
                        if sharedKey.identifier == identifier then
                            local alreadyExists = false
                            for _, v in ipairs(vehicleTable) do
                                if v.plate == vehicle.plate or (v.originalPlate and v.originalPlate == vehicle.plate) then
                                    alreadyExists = true
                                    break
                                end
                            end
                            if not alreadyExists then
                                table.insert(vehicleTable, vehicle)
                            end
                            break
                        end
                    end
                end
            elseif Config.vehicleSQL == 'owned_vehicles' then
                vehicleTable = ExecuteSql('SELECT * FROM owned_vehicles WHERE owner = ?', { identifier })
                local sharedVehicles = ExecuteSql(
                    'SELECT * FROM owned_vehicles WHERE shared IS NOT NULL AND shared != ?', { '[]' })
                for _, vehicle in ipairs(sharedVehicles) do
                    local sharedData = json.decode(vehicle.shared)
                    for _, sharedKey in ipairs(sharedData) do
                        if sharedKey.identifier == identifier then
                            local alreadyExists = false
                            for _, v in ipairs(vehicleTable) do
                                if v.plate == vehicle.plate then
                                    alreadyExists = true
                                    break
                                end
                            end
                            if not alreadyExists then
                                table.insert(vehicleTable, vehicle)
                            end
                            break
                        end
                    end
                end
            end
        else
            if Config.vehicleSQL == 'player_vehicles' then
                local sharedVehicles
                if Config.getAllVehicles then
                    vehicleTable = ExecuteSql('SELECT * FROM player_vehicles WHERE citizenid = ?', { identifier })
                    sharedVehicles = ExecuteSql('SELECT * FROM player_vehicles WHERE shared IS NOT NULL AND shared != ?',{ '[]' })
                else
                    vehicleTable = ExecuteSql('SELECT * FROM player_vehicles WHERE citizenid = ? AND garage = ?', { identifier, data.indexgarage })
                    sharedVehicles = ExecuteSql('SELECT * FROM player_vehicles WHERE shared IS NOT NULL AND shared != ? AND garage = ?',{ '[]', data.indexgarage })
                end

                -- Modifier les plaques pour afficher les fausses plaques si elles existent
                for _, vehicle in ipairs(vehicleTable) do
                    if vehicle.fakeplate and vehicle.fakeplate ~= "" then
                        vehicle.plate = vehicle.fakeplate
                    end
                end

                for _, vehicle in ipairs(sharedVehicles) do
                    local sharedData = json.decode(vehicle.shared)
                    for _, sharedKey in ipairs(sharedData) do
                        if sharedKey.identifier == identifier then
                            local alreadyExists = false
                            for _, v in ipairs(vehicleTable) do
                                if v.plate == vehicle.plate or (vehicle.fakeplate and vehicle.fakeplate ~= "" and v.plate == vehicle.fakeplate) then
                                    alreadyExists = true
                                    break
                                end
                            end
                            if not alreadyExists then
                                -- Modifier la plaque pour afficher la fausse plaque si elle existe
                                if vehicle.fakeplate and vehicle.fakeplate ~= "" then
                                    vehicle.plate = vehicle.fakeplate
                                end
                                table.insert(vehicleTable, vehicle)
                            end
                            break
                        end
                    end
                end
            end
            if Config.vehicleSQL == 'owned_vehicles' then
                local sharedVehicles
                if Config.getAllVehicles then
                    vehicleTable = ExecuteSql('SELECT * FROM owned_vehicles WHERE owner = ?', { identifier })
                    sharedVehicles = ExecuteSql('SELECT * FROM owned_vehicles WHERE shared IS NOT NULL AND shared != ?',{ '[]' })
                else
                    vehicleTable = ExecuteSql('SELECT * FROM owned_vehicles WHERE owner = ? AND parking = ?',
                        { identifier, data.indexgarage })
                    sharedVehicles = ExecuteSql(
                        'SELECT * FROM owned_vehicles WHERE shared IS NOT NULL AND shared != ? AND parking = ?',
                        { '[]', data.indexgarage })
                end

                for _, vehicle in ipairs(sharedVehicles) do
                    local sharedData = json.decode(vehicle.shared)
                    for _, sharedKey in ipairs(sharedData) do
                        if sharedKey.identifier == identifier then
                            local alreadyExists = false
                            for _, v in ipairs(vehicleTable) do
                                if v.plate == vehicle.plate then
                                    alreadyExists = true
                                    break
                                end
                            end
                            if not alreadyExists then
                                table.insert(vehicleTable, vehicle)
                            end
                            break
                        end
                    end
                end
            end
        end
        if vehicleTable == nil then
            return cb({})
        end
        if Config.ClassSystem then
            local filteredVehicles = filterVehiclesByCategory(vehicleTable, data.category)
            cb(filteredVehicles)
        else
            cb(vehicleTable)
        end
    end)
    RegisterCallback('tworst-garages:server:spawnvehicle', function(source, cb, data)
        local src = source
        local Player = GetPlayer(src)
        if not Player then return end
        local identifier = GetIdentifier(src)
        local vehicleData
        local isOwner = false
        local hasSharedKey = false
        local vehProps = {}
        local actualPlate = data.plate
        local displayPlate = data.plate -- Plaque à afficher (fausse ou originale)

        -- Vérifier si c'est une fausse plaque et obtenir la plaque originale
        GetOriginalPlateFromFake(data.plate, function(originalPlate)
            actualPlate = originalPlate
            
            if Config.vehicleSQL == 'player_vehicles' then
                vehicleData = ExecuteSql('SELECT * FROM player_vehicles WHERE plate = ? AND citizenid = ?',
                    { actualPlate, identifier })
            elseif Config.vehicleSQL == 'owned_vehicles' then
                vehicleData = ExecuteSql('SELECT * FROM owned_vehicles WHERE plate = ? AND owner = ?',
                    { actualPlate, identifier })
            end

            if vehicleData and vehicleData[1] then
                isOwner = true
            else
                local sharedKeysQuery
                if Config.vehicleSQL == 'player_vehicles' then
                    sharedKeysQuery = ExecuteSql('SELECT shared FROM player_vehicles WHERE plate = ?', { actualPlate })
                elseif Config.vehicleSQL == 'owned_vehicles' then
                    sharedKeysQuery = ExecuteSql('SELECT shared FROM owned_vehicles WHERE plate = ?', { actualPlate })
                end

                if sharedKeysQuery and sharedKeysQuery[1] and sharedKeysQuery[1].shared then
                    local success, sharedKeysData = pcall(json.decode, sharedKeysQuery[1].shared)
                    if success and sharedKeysData then
                        for _, sharedKey in ipairs(sharedKeysData) do
                            if sharedKey.identifier == identifier then
                                hasSharedKey = true
                                break
                            end
                        end
                    else
                        print('Failed to decode shared keys JSON for plate:', actualPlate)
                    end
                end
            end

            if isOwner or hasSharedKey then
                local result
                if Config.vehicleSQL == 'player_vehicles' then
                    result = ExecuteSql('SELECT mods, fakeplate FROM player_vehicles WHERE plate = ?', { actualPlate })
                    if result and result[1] and result[1].mods then
                        vehProps = json.decode(result[1].mods)
                        -- Si une fausse plaque existe, l'utiliser dans les propriétés
                        if result[1].fakeplate and result[1].fakeplate ~= "" then
                            vehProps.plate = result[1].fakeplate
                            displayPlate = result[1].fakeplate
                        end
                    end
                elseif Config.vehicleSQL == 'owned_vehicles' then
                    result = ExecuteSql('SELECT vehicle FROM owned_vehicles WHERE plate = ?', { actualPlate })
                    if result and result[1] and result[1].vehicle then
                        vehProps = json.decode(result[1].vehicle)
                    end
                end

                local callbackData = {
                    netId = 0,
                    vehProps = vehProps,
                    plate = displayPlate -- Utiliser la plaque à afficher (fausse ou originale)
                }
                cb(callbackData)
            else
                cb(false)
            end
        end)
    end)


    RegisterCallback('tworst-garages:server:getVehicleProperties', function(source, cb, plate)
        local src = source
        local Player = GetPlayer(src)
        if not Player then return end
        
        -- Vérifier si c'est une fausse plaque et obtenir la plaque originale
        GetOriginalPlateFromFake(plate, function(originalPlate)
            local actualPlate = originalPlate
            local vehProps = {}
            
            if Config.vehicleSQL == 'player_vehicles' then
                result = ExecuteSql('SELECT mods FROM player_vehicles WHERE plate = ?', { actualPlate })
                if result and result[1] and result[1].mods then
                    vehProps = json.decode(result[1].mods)
                else
                    vehProps = {}
                end
            elseif Config.vehicleSQL == 'owned_vehicles' then
                result = ExecuteSql('SELECT vehicle FROM owned_vehicles WHERE plate = ?', { actualPlate })
                if result and result[1] and result[1].vehicle then
                    vehProps = json.decode(result[1].vehicle)
                else
                    vehProps = {}
                end
            end

            cb(vehProps)
        end)
    end)
    RegisterCallback('tworst-garages:server:IsSpawnOk', function(source, cb, data)
        -- Vérifier si c'est une fausse plaque et obtenir la plaque originale
        GetOriginalPlateFromFake(data.plate, function(originalPlate)
            local actualPlate = originalPlate
            
            if Config.vehicleSQL == 'player_vehicles' then
                if data.type == 'depot' then
                    if Config.getAllVehicles then
                        local plateVehicle = ExecuteSql('SELECT state, garage FROM player_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].state == 1 or tostring(plateVehicle[1].garage) ~= tostring(data.index) then
                            cb(false)
                            return
                        end
                    else
                        local plateVehicle = ExecuteSql('SELECT state FROM player_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].state == 1 then
                            cb(false)
                            return
                        end
                    end
                else
                    if Config.getAllVehicles then
                        local plateVehicle = ExecuteSql('SELECT state, garage FROM player_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].state == 0 or tostring(plateVehicle[1].garage) ~= tostring(data.index) then
                            cb(false)
                            return
                        end
                    else
                        local plateVehicle = ExecuteSql('SELECT state FROM player_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].state == 0 then
                            cb(false)
                            return
                        end
                    end
                end
            elseif Config.vehicleSQL == 'owned_vehicles' then
                if data.type == 'depot' then
                    if Config.getAllVehicles then
                        local plateVehicle = ExecuteSql('SELECT stored, parking FROM owned_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].stored == 1 or tostring(plateVehicle[1].parking) ~= tostring(data.indexgarage) then
                            cb(false)
                            return
                        end
                    else
                        local plateVehicle = ExecuteSql('SELECT stored FROM owned_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].stored == 1 then
                            cb(false)
                            return
                        end
                    end
                else
                    if Config.getAllVehicles then
                        local plateVehicle = ExecuteSql('SELECT stored, parking FROM owned_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].stored == 0 or tostring(plateVehicle[1].parking) ~= tostring(data.index) then
                            cb(false)
                            return
                        end
                    else
                        local plateVehicle = ExecuteSql('SELECT stored FROM owned_vehicles WHERE plate = ?', { actualPlate })
                        if plateVehicle[1].stored == 0 then
                            cb(false)
                            return
                        end
                    end
                end
            end
            cb(true)
        end)
    end)
end)


RegisterNetEvent('tworst-garages:server:updateVehicleStats', function(plate, fuel, engine, body, vehicleProps)
    local src = source
    local Player = GetPlayer(src)
    if not Player then return end
    local identifier = GetIdentifier(src)
    
    -- Vérifier si c'est une fausse plaque et obtenir la plaque originale
    GetOriginalPlateFromFake(plate, function(originalPlate)
        local actualPlate = originalPlate
        
        if Config.vehicleSQL == 'player_vehicles' then
            ExecuteSql(
                'UPDATE player_vehicles SET fuel = ?, engine = ?, body = ?, mods = ? WHERE plate = ? AND citizenid = ?',
                { fuel, engine, body, json.encode(vehicleProps), actualPlate, identifier })
        end
    end)
end)

RegisterServerEvent('upr_garage:updateOwnedVehicle')
AddEventHandler('upr_garage:updateOwnedVehicle', function(plate, vehProps)
    local src = source
    local Player = GetPlayer(src)
    if not Player then return end
    local identifier = GetIdentifier(src)
    
    -- Vérifier si c'est une fausse plaque et obtenir la plaque originale
    GetOriginalPlateFromFake(plate, function(originalPlate)
        local actualPlate = originalPlate
        
        ExecuteSql('UPDATE owned_vehicles SET vehicle = ? WHERE plate = ? AND owner = ?',
            { json.encode(vehProps), actualPlate, identifier })
    end)
end)

RegisterServerEvent('tworst-garages:server:updateVehicleState', function(state, plate)
    local src = source
    local Player = GetPlayer(src)
    if not Player then return end
    
    -- Vérifier si c'est une fausse plaque et obtenir la plaque originale
    GetOriginalPlateFromFake(plate, function(originalPlate)
        local actualPlate = originalPlate
        
        if Config.vehicleSQL == 'player_vehicles' then
            ExecuteSql('UPDATE player_vehicles SET state = ?, depotprice = ? WHERE plate = ?',
                { state, 0, actualPlate })
        else
            ExecuteSql('UPDATE owned_vehicles SET stored = ?, depotprice = ? WHERE plate = ?',
                { state, 0, actualPlate })
        end
    end)
end)



--RegisterNetEvent('tworst-garages:server:trackVehicle', function(plate)
--    local src = source
--   -- local vehicleData = OutsideVehicles[plate]
--    if vehicleData and DoesEntityExist(vehicleData.entity) then
--        TriggerClientEvent('tworst-garages:client:trackVehicle', src, GetEntityCoords(vehicleData.entity))
--        TriggerClientEvent('tworst-garage:client:sendNotification', src, 'Vehicle Marked on the map', 'succesNotify')
--    else
--        TriggerClientEvent('tworst-garage:client:sendNotification', src, 'No vehicle found!', 'errorNotify')
--    end
--end)

RegisterNetEvent('tworst-garages:server:PayDepotPrice', function(data)
    local src = source
    local Player = GetPlayer(src)
    
    -- Vérifier si c'est une fausse plaque et obtenir la plaque originale
    GetOriginalPlateFromFake(data.plate, function(originalPlate)
        local actualPlate = originalPlate
        
        if Config.vehicleSQL == 'player_vehicles' then
            local result = ExecuteSql('SELECT depotprice FROM player_vehicles WHERE plate = ?', { actualPlate })

            if result and result[1] then
                local depotPrice = tonumber(result[1].depotprice)
                    local cashBalance = GetPlayerMoney(src, 'cash')
                    local bankBalance = GetPlayerMoney(src, 'bank')

                    if cashBalance >= depotPrice then
                        RemoveMoney(src, 'cash', depotPrice, 'paid-depot')
                        TriggerClientEvent('tworst-garages:client:takeOutGarage', src, data)
                    elseif bankBalance >= depotPrice then
                        RemoveMoney(src, 'bank', depotPrice, 'paid-depot')
                        TriggerClientEvent('tworst-garages:client:takeOutGarage', src, data)
                    else
                        TriggerClientEvent("tworst-garage:client:sendNotification", src,
                            Config.NotificationText['notenoughmoney'].text,
                            Config.NotificationText['notenoughmoney'].type)
                    end
                else
                    TriggerClientEvent('tworst-garage:client:sendNotification', src,
                        Config.NotificationText['notbepared'].text,
                        Config.NotificationText['notbepared'].type)
                end
        elseif Config.vehicleSQL == 'owned_vehicles' then
            local result = ExecuteSql('SELECT depotprice FROM owned_vehicles WHERE plate = ?', { actualPlate })
            if result and result[1] then
                local depotPrice = result[1].depotprice
                local cashBalance = GetPlayerMoney(src, 'cash')
                local bankBalance = GetPlayerMoney(src, 'bank')

                if cashBalance >= depotPrice then
                    RemoveMoney(src, 'cash', depotPrice, 'paid-depot')
                    TriggerClientEvent('tworst-garages:client:takeOutGarage', src, data)
                elseif bankBalance >= depotPrice then
                    RemoveMoney(src, 'bank', depotPrice, 'paid-depot')
                    TriggerClientEvent('tworst-garages:client:takeOutGarage', src, data)
                else
                    TriggerClientEvent("tworst-garage:client:sendNotification", src,
                        Config.NotificationText['notenoughmoney'].text,
                        Config.NotificationText['notenoughmoney'].type)
                end
            else
                TriggerClientEvent('tworst-garage:client:sendNotification', src,
                    Config.NotificationText['notbepared'].text,
                    Config.NotificationText['notbepared'].type)
            end
        end
    end)
end)

local vehicleTypes = { -- https://docs.fivem.net/natives/?_0xA273060E
    motorcycles = 'bike',
    boats = 'boat',
    helicopters = 'heli',
    planes = 'plane',
    submarines = 'submarine',
    trailer = 'trailer',
    train = 'train'
}

function GetVehicleTypeByModel(model)
    local vehicleData = vehiclesData[model]
    if not vehicleData then return 'automobile' end
    local category = vehicleData.category
    local vehicleType = vehicleTypes[category]
    return vehicleType or 'automobile'
end

RegisterNetEvent('tworst-garage:server:takeoutVehicle', function(plate)
    if Config.vehicleSQL == 'player_vehicles' then
        ExecuteSql('UPDATE player_vehicles SET state = 0 WHERE plate = ?', { plate })
    else
        ExecuteSql('UPDATE owned_vehicles SET stored = 0 WHERE plate = ?', { plate })
    end
end)
