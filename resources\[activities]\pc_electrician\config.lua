Config = {}

-- Configuration générale
Config.Debug = false
Config.MinimumPlayers = 1 -- Nombre minimum de joueurs pour commencer l'activité
Config.MaximumPlayers = 4 -- Nombre maximum de joueurs par groupe

-- Configuration des multiplicateurs de groupe
Config.GroupMultipliers = {
    [1] = 1.0,    -- 1 joueur = récompense normale
    [2] = 1.1,    -- 2 joueurs = 10% de bonus
    [3] = 1.2,    -- 3 joueurs = 20% de bonus
    [4] = 1.25     -- 4 joueurs = 25% de bonus
}

-- Configuration du ped et du point de départ
Config.StartPoint = {
    ped = {
        model = "s_m_y_construct_01",
        coords = vec4(745.36, -1947.26, 29.19, 82.11),
    },
    blip = {
        sprite = 354,
        color = 5,
        scale = 0.7,
        label = "Électricien"
    }
}

-- Configuration des véhicules
Config.Vehicles = {
    {
        model = "speedo",
        coords = vec4(742.35, -1954.11, 29.29, 265.52)
    }
}

-- Configuration des jobs autorisés
Config.AllowedJobs = {} -- Table vide pour permettre à tous les joueurs d'accéder à l'activité

-- Configuration des différentes runs possibles
Config.Runs = {
    {
        name = "Réparation Standard",
        chance = 100,
        repairPoints = {
            {
                label = "Panne électrique 1",
                coords = vector3(745.88433837891, -1948.3657226562, 29.619190216064)
            },
        },
        rewards = {
            complete = {
                money = 2000,
            }
        }
    },
    -- {
    --     name = "Réparation Urgente",
    --     chance = 0,
    --     repairPoints = {
    --         {
    --             label = "Panne électrique urgente",
    --             coords = vector3(-350.90905761719, -1664.2722167969, 17.744537353516)
    --         },
    --         {
    --             label = "Panne électrique urgente",
    --             coords = vector3(-374.33047485352, -1538.2844238281, 27.139883041382)
    --         },
    --     },
    --     rewards = {
    --         complete = {
    --             money = 3500,
    --         }
    --     }
    -- },
} 