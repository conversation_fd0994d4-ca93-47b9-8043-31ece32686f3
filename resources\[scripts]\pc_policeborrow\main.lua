local QBCore = exports['qb-core']:GetCoreObject()
local lastUse = 0

Citizen.CreateThread(function()
    exports.ox_target:addGlobalVehicle({
        {
            name = 'borrow_police_vehicle',
            icon = "fas fa-car",
            label = "Réquisitionner le véhicule",
            onSelect = function(data)
                local entity = data.entity
                local playerPed = PlayerPedId()
                local veh = entity
                local driver = GetPedInVehicleSeat(veh, -1)

                if GetGameTimer() - lastUse < Config.Cooldown * 1000 then
                    QBCore.Functions.Notify("Vous devez attendre avant de réquisitionner un nouveau véhicule", "error")
                    return
                end

                lastUse = GetGameTimer()
                TriggerEvent('pc_policebadge:client:showBadge')
                Wait(3000)
                SetBlockingOfNonTemporaryEvents(driver, true)
                SetPedFleeAttributes(driver, 0, false)
                SetPedCanRagdoll(driver, false)
                SetPedKeepTask(driver, true)
                SetEntityAsMissionEntity(driver, true, true)
                TaskLeaveVehicle(driver, veh, 0)
                local exitTimeout = 5000
                while IsPedInAnyVehicle(driver, false) and exitTimeout > 0 do
                    Wait(100)
                    exitTimeout = exitTimeout - 100
                end
                ClearPedTasksImmediately(driver)
                DetachEntity(driver, true, true)
                ClearPedTasksImmediately(driver)
                RequestAnimDict("anim@mp_player_intincarsalutestd@ds@")
                while not HasAnimDictLoaded("anim@mp_player_intincarsalutestd@ds@") do 
                    Wait(10) 
                end
                TaskPlayAnim(driver, "anim@mp_player_intincarsalutestd@ds@", "idle_a", 8.0, -8.0, 1500, 49, 0, false, false, false)
                Wait(1600)
                ClearPedTasks(driver)
                local approachTimeout = 10000
                while approachTimeout > 0 and #(GetEntityCoords(driver) - GetEntityCoords(playerPed)) > 1.5 do
                    TaskGoToEntity(driver, playerPed, -1, 1.2, 2.0, 0.0, 0)
                    Wait(500)
                    approachTimeout = approachTimeout - 500
                end
                TaskTurnPedToFaceEntity(driver, playerPed, 1500)
                Wait(1500)
                RequestAnimDict("mp_common")
                while not HasAnimDictLoaded("mp_common") do 
                    Wait(10) 
                end
                TaskPlayAnim(playerPed, "mp_common", "givetake1_a", 8.0, -8.0, 2000, 49, 0, false, false, false)
                TaskPlayAnim(driver, "mp_common", "givetake1_b", 8.0, -8.0, 2000, 49, 0, false, false, false)
                Wait(2000)
                TriggerEvent("vehiclekeys:client:SetOwner", GetVehicleNumberPlateText(veh))
                SetVehicleDoorsLocked(veh, false) 
                SetVehicleNeedsToBeHotwired(veh, false)
                ClearPedTasks(driver)
                SetBlockingOfNonTemporaryEvents(driver, false)
                SetPedAsNoLongerNeeded(driver)
                TaskWanderStandard(driver, 10.0, 10)
                ClearPedTasks(playerPed)
            end,
            canInteract = function(entity, distance, coords, name, bone)
                local job = QBCore.Functions.GetPlayerData().job
                if not (job and job.name == "police") then
                    return false
                end
                local driver = GetPedInVehicleSeat(entity, -1)
                if not DoesEntityExist(driver) or IsPedAPlayer(driver) then
                    return false
                end
                local hasItem = exports.ox_inventory:Search('count', Config.PoliceBadgeItem) > 0
                if not hasItem then
                    return false
                end
                return true
            end,
        }
    },
    {
        distance = 2.5
    })
end)