<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <link href="style.css" rel="stylesheet">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
        <title>SY Lockpick</title>
    </head>
    <body>
        <div id="wrap">
            <img src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/95637/collar.png" alt="" id="collar" />
            <div id="cylinder"></div>
            <div id="driver"></div>
            <div id="pin">
                <div class="top"></div>
                <div class="bott"></div>
            </div>
        </div>
        <div id="keypad"></div>
        <div id="padlock">
            <div class="sitelocker">
                <div class="container">
                  <div class="lock">
                    <!--<div class="shackle">
                      <div class="top">
                        <div class="inner"></div>
                      </div>
                      <div class="left">
                        <div class="dentL"></div>
                        <div class="dentR"></div>
                      </div>
                      <div class="right"></div>
                    </div> -->
                    <div class="arrow"></div>
                  </div>
                  <div class="dial">
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                    <div class="tick"></div>
                  </div>
                </div>
                <div class="combo">
              <span class="combonum num1"></span>
              <span class="combonum num2"></span>
              <span class="combonum num3"></span>
              <span class="combonum num4"></span>
              <span class="combonum num5"></span>
            </div>
        </div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/2.1.3/TweenMax.min.js"></script>
		    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/1.11.5/utils/Draggable.min.js"></script>
        <script src="script.js" type="text/javascript"></script>
    </body>
</html>