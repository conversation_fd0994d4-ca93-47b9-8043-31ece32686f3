body {
    background-color: transparent;
    font-family: Arial, Helvetica, sans-serif;
    overflow: hidden;
}

.container {
    background-image: url("images/badge.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: -600px;
    right: 10px;
    width: 392px;
    height: 583px;
    transition: top 0.5s linear;
}

.sub-container {
    width: 330px;
    height: 200px;
    background-color: rgb(38, 42, 78);
    border-radius: 10px;
    left: 197px;
    transform: translate(-50%, -50%);
}

.sub-container:nth-child(1) {
    position: relative;
    top: 135px;
}

.sub-container-background {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url("https://static.wikia.nocookie.net/gtawiki/images/2/21/Lossantos_seal.png");
    background-repeat: no-repeat;
    background-size: 50%;
    background-position: 50% 50%;
    filter: opacity(0.25);
}

.sub-container-header {
    position: relative;
    text-align: left;
    top: -53px;
    left: 116px;
    width: 100%;
    
    
}
.info {
    top: -5px;
    left: 172px;
    position: relative;
    transform: translate(-50%, -50%);
}
.photo-id {
    width: 100px;
    height: 140px;
    border: 2px solid black;
    position: relative;
    text-align: center;
    top: 100px;
    left: 60px;
    transform: translate(-50%, -50%);
    background-image: url("https://cdn.koreatraveleasy.com/wp-content/uploads/2020/04/24162755/time-on-me-studio-customer-photo3-442x590.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.fields {
    position: relative;
    text-align: center;
    top: -14px;
    left: 228px;
    width: 228px;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: row;
}

.fields-start {
    display: flex;
    flex-direction: column;
    width: 115px;
}

.field {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid white;
}


.field p {
    font-size: 8pt;
    margin: 4px;
    padding: 0;
}

/*FOOTER*/

.footer {
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.footer h3 {
    font-size: 12pt;
    margin: 0;
    padding: 0;
}

.sub-container:nth-child(2) {
    position: relative;
    top: 242px;
}

/*UPDATE INFO*/
#info-form-edit {
    background-color: rgb(44, 46, 60);
    border: none;
    padding: 5px;
    font-size: 8pt;
    border-radius: 5px;
    width: 80px;
    height: 30px;
    cursor: pointer;
    color: white;
}
.info-form {
    display: none;
    flex-direction: column;
}
.info-form div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 2px;
    margin-left: 10px;
    margin-right: 10px;
}

.info-form input, .info-form select {
    width: 173px;
    background-color: rgb(44, 46, 60);
    border: none;
    border-radius: 5px;
    color: white;
}

.info-form select {
    width: 178px;
}

.info-form div button {
    background-color: rgb(44, 46, 60);
    border: none;
    color: white;
    padding: 5px;
    font-size: 8pt;
    border-radius: 5px;
    width: 80px;
    height: 30px;
    cursor: pointer;
    text-align: center;
    margin: auto;
    margin-top: 2px;
}