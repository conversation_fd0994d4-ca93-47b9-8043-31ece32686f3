-- Assure l'accès à ox_lib pour les notifications
local lib = exports.ox_lib
local QBCore = exports['qb-core']:GetCoreObject()

local vangelicoState = nil
local vitrines = {}
local pendingHack = false
local isResettingVitrines = false
local justReset = false

-- Thread de gestion visuelle des vitrines dans la zone Vangelico
local insideVangelico = false

local function isPlayerInVangelico()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local center = vector3(-626.83, -235.35, 38.05)
    local dist = #(playerCoords - center)
    return dist < 200.0 -- élargir pour test
end

-- Synchronisation de l'état depuis le serveur
RegisterNetEvent('pc_robbery:syncVangelico', function(state)
    isResettingVitrines = true
    justReset = true
    vangelicoState = state
    for i in pairs(Config.Vangelico.vitrines) do
        vitrines[i] = vitrines[i] or {}
        vitrines[i].isOpened = state.vitrines[i].isOpened
        vitrines[i].isBusy = state.vitrines[i].isBusy
        vitrines[i].locked = state.vitrines[i].locked -- Synchronisation du lock dynamique
    end
    isResettingVitrines = false
    SetTimeout(2000, function() justReset = false end)
end)

-- Demande de synchronisation à la connexion
CreateThread(function()
    Wait(3000) -- Délai plus long pour laisser le serveur et le joueur se charger
    if justReset then
        return
    end
    TriggerServerEvent('pc_robbery:requestVangelicoSync')
    -- Boucle de retry tant que l'état n'est pas reçu
    while vangelicoState == nil do
        Wait(2000)
        if justReset then
            return
        end
        TriggerServerEvent('pc_robbery:requestVangelicoSync')
    end
end)

-- Création unique des ox_target au démarrage avec exports
CreateThread(function()
    exports.ox_target:addBoxZone({
        coords = Config.Vangelico.hackCoords,
        size = vec3(1, 1, 2),
        rotation = 0,
        debug = false,
        options = {
            {
                name = 'vangelico_hack',
                icon = 'fas fa-laptop',
                label = 'Connexion au système',
                distance = 1.2,
                onSelect = function()
                    if vangelicoState then
                        local now = GetCloudTimeAsInt()
                        local vangelicoRemaining = vangelicoState.cooldown and vangelicoState.cooldownEnd and (vangelicoState.cooldownEnd - now) or 0
                        local globalRemaining = vangelicoState.globalCooldownEnd and (vangelicoState.globalCooldownEnd - now) or 0
                        local remaining = math.max(vangelicoRemaining, globalRemaining, 0)
                        if vangelicoRemaining > 0 or globalRemaining > 0 then
                            local minutes = math.floor(remaining / 60)
                            local seconds = remaining % 60
                            local timeStr = (minutes > 0)
                                and (('%dm %02ds'):format(minutes, seconds))
                                or (('%ds'):format(seconds))
                            exports.ox_lib:notify({
                                title = 'Vangelico',
                                description = ('Vous devez attendre %s avant de pouvoir commencer le braquage.'):format(timeStr),
                                type = 'info'
                            })
                            return
                        elseif vangelicoState.isHacked then
                            -- Hack déjà fait, mais pas encore de cooldown
                            exports.ox_lib:notify({
                                title = 'Vangelico',
                                description = "Le système de sécurité est déjà désactivé. Attendez la fin du braquage pour recommencer.",
                                type = 'info'
                            })
                            return
                        end
                    end
                    HackSecuritySystem()
                end,
                canInteract = function()
                    return true
                end
            }
        }
    })

    -- Target Police pour déverrouiller la porte principale
    exports.ox_target:addBoxZone({
        coords = Config.Vangelico.mainDoorPoliceUnlock,
        size = vec3(1, 1, 2),
        rotation = 0,
        debug = false,
        options = {
            {
                name = 'vangelico_police_unlock',
                icon = 'fas fa-unlock',
                label = 'Déverrouiller la porte (Police)',
                distance = 1.5,
                groups = {'police'},
                onSelect = function()
                    TriggerServerEvent('pc_robbery:policeUnlockVangelicoDoor')
                end,
                -- canInteract = function()
                --     return vangelicoState and vangelicoState.cooldown
                -- end
            }
        }
    })

    for i, vitrine in ipairs(Config.Vangelico.vitrines) do
        exports.ox_target:addBoxZone({
            coords = vitrine.coords,
            size = vec3(1, 1, 2),
            rotation = vitrine.heading,
            debug = false,
            options = {
                {
                    name = 'vangelico_vitrine_' .. i,
                    icon = 'fas fa-gem',
                    label = 'Casser la vitrine',
                    distance = 1.2,
                    onSelect = function()
                        BreakVitrine(i)
                    end,
                    canInteract = function()
                        local state = vangelicoState and vangelicoState.vitrines and vangelicoState.vitrines[i]
                        local can = vangelicoState and vangelicoState.isActive and vangelicoState.isHacked
                            and state and not state.isOpened and not state.isBusy and not state.locked
                        return can
                    end
                }
            }
        })
    end
end)

function StartRobbery()
    if vangelicoState and (vangelicoState.isActive or vangelicoState.cooldown) then
        exports.ox_lib:notify({ title = 'Erreur', description = 'Un braquage est déjà en cours ou en cooldown !', type = 'error' })
        return
    end
    TriggerServerEvent('pc_robbery:startVangelico')
end

function EndRobbery()
    TriggerServerEvent('pc_robbery:endVangelico')
end

function HackSecuritySystem()
    if pendingHack then
        return
    end
    local item = Config.Vangelico.requiredItem
    local count = exports.ox_inventory:Search('count', item)
    if type(count) ~= 'number' then
        exports.ox_lib:notify({ title = 'Erreur', description = 'Erreur interne : inventaire non lisible.', type = 'error' })
        return
    end
    local hasItem = count > 0
    if not hasItem then
        exports.ox_lib:notify({ title = 'Erreur', description = ('Vous n\'avez pas l\'objet requis : %s !'):format(item), type = 'error' })
        return
    end
    pendingHack = true
    if not vangelicoState then
        exports.ox_lib:notify({ title = 'Erreur', description = 'Synchronisation en cours, réessayez dans une seconde.', type = 'error' })
        pendingHack = false
        return
    end
    if vangelicoState.cooldown or vangelicoState.isHacked then
        exports.ox_lib:notify({ title = 'Erreur', description = "Le hack n'est pas possible maintenant.", type = 'error' })
        pendingHack = false
        return
    end
    TriggerServerEvent('pc_robbery:startVangelico')
end

function StartHackMinigame()
    if not pendingHack then
        return
    end
    -- La vérification de l'item a été déplacée dans HackSecuritySystem, donc inutile ici
    exports.ox_lib:notify({ title = 'Vangelico', description = 'Hack du système de sécurité en cours...', type = 'info' })
    local ped = PlayerPedId()
    local animDict = "anim@scripted@player@mission@tunf_bunk_ig3_nas_upload@"
    local animName = "shootout_typing"
    RequestAnimDict(animDict)
    local tries = 0
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
        tries = tries + 1
        if tries > 100 then
            break
        end
    end
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, -1, 1, 0, false, false, false)

    -- 1. Progressbar d'abord
    local finished = exports.ox_lib:progressBar({
        duration = 7000,
        label = 'Connexion au système de sécurité...',
        useWhileDead = false,
        canCancel = true,
        disable = { move = true, car = true, combat = true },
        anim = nil
    })
    if not finished then
        exports.ox_lib:notify({ title = 'Hack annulé', description = 'Vous avez annulé le hack.', type = 'error' })
        TriggerServerEvent('pc_robbery:failHackVangelico')
        pendingHack = false
        ClearPedTasks(ped)
        return
    end

    -- 2. Puis minijeu
    local success = exports['glitch-minigames']:StartCircuitBreaker(4, 0)
    ClearPedTasks(ped) -- <-- Ici, après le minijeu

    if success then
        local cooldown = Config.Vangelico.mainDoorCooldown or 30
        local minutes = math.floor(cooldown / 60)
        local seconds = cooldown % 60
        local timeStr = (minutes > 0)
            and (('%dm %02ds'):format(minutes, seconds))
            or (('%ds'):format(seconds))
        exports.ox_lib:notify({
            title = 'Avertissement',
            description = ('Intrusion détectée, verrouillage de la porte principale pendant %s.'):format(timeStr),
            type = 'warning',
            duration = 10000,
        })
        exports['ps-dispatch']:VangelicoRobbery()
        TriggerServerEvent('pc_robbery:successHackVangelico')
        -- EnableVitrineTargets()
    else
        exports.ox_lib:notify({ title = 'Échec', description = 'Hack échoué !', type = 'error' })
        TriggerServerEvent('pc_robbery:failHackVangelico')
    end
    pendingHack = false
end

function BreakVitrine(vitrineId)
    if isResettingVitrines then
        exports.ox_lib:notify({ title = 'Erreur', description = 'Veuillez patienter, les vitrines sont en cours de réinitialisation.', type = 'error' })
        return
    end
    -- Sécurité : vérifier que vitrineId est bien un nombre
    if type(vitrineId) ~= 'number' then
        exports.ox_lib:notify({ title = 'Erreur', description = 'Erreur interne : identifiant de vitrine invalide.', type = 'error' })
        return
    end
    -- Vérification de l'arme crowbar en main
    local ped = PlayerPedId()
    local weaponHash = GetSelectedPedWeapon(ped)
    if weaponHash ~= GetHashKey('WEAPON_CROWBAR') then
        exports.ox_lib:notify({ title = 'Erreur', description = "Vous devez avoir une crowbar en main pour casser la vitrine !", type = 'error' })
        return
    end
    -- On ne fait plus de check inventaire côté client, on laisse le serveur gérer
    local vitrineState = vangelicoState and vangelicoState.vitrines and vangelicoState.vitrines[vitrineId]
    if vitrineState and vitrineState.locked then
        exports.ox_lib:notify({ title = 'Vitrine', description = 'Cette vitrine est déjà en train d\'être cassée par un autre joueur.', type = 'info' })
        return
    end
    if not vangelicoState or not vangelicoState.isActive or not vangelicoState.isHacked then
        exports.ox_lib:notify({ title = 'Erreur', description = 'Vous ne pouvez pas casser la vitrine maintenant.', type = 'error' })
        return
    end
    local ped = PlayerPedId()
    local animDictCabinet = 'missheist_jewel'
    local animNameSmashFront = {
        'smash_case_tray_b',
        'smash_case_necklace_skull'
    }
    local animNameSmashTop = {
        'smash_case_tray_a',
        'smash_case_d',
        'smash_case_e'
    }
    local vitrine = Config.Vangelico.vitrines[vitrineId]
    if type(vitrine) ~= 'table' then
        exports.ox_lib:notify({ title = 'Erreur', description = 'Erreur interne : vitrine introuvable.', type = 'error' })
        return
    end
    local animName
    local playerCoords = GetEntityCoords(ped)

    -- Charger l'animDict
    RequestAnimDict(animDictCabinet)
    local tries = 0
    while not HasAnimDictLoaded(animDictCabinet) do
        Wait(10)
        tries = tries + 1
        if tries > 100 then break end
    end

    -- Choix de l'animation selon la vitrine
    if vitrine.rayFire == 'DES_Jewel_Cab4' then
        animName = animNameSmashFront[math.random(1, #animNameSmashFront)]
        TaskPlayAnim(ped, animDictCabinet, animName, 3.0, 3.0, -1, 2, 0, false, false, false)
        Wait(150)
        if vitrine.rayFire then
            local rayFire = GetRayfireMapObject(vitrine.coords.x, vitrine.coords.y, vitrine.coords.z, 1.4, vitrine.rayFire)
            SetStateOfRayfireMapObject(rayFire, 9)
        end
    elseif vitrine.rayFire then
        animName = animNameSmashTop[math.random(1, #animNameSmashTop)]
        TaskPlayAnim(ped, animDictCabinet, animName, 3.0, 3.0, -1, 2, 0, false, false, false)
        Wait(300)
        local rayFire = GetRayfireMapObject(vitrine.coords.x, vitrine.coords.y, vitrine.coords.z, 1.4, vitrine.rayFire)
        SetStateOfRayfireMapObject(rayFire, 9)
    else
        animName = animNameSmashTop[math.random(1, #animNameSmashTop)]
        TaskPlayAnim(ped, animDictCabinet, animName, 3.0, 3.0, -1, 2, 0, false, false, false)
        Wait(300)
    end
    RemoveAnimDict(animDictCabinet)

    -- Particules et son
    RequestNamedPtfxAsset('scr_jewelheist')
    while not HasNamedPtfxAssetLoaded('scr_jewelheist') do Wait(10) end
    UseParticleFxAssetNextCall('scr_jewelheist')
    StartNetworkedParticleFxNonLoopedOnEntity('scr_jewel_cab_smash', GetCurrentPedWeaponEntityIndex(ped), 0, 0, 0, 0, 0, 0, 1.6, false, false, false)
    -- Son (optionnel, à adapter selon vos fonctions)
    -- TriggerEvent('playSmashAudio', playerCoords) -- À remplacer par votre fonction de son si besoin

    Wait(GetAnimDuration(animDictCabinet, animName) * 850)
    ClearPedTasks(ped)
    RemoveNamedPtfxAsset('scr_jewelheist')

    TriggerServerEvent('pc_robbery:breakVitrine', vitrineId)
end

-- Thread de gestion visuelle des vitrines dans la zone Vangelico
CreateThread(function()
    while true do
        Wait(1000)
        if isPlayerInVangelico() then
            if not insideVangelico then
                insideVangelico = true
                -- Correction : reset visuel à l'entrée
                if not vangelicoState or not vangelicoState.vitrines then
                    while not vangelicoState or not vangelicoState.vitrines do
                        Wait(100)
                    end
                    for i, vitrine in ipairs(Config.Vangelico.vitrines) do
                        if vitrine.rayFire then
                            local rayFire = GetRayfireMapObject(vitrine.coords.x, vitrine.coords.y, vitrine.coords.z, 1.4, vitrine.rayFire)
                            if vangelicoState.vitrines[i] and vangelicoState.vitrines[i].isOpened then
                                SetStateOfRayfireMapObject(rayFire, 9)
                            else
                                SetStateOfRayfireMapObject(rayFire, 1)
                                Wait(100)
                                SetStateOfRayfireMapObject(rayFire, 6)
                            end
                        end
                    end
                end
            end
            CreateThread(function()
                while insideVangelico do
                    if vangelicoState and vangelicoState.vitrines then
                        for i, vitrine in ipairs(Config.Vangelico.vitrines) do
                            if vangelicoState.vitrines[i] and vangelicoState.vitrines[i].isOpened and vitrine.rayFire then
                                local rayFire = GetRayfireMapObject(vitrine.coords.x, vitrine.coords.y, vitrine.coords.z, 1.4, vitrine.rayFire)
                                SetStateOfRayfireMapObject(rayFire, 9)
                            end
                        end
                    end
                    Wait(6000)
                end
            end)
            while isPlayerInVangelico() do Wait(1000) end
            insideVangelico = false
        end
    end
end)

RegisterCommand('startvangelico', function()
    StartRobbery()
end, false)

RegisterCommand('endvangelico', function()
    EndRobbery()
end, false)

RegisterNetEvent('pc_robbery:startHackMinigame', StartHackMinigame)
RegisterNetEvent('pc_robbery:failStartHack', function()
    pendingHack = false
end)
