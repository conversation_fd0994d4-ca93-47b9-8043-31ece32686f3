-- Chargement sécurisé de QBCore
local QBCore = nil
CreateThread(function()
    while QBCore == nil do
        QBCore = exports['qb-core']:GetCoreObject()
        if QBCore then
            break
        else
            Wait(1000)
        end
    end
end)

-- Tables pour gérer les états
local registerLocks = {} -- Verrous des caisses
local safeLocks = {} -- Verrous des coffres
local registerCooldowns = {} -- Cooldowns des caisses par magasin et par caisse
local safeCooldowns = {} -- Cooldowns des coffres par magasin
local globalCooldown = false -- Cooldown global
local globalCooldownStore = nil -- Magasin en cooldown global

-- Variables globales côté serveur pour l'état de Paleto Bank
local paletoServerState = {
    wiresCut = false,
    c4DoorExists = true,
    mainDoorOpen = false,
    thermiteUsed = false, -- Thermite déjà utilisée
    c4Used = false, -- C4 déjà utilisé
    vaultsDrilled = {} -- Coffres percés {[1] = true, [2] = true, etc.}
}

-- Table pour tracker les coffres en cours de perçage (vaultIndex = playerId)
local paletoVaultLocks = {}
-- Table pour tracker les coffres déjà percés (vaultIndex = true)
local paletoVaultDrilled = {}

-- Variables de cooldown pour Paleto Bank
local paletoCooldownActive = false
local paletoCooldownTimer = nil
local paletoCooldownStartTime = 0
local globalCooldownActive = false
local globalCooldownTimer = nil
local globalCooldownStartTime = 0
local cooldownTimers = {} -- Timers pour le compte à rebours
local bankCooldowns = {} -- Cooldowns des banques par ID
local bankCooldownTimers = {} -- Timers pour les cooldowns des banques
local bankCooldown = false -- Cooldown de la banque
local bankCooldownTimer = nil -- Timer pour le cooldown de la banque
local vaultLocks = {} -- Verrous pour les coffres de banque (format: "bankId_vaultIndex" = playerId)

-- Variables pour la synchronisation des états
local globalBankStates = {
    hasThreatenedGuard = {},  -- Par bankId
    hasInsertedCard = {},     -- Par bankId
    hasUsedThermite = {},     -- Par bankId
    drilledVaults = {},       -- Par bankId puis vaultIndex
    doorStates = {}           -- Par bankId (true = ouverte, false = fermée)
}

-- Ajout : Verrous pour les vitrines Vangelico
local vitrineLocks = {} -- [vitrineId] = playerId

-- Fonction pour trouver la porte ox_doorlock d'une banque
local function findBankDoor(bankId, bankConfig)
    local door = nil

    -- Méthode 1: ID de porte spécifique
    if bankConfig.doorId then
        door = exports.ox_doorlock:getDoor(bankConfig.doorId)
        if door then
            return door
        else
            return nil
        end
    end

    -- Méthode 2: Nom de porte spécifique
    if bankConfig.doorName then
        door = exports.ox_doorlock:getDoorFromName(bankConfig.doorName)
        if door then
            return door
        else
            return nil
        end
    end

    -- Méthode 3: Recherche par coordonnées (méthode de fallback)
    local doorCoords = bankConfig.vaultDoorCoords
    if doorCoords then
        local allDoors = exports.ox_doorlock:getAllDoors()
        local closestDoor = nil
        local closestDistance = math.huge

        for _, doorData in pairs(allDoors) do
            if doorData.coords then
                local distance = #(vector3(doorCoords.x, doorCoords.y, doorCoords.z) - vector3(doorData.coords.x, doorData.coords.y, doorData.coords.z))
                if distance < closestDistance and distance < 5.0 then
                    closestDistance = distance
                    closestDoor = doorData
                end
            end
        end

        if closestDoor then
            return closestDoor
        else
            return nil
        end
    end

    return nil
end

-- Fonction pour réinitialiser toutes les portes de banque
local function resetAllBankDoors()
    for bankId, bankConfig in pairs(Config.Banks) do
        -- Fermer et barrer la porte physiquement
        TriggerClientEvent('asc_robbery:closeBankDoor', -1, nil, bankId)

        -- Trouver et barrer la porte principale via ox_doorlock
        local door = findBankDoor(bankId, bankConfig)
        if door then
            local success = exports.ox_doorlock:setDoorState(door.id, 1) -- 1 = barré
            if success then
        -- Barrer les portes secondaires si configurées
        if bankConfig.secondaryDoors then
            for _, secondaryDoorId in pairs(bankConfig.secondaryDoors) do
                local secondaryDoor = exports.ox_doorlock:getDoor(secondaryDoorId)
                if secondaryDoor then
                    local success = exports.ox_doorlock:setDoorState(secondaryDoorId, 1) -- 1 = barré
                    if success then
                                -- Barrer les portes secondaires si configurées
                    else
                                -- Barrer les portes secondaires si configurées
                    end
                else
                            -- Barrer les portes secondaires si configurées
                end
            end
        end
            else
                -- Barrer les portes secondaires si configurées
            end
        else
            -- Barrer les portes secondaires si configurées
        end
    end
end

-- Fonction pour obtenir le nombre de policiers
local function getPoliceCount()
    if not QBCore then
        return 0
    end

    local players = QBCore.Functions.GetQBPlayers()
    local count = 0
    for _, player in pairs(players) do
        print('[DEBUG][POLICE] job:', player.PlayerData.job.name, 'onduty:', player.PlayerData.job.onduty)
        if player.PlayerData.job.name == "police" and player.PlayerData.job.onduty then
            count = count + 1
        end
    end
    print('[DEBUG][POLICE] Total policiers en service:', count)
    return count
end

-- Fonction pour calculer le temps restant d'un cooldown
local function getRemainingCooldownTime(cooldownType)
    if cooldownType == 'paleto' and paletoCooldownActive then
        local elapsed = (GetGameTimer() - paletoCooldownStartTime) / 1000
        local remaining = Config.Paletobank.cooldown - elapsed
        return math.max(0, math.ceil(remaining))
    elseif cooldownType == 'global' and globalCooldown and cooldownTimers.global then
        local remaining = (cooldownTimers.global - GetGameTimer()) / 1000
        return math.max(0, math.ceil(remaining))
    end
    return 0
end

-- Fonction pour formater le temps en minutes et secondes
local function formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    if minutes > 0 then
        return minutes .. 'm ' .. remainingSeconds .. 's'
    else
        return remainingSeconds .. 's'
    end
end

-- Fonction pour obtenir l'index du magasin à partir des coordonnées
local function getStoreIndex(coords)
    for i, store in ipairs(Config.Stores) do
        if store.coords.x == coords.x and store.coords.y == coords.y and store.coords.z == coords.z then
            return i
        end
    end
    return nil
end

-- Événement pour vérifier le nombre de policiers
RegisterNetEvent('asc_robbery:checkPolice')
AddEventHandler('asc_robbery:checkPolice', function()
    local src = source
    local count = getPoliceCount()
    TriggerClientEvent('asc_robbery:receivePoliceCount', src, count)
end)

-- Fonction pour vérifier si un cooldown est actif
local function isCooldownActive(cooldownType, identifier)
    -- Vérifier d'abord le cooldown global pour tous les types de braquages
    if globalCooldown and cooldownTimers.global and (cooldownTimers.global > GetGameTimer()) then
        -- Si c'est le même magasin que celui en cooldown global, on continue la vérification
        if cooldownType == 'store' and identifier == globalCooldownStore then
            -- Continue avec la vérification spécifique
        else
            return true -- Cooldown global actif pour un autre magasin
        end
    end

    -- Vérifications spécifiques selon le type
    if cooldownType == 'bank' then
        return bankCooldowns[identifier] and bankCooldownTimers[identifier] and (bankCooldownTimers[identifier] > GetGameTimer())
    elseif cooldownType == 'store' then
        return safeCooldowns[identifier] and cooldownTimers['safe_' .. identifier] and (cooldownTimers['safe_' .. identifier] > GetGameTimer())
    end
    return false
end

-- Fonction pour obtenir le temps restant d'un cooldown
local function getCooldownRemaining(cooldownType, identifier)
    local globalTime = 0
    local specificTime = 0

    -- Obtenir le temps restant du cooldown global
    if cooldownTimers.global then
        globalTime = math.ceil((cooldownTimers.global - GetGameTimer()) / 1000)
    end

    -- Obtenir le temps restant du cooldown spécifique
    if cooldownType == 'bank' and bankCooldownTimers[identifier] then
        specificTime = math.ceil((bankCooldownTimers[identifier] - GetGameTimer()) / 1000)
    elseif cooldownType == 'store' and cooldownTimers['safe_' .. identifier] then
        specificTime = math.ceil((cooldownTimers['safe_' .. identifier] - GetGameTimer()) / 1000)
    end

    -- Retourner le temps le plus long
    return math.max(globalTime, specificTime)
end

-- Événement pour vérifier si une caisse peut être braquée
RegisterNetEvent('asc_robbery:checkRegister')
AddEventHandler('asc_robbery:checkRegister', function(storeCoords, registerIndex)
    local src = source
    local storeIndex = getStoreIndex(storeCoords)
    
    if not storeIndex then return end
    
    -- Vérifier si la caisse est déjà en cours de braquage
    local lockKey = storeIndex .. '_' .. registerIndex
    if registerLocks[lockKey] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Cette caisse est déjà en cours de braquage',
            type = 'error'
        })
        return
    end

    -- Vérifier le cooldown global (sauf si c'est le même magasin)
    if isCooldownActive('global') and globalCooldownStore ~= storeIndex then
        local remainingTime = getCooldownRemaining('global')
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Un braquage est en cours dans un autre magasin (Reste: ' .. formatTime(remainingTime) .. ')',
            type = 'error'
        })
        return
    end

    -- Vérifier le cooldown de la caisse spécifique
    local cooldownKey = storeIndex .. '_' .. registerIndex
    if registerCooldowns[cooldownKey] then
        local remainingTime = cooldownTimers[cooldownKey] and math.ceil((cooldownTimers[cooldownKey] - GetGameTimer()) / 1000) or 0
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous devez attendre ' .. formatTime(remainingTime) .. ' avant de pouvoir faire le braquage.',
            type = 'error'
        })
        return
    end

    -- Verrouiller la caisse
    registerLocks[lockKey] = true
    TriggerClientEvent('asc_robbery:registerCheckResult', src, true)
end)

-- Événement pour vérifier si un coffre peut être braqué
RegisterNetEvent('asc_robbery:checkSafe')
AddEventHandler('asc_robbery:checkSafe', function(storeCoords)
    local src = source
    local storeIndex = getStoreIndex(storeCoords)
    
    if not storeIndex then return end
    
    -- Vérifier si le coffre est déjà en cours de braquage
    if safeLocks[storeIndex] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Ce coffre est déjà en cours de braquage',
            type = 'error'
        })
        return
    end

    -- Vérifier le cooldown global (sauf si c'est le même magasin)
    if isCooldownActive('global') and globalCooldownStore ~= storeIndex then
        local remainingTime = getCooldownRemaining('global')
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Un braquage est en cours dans un autre magasin (Reste: ' .. formatTime(remainingTime) .. ')',
            type = 'error'
        })
        return
    end

    -- Vérifier le cooldown du coffre du magasin
    if isCooldownActive('store', storeIndex) then
        local remainingTime = getCooldownRemaining('store', storeIndex)
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Le coffre de ce magasin a été braqué récemment (Reste: ' .. formatTime(remainingTime) .. ')',
            type = 'error'
        })
        return
    end

    -- Verrouiller le coffre
    safeLocks[storeIndex] = true
    
    TriggerClientEvent('asc_robbery:safeCheckResult', src, true)
end)

-- Événement pour libérer le verrou d'une caisse en cas d'échec
RegisterNetEvent('asc_robbery:releaseRegisterLock')
AddEventHandler('asc_robbery:releaseRegisterLock', function(storeCoords, registerIndex)
    local storeIndex = getStoreIndex(storeCoords)
    if storeIndex then
        local lockKey = storeIndex .. '_' .. registerIndex
        registerLocks[lockKey] = nil
    end
end)

-- Événement pour libérer le verrou d'un coffre en cas d'échec
RegisterNetEvent('asc_robbery:releaseSafeLock')
AddEventHandler('asc_robbery:releaseSafeLock', function(storeCoords)
    local storeIndex = getStoreIndex(storeCoords)
    if storeIndex then
        safeLocks[storeIndex] = nil
    end
end)

-- Fonction pour démarrer les cooldowns
local function startCooldowns(storeIndex, isSafe, registerIndex)
    -- Démarrer le cooldown approprié
    if isSafe then
        safeCooldowns[storeIndex] = true
        local timerKey = 'safe_' .. storeIndex
        cooldownTimers[timerKey] = GetGameTimer() + (Config.General.Stores.storeCooldown * 1000)
        
        -- Créer une boucle de mise à jour pour le compte à rebours
        -- CreateThread(function()
        --     while safeCooldowns[storeIndex] do
        --         Wait(1000) -- Mise à jour toutes les secondes
        --     end
        -- end)
        
        SetTimeout(Config.General.Stores.storeCooldown * 1000, function()
            safeCooldowns[storeIndex] = nil
            cooldownTimers[timerKey] = nil
            -- Vérifier si tous les cooldowns sont terminés pour réinitialiser
            local allCooldownsFinished = true
            for _, active in pairs(safeCooldowns) do
                if active then allCooldownsFinished = false break end
            end
            for _, active in pairs(registerCooldowns) do
                if active then allCooldownsFinished = false break end
            end
            for _, active in pairs(bankCooldowns) do
                if active then allCooldownsFinished = false break end
            end

            if allCooldownsFinished and not globalCooldown then
                resetAllBankDoors()
                TriggerClientEvent('asc_robbery:resetClientStates', -1)
            end
        end)
    else
        local cooldownKey = storeIndex .. '_' .. registerIndex
        registerCooldowns[cooldownKey] = true
        cooldownTimers[cooldownKey] = GetGameTimer() + (Config.General.Stores.storeCooldown * 1000)
        
        -- Créer une boucle de mise à jour pour le compte à rebours
        CreateThread(function()
            while registerCooldowns[cooldownKey] do
                Wait(1000) -- Mise à jour toutes les secondes
            end
        end)
        
        SetTimeout(Config.General.Stores.storeCooldown * 1000, function()
            registerCooldowns[cooldownKey] = nil
            cooldownTimers[cooldownKey] = nil

            -- Vérifier si tous les cooldowns sont terminés pour réinitialiser
            local allCooldownsFinished = true
            for _, active in pairs(safeCooldowns) do
                if active then allCooldownsFinished = false break end
            end
            for _, active in pairs(registerCooldowns) do
                if active then allCooldownsFinished = false break end
            end
            for _, active in pairs(bankCooldowns) do
                if active then allCooldownsFinished = false break end
            end

            if allCooldownsFinished and not globalCooldown then
                resetAllBankDoors()
                TriggerClientEvent('asc_robbery:resetClientStates', -1)
            end
        end)
    end
    
    -- Démarrer le cooldown global si pas déjà actif
    if not globalCooldown then
        globalCooldown = true
        globalCooldownStore = storeIndex
        cooldownTimers.global = GetGameTimer() + (Config.GlobalCooldown * 1000)
        
        -- Créer une boucle de mise à jour pour le compte à rebours
        CreateThread(function()
            while globalCooldown do
                Wait(1000) -- Mise à jour toutes les secondes
            end
        end)
        
        SetTimeout(Config.GlobalCooldown * 1000, function()
            globalCooldown = false
            globalCooldownStore = nil
            cooldownTimers.global = nil
            -- Vérifier si tous les cooldowns sont terminés pour réinitialiser
            local allCooldownsFinished = true
            for _, active in pairs(safeCooldowns) do
                if active then allCooldownsFinished = false break end
            end
            for _, active in pairs(registerCooldowns) do
                if active then allCooldownsFinished = false break end
            end
            for _, active in pairs(bankCooldowns) do
                if active then allCooldownsFinished = false break end
            end

            if allCooldownsFinished then
                resetAllBankDoors()
                TriggerClientEvent('asc_robbery:resetClientStates', -1)
            end
        end)
    end
end

-- Événement pour donner la récompense de la caisse
RegisterNetEvent('asc_robbery:giveRegisterReward')
AddEventHandler('asc_robbery:giveRegisterReward', function(storeCoords, registerIndex)
    local src = source
    local storeIndex = getStoreIndex(storeCoords)
    
    if not storeIndex then return end
    
    -- Calcul de la récompense
    local amount = math.random(Config.General.Stores.registerReward.minAmount, Config.General.Stores.registerReward.maxAmount)
    
    -- Ajout de l'item
    local success = exports.ox_inventory:AddItem(src, Config.General.Stores.registerReward.name, amount)
    
    if success then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Succès',
            description = 'Vous avez volé ' .. amount .. ' ' .. Config.General.Stores.registerReward.name,
            type = 'success'
        })
        
        -- Démarrer les cooldowns
        startCooldowns(storeIndex, false, registerIndex)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous ne pouvez pas transporter plus d\'argent.',
            type = 'error'
        })
    end
    
    -- Déverrouiller la caisse
    local lockKey = storeIndex .. '_' .. registerIndex
    registerLocks[lockKey] = nil
end)

-- Événement pour donner la récompense du coffre
RegisterNetEvent('asc_robbery:giveSafeReward')
AddEventHandler('asc_robbery:giveSafeReward', function(storeCoords)
    local src = source
    local storeIndex = getStoreIndex(storeCoords)
    
    if not storeIndex then return end
    
    -- Calcul de la récompense
    local amount = math.random(Config.General.Stores.rewardSafe.minAmount, Config.General.Stores.rewardSafe.maxAmount)
    
    -- Ajout de l'item
    local success = exports.ox_inventory:AddItem(src, Config.General.Stores.rewardSafe.name, amount)
    
    if success then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Succès',
            description = 'Vous avez volé ' .. amount .. ' ' .. Config.General.Stores.rewardSafe.name,
            type = 'success'
        })

        -- Démarrer les cooldowns
        startCooldowns(storeIndex, true)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous ne pouvez pas transporter plus d\'argent.',
            type = 'error'
        })
    end

    -- Déverrouiller le coffre
    safeLocks[storeIndex] = nil
end)

-- Événement pour vérifier si un coffre de banque peut être percé
RegisterNetEvent('asc_robbery:checkVault')
AddEventHandler('asc_robbery:checkVault', function(bankId, vaultIndex)
    local src = source

    -- Vérifier si la banque existe
    if not Config.Banks[bankId] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Banque non trouvée',
            type = 'error'
        })
        return
    end

    -- Créer la clé de verrouillage
    local lockKey = bankId .. '_' .. vaultIndex

    -- Vérifier si le coffre est déjà en cours de perçage
    if vaultLocks[lockKey] then
        local lockingPlayer = vaultLocks[lockKey]
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Ce coffre est déjà en cours de perçage par un autre joueur',
            type = 'error'
        })
        return
    end

    -- Vérifier si le joueur a la perceuse
    local hasItem = exports.ox_inventory:Search(src, 'count', Config.General.bank.requiredItemVault) > 0
    if not hasItem then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous avez besoin d\'une perceuse',
            type = 'error'
        })
        return
    end

    -- Verrouiller le coffre
    vaultLocks[lockKey] = src

    TriggerClientEvent('asc_robbery:vaultCheckResult', src, true, bankId, vaultIndex)
end)

-- Événement pour libérer le verrou d'un coffre de banque en cas d'échec
RegisterNetEvent('asc_robbery:unlockVault')
AddEventHandler('asc_robbery:unlockVault', function(bankId, vaultIndex)
    local lockKey = bankId .. '_' .. vaultIndex
    vaultLocks[lockKey] = nil
end)

-- Événement pour donner la récompense du coffre
RegisterNetEvent('asc_robbery:giveVaultReward', function(bankId, vaultIndex)
    local src = source

    -- Créer la clé de verrouillage
    local lockKey = bankId .. '_' .. vaultIndex

    -- Vérifier que le joueur a bien le verrou
    if vaultLocks[lockKey] ~= src then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Action non autorisée',
            type = 'error'
        })
        return
    end

    -- Vérifier si le joueur a la perceuse
    local hasItem = exports.ox_inventory:Search(src, 'count', Config.General.bank.requiredItemVault) > 0
    if hasItem then
        -- Donner la récompense
        local amount = math.random(Config.General.bank.reward.minAmount, Config.General.bank.reward.maxAmount)
        local success = exports.ox_inventory:AddItem(src, Config.General.bank.reward.name, amount)

        if success then
            -- Supprimer la perceuse
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Succès',
                description = 'Vous avez percé le coffre et obtenu ' .. amount .. ' ' .. Config.General.bank.reward.name,
                type = 'success'
            })
        else
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Erreur',
                description = 'Vous ne pouvez pas transporter plus d\'objets',
                type = 'error'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous avez besoin d\'une perceuse',
            type = 'error'
        })
    end

    -- Libérer le verrou du coffre
    vaultLocks[lockKey] = nil

    -- Mettre à jour l'état global du coffre percé
    TriggerEvent('asc_robbery:updateDrilledVault', bankId, vaultIndex, true)
end)

------------ FLEECA
-- Événement pour donner l'item du garde
RegisterNetEvent('asc_robbery:givePedItem', function()
    local src = source
    local success = exports.ox_inventory:AddItem(src, Config.General.bank.pedItem.name, Config.General.bank.pedItem.minAmount)
    
    if success then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Succès',
            description = 'Vous avez obtenu la carte du garde',
            type = 'success'
        })
    else
    end
end)

-- Événement pour déverrouiller la porte de la banque
RegisterNetEvent('asc_robbery:unlockBankDoor', function(doorId, bankId)
    local src = source

    -- Vérifier si bankId est valide
    if not bankId then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'ID de banque invalide',
            type = 'error'
        })
        return
    end

    -- Vérifier si la banque existe
    if not Config.Banks[bankId] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Banque non trouvée',
            type = 'error'
        })
        return
    end
    
    local door = exports.ox_doorlock:getDoor(doorId)
    if door then
        -- Vérifier si le joueur a la carte
        local hasItem = exports.ox_inventory:Search(src, 'count', Config.General.bank.pedItem.name) > 0
        if hasItem then
            -- Débarrer la porte
            exports.ox_doorlock:setDoorState(doorId, 0)
            -- Supprimer la carte après le succès du minigame (seulement si pas déjà fait)
            if not globalBankStates.hasInsertedCard[bankId] then
                exports.ox_inventory:RemoveItem(src, Config.General.bank.pedItem.name, 1)
            else
            end
            -- Ouvrir la porte avec le heading configuré
            TriggerClientEvent('asc_robbery:openBankDoor', -1, doorId, bankId)
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Succès',
                description = 'La porte est déverrouillée',
                type = 'success'
            })

            -- Mettre à jour l'état global de la porte
            TriggerEvent('asc_robbery:updateDoorState', bankId, true)

            -- Mettre à jour l'état de la carte insérée
            TriggerEvent('asc_robbery:updateInsertedCard', bankId, true)
        else
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Erreur',
                description = 'Vous avez besoin de la carte du garde',
                type = 'error'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'La porte n\'existe pas',
            type = 'error'
        })
    end
end)

-- Événement pour barrer la porte de la banque
RegisterNetEvent('asc_robbery:lockBankDoor', function(bankId)
    local src = source

    if not bankId then
        return
    end

    -- Obtenir l'ID de la porte la plus proche
    local door = exports.ox_doorlock:getClosestDoor()
    if door then
        exports.ox_doorlock:setDoorState(door.id, 1) -- 1 = barré
        -- Fermer la porte physiquement aussi
        TriggerClientEvent('asc_robbery:closeBankDoor', -1, door.id, bankId)
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Succès',
            description = 'La porte est barrée et fermée',
            type = 'success'
        })
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Aucune porte trouvée à proximité',
            type = 'error'
        })
    end
end)

-- Événement pour retirer la thermite
RegisterNetEvent('asc_robbery:removeThermite')
AddEventHandler('asc_robbery:removeThermite', function()
    local src = source
    exports.ox_inventory:RemoveItem(src, Config.General.bank.requiredItemVaultDoor, 1)
end)

-- Événement pour déverrouiller la porte avec le thermite
RegisterNetEvent('asc_robbery:unlockThermiteDoor', function(doorId, bankId)
    local src = source

    -- Déverrouiller la porte
    exports.ox_doorlock:setDoorState(doorId, 0)

    -- Mettre à jour l'état global de la thermite si bankId est fourni
    if bankId then
        TriggerEvent('asc_robbery:updateUsedThermite', bankId, true)
    end

    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Succès',
        description = 'La porte du coffre est déverrouillée',
        type = 'success'
    })
end)

-- Événement pour vérifier le cooldown de la banque
RegisterNetEvent('asc_robbery:checkBankCooldown')
AddEventHandler('asc_robbery:checkBankCooldown', function(bankId)
    local src = source
    
    -- Vérifier le nombre de policiers
    local policeCount = getPoliceCount()
    if policeCount < Config.Banks[bankId].minPolice then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Il n\'y a pas assez de policiers en service pour braquer cette banque',
            type = 'error'
        })
        TriggerClientEvent('asc_robbery:bankCooldownResult', src, false, bankId)
        return
    end
    
    -- Vérifier le cooldown global ou spécifique
    if isCooldownActive('global') or isCooldownActive('bank', bankId) then
        local remainingTime = getCooldownRemaining('bank', bankId)
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous devez attendre ' .. formatTime(remainingTime) .. ' avant de pouvoir commencer le braquage',
            type = 'error'
        })
        TriggerClientEvent('asc_robbery:bankCooldownResult', src, false, bankId)
        return
    end

    -- Si pas de cooldown, autoriser le braquage
    TriggerClientEvent('asc_robbery:bankCooldownResult', src, true, bankId)

    -- Mettre à jour l'état global
    TriggerEvent('asc_robbery:updateThreatenedGuard', bankId, true)
end)

-- Événement pour démarrer le cooldown de la banque
RegisterNetEvent('asc_robbery:startBankCooldown')
AddEventHandler('asc_robbery:startBankCooldown', function(bankId)
    local src = source
    
    -- Vérifier si la banque est déjà en cooldown
    if isCooldownActive('bank', bankId) then
        local remainingTime = getCooldownRemaining('bank', bankId)
        return
    end

    -- Démarrer le cooldown global
    globalCooldown = true
    globalCooldownStore = 'bank_' .. bankId
    cooldownTimers.global = GetGameTimer() + (Config.GlobalCooldown * 1000)

    -- Démarrer le cooldown spécifique à la banque
    bankCooldowns[bankId] = true
    bankCooldownTimers[bankId] = GetGameTimer() + (Config.Banks[bankId].cooldown * 1000)

    -- Timer pour réinitialiser le cooldown global
    SetTimeout(Config.GlobalCooldown * 1000, function()
        globalCooldown = false
        globalCooldownStore = nil

        -- Vérifier si tous les cooldowns sont terminés pour réinitialiser
        local allCooldownsFinished = true
        for _, active in pairs(safeCooldowns) do
            if active then allCooldownsFinished = false break end
        end
        for _, active in pairs(registerCooldowns) do
            if active then allCooldownsFinished = false break end
        end
        for _, active in pairs(bankCooldowns) do
            if active then allCooldownsFinished = false break end
        end

        if allCooldownsFinished then
            resetAllBankDoors()
            TriggerClientEvent('asc_robbery:resetClientStates', -1)
        end
    end)

    -- Timer pour réinitialiser le cooldown de la banque
    SetTimeout(Config.Banks[bankId].cooldown * 1000, function()
        bankCooldowns[bankId] = false
        -- Vérifier si tous les cooldowns sont terminés pour réinitialiser
        local allCooldownsFinished = true
        for _, active in pairs(safeCooldowns) do
            if active then allCooldownsFinished = false break end
        end
        for _, active in pairs(registerCooldowns) do
            if active then allCooldownsFinished = false break end
        end
        for _, active in pairs(bankCooldowns) do
            if active then allCooldownsFinished = false break end
        end

        if allCooldownsFinished and not globalCooldown then
            resetAllBankDoors()
            TriggerClientEvent('asc_robbery:resetClientStates', -1)
        end
    end)
end)

-- Fonction pour réinitialiser tous les états du serveur
local function resetAllServerStates()

    -- Réinitialiser tous les verrous
    registerLocks = {}
    safeLocks = {}
    vaultLocks = {}

    -- Réinitialiser les états globaux des banques
    globalBankStates = {
        hasThreatenedGuard = {},
        hasInsertedCard = {},
        hasUsedThermite = {},
        drilledVaults = {},
        doorStates = {}
    }

    -- Réinitialiser tous les cooldowns
    registerCooldowns = {}
    safeCooldowns = {}
    bankCooldowns = {}

    -- Réinitialiser le cooldown global
    globalCooldown = false
    globalCooldownStore = nil

    -- Réinitialiser tous les timers
    cooldownTimers = {}
    bankCooldownTimers = {}

    -- Fermer et barrer toutes les portes de banque
    resetAllBankDoors()
end

-- Événement pour réinitialiser tous les états
RegisterNetEvent('asc_robbery:resetAllStates')
AddEventHandler('asc_robbery:resetAllStates', function()
    local src = source
    resetAllServerStates()

    -- Notifier tous les clients de réinitialiser leurs états
    TriggerClientEvent('asc_robbery:resetClientStates', -1)

    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Succès',
        description = 'Tous les états ont été réinitialisés',
        type = 'success'
    })
end)

-- Réinitialisation lors du démarrage de la ressource
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(2000) -- Attendre que tout soit chargé
        resetAllServerStates()
        TriggerClientEvent('asc_robbery:resetClientStates', -1)
    end
end)

-- Commande pour réinitialiser manuellement (pour les admins)
RegisterCommand('resetrobbery', function(source, args, rawCommand)
    local src = source

    -- Vérifier si c'est un admin (vous pouvez adapter cette vérification selon votre système)
    if src == 0 or IsPlayerAceAllowed(src, 'command.resetrobbery') then
        resetAllServerStates()
        TriggerClientEvent('asc_robbery:resetClientStates', -1)

        if src == 0 then
        else
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Succès',
                description = 'Tous les états de braquage ont été réinitialisés',
                type = 'success'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous n\'avez pas la permission d\'utiliser cette commande',
            type = 'error'
        })
    end
end, false)

-- Commande pour afficher le statut des cooldowns (pour les admins)
RegisterCommand('robberyStatus', function(source, args, rawCommand)
    local src = source

    -- Vérifier si c'est un admin
    if src == 0 or IsPlayerAceAllowed(src, 'command.resetrobbery') then
        if src ~= 0 then
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Info',
                description = 'Statut des cooldowns affiché dans la console serveur',
                type = 'info'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous n\'avez pas la permission d\'utiliser cette commande',
            type = 'error'
        })
    end
end, false)

-- Commande pour tester les portes ox_doorlock (pour les admins)
RegisterCommand('testBankDoors', function(source, args, rawCommand)
    local src = source

    -- Vérifier si c'est un admin
    if src == 0 or IsPlayerAceAllowed(src, 'command.resetrobbery') then

        -- Obtenir toutes les portes d'ox_doorlock
        local allDoors = exports.ox_doorlock:getAllDoors()

        for bankId, bankConfig in pairs(Config.Banks) do

            -- Utiliser la fonction findBankDoor pour trouver la porte
            local door = findBankDoor(bankId, bankConfig)

            if door then
                if door.coords then
                    local distance = #(vector3(bankConfig.vaultDoorCoords.x, bankConfig.vaultDoorCoords.y, bankConfig.vaultDoorCoords.z) - vector3(door.coords.x, door.coords.y, door.coords.z))
                end
            else
            end

            -- Tester les portes secondaires
            if bankConfig.secondaryDoors and #bankConfig.secondaryDoors > 0 then
                for i, secondaryDoorId in pairs(bankConfig.secondaryDoors) do
                    local secondaryDoor = exports.ox_doorlock:getDoor(secondaryDoorId)
                    if secondaryDoor then
                        if secondaryDoor.coords then
                        end
                    else
                    end
                end
            else
            end
        end


        if src ~= 0 then
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Info',
                description = 'Test des portes affiché dans la console serveur',
                type = 'info'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous n\'avez pas la permission d\'utiliser cette commande',
            type = 'error'
        })
    end
end, false)

-- Nettoyage lors de l'arrêt de la ressource
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        resetAllServerStates()
    end
end)

-- Commande pour lister toutes les portes ox_doorlock (pour les admins)
RegisterCommand('listAllDoors', function(source, args, rawCommand)
    local src = source

    -- Vérifier si c'est un admin
    if src == 0 or IsPlayerAceAllowed(src, 'command.resetrobbery') then

        -- Obtenir toutes les portes d'ox_doorlock
        local allDoors = exports.ox_doorlock:getAllDoors()

        for _, door in pairs(allDoors) do
            if door.coords then
            end
        end

        if src ~= 0 then
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Info',
                description = 'Liste des portes affichée dans la console serveur',
                type = 'info'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous n\'avez pas la permission d\'utiliser cette commande',
            type = 'error'
        })
    end
end, false)

-- Événement pour demander la synchronisation des états
RegisterNetEvent('asc_robbery:requestSync')
AddEventHandler('asc_robbery:requestSync', function(bankId)
    local src = source

    -- Préparer les données de synchronisation pour la banque spécifique
    local syncData = {
        hasThreatenedGuard = globalBankStates.hasThreatenedGuard[bankId] or false,
        hasInsertedCard = globalBankStates.hasInsertedCard[bankId] or false,
        hasUsedThermite = globalBankStates.hasUsedThermite[bankId] or false,
        drilledVaults = globalBankStates.drilledVaults[bankId] or {},
        doorState = globalBankStates.doorStates[bankId] or false
    }

    -- Envoyer les données de synchronisation au client
    TriggerClientEvent('asc_robbery:receiveSync', src, bankId, syncData)
end)

-- Événements pour mettre à jour les états globaux
RegisterNetEvent('asc_robbery:updateThreatenedGuard')
AddEventHandler('asc_robbery:updateThreatenedGuard', function(bankId, state)
    globalBankStates.hasThreatenedGuard[bankId] = state
    TriggerClientEvent('asc_robbery:syncThreatenedGuard', -1, bankId, state)
end)

RegisterNetEvent('asc_robbery:updateInsertedCard')
AddEventHandler('asc_robbery:updateInsertedCard', function(bankId, state)
    globalBankStates.hasInsertedCard[bankId] = state
    TriggerClientEvent('asc_robbery:syncInsertedCard', -1, bankId, state)
end)

RegisterNetEvent('asc_robbery:updateUsedThermite')
AddEventHandler('asc_robbery:updateUsedThermite', function(bankId, state)
    globalBankStates.hasUsedThermite[bankId] = state
    TriggerClientEvent('asc_robbery:syncUsedThermite', -1, bankId, state)
end)

RegisterNetEvent('asc_robbery:updateDrilledVault')
AddEventHandler('asc_robbery:updateDrilledVault', function(bankId, vaultIndex, state)
    if not globalBankStates.drilledVaults[bankId] then
        globalBankStates.drilledVaults[bankId] = {}
    end
    globalBankStates.drilledVaults[bankId][vaultIndex] = state
    TriggerClientEvent('asc_robbery:syncDrilledVault', -1, bankId, vaultIndex, state)
end)

RegisterNetEvent('asc_robbery:updateDoorState')
AddEventHandler('asc_robbery:updateDoorState', function(bankId, isOpen)
    globalBankStates.doorStates[bankId] = isOpen
    TriggerClientEvent('asc_robbery:syncDoorState', -1, bankId, isOpen)
end)

------------------------------------------------- PALETO BANK
-- Événement pour vérifier le nombre de policiers pour Paleto Bank
RegisterNetEvent('asc_robbery:checkPoliceForPaleto')
AddEventHandler('asc_robbery:checkPoliceForPaleto', function()
    local src = source
    local policeCount = getPoliceCount()
    local requiredPolice = Config.Paletobank.minPolice

    TriggerClientEvent('asc_robbery:receivePoliceCountPaleto', src, policeCount, requiredPolice)
end)

-- Événement pour tenter de couper les fils de Paleto Bank
RegisterNetEvent('asc_robbery:attemptPaletoCutWires')
AddEventHandler('asc_robbery:attemptPaletoCutWires', function()
    local src = source

    local policeCount = getPoliceCount()
    local requiredPolice = Config.Paletobank.minPolice
    local requiredItem = Config.Paletobank.requiredItemsCutWire

    -- Vérifier si les fils ont déjà été coupés
    if paletoServerState.wiresCut then
        local remainingTime = getRemainingCooldownTime('paleto')
        local timeFormatted = formatTime(remainingTime)
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Banque en cooldown',
            description = 'Paleto Bank est en cours de braquage. Temps restant: ' .. timeFormatted,
            type = 'error'
        })
        return
    end

    -- Vérifier le cooldown de Paleto Bank
    if paletoCooldownActive then
        local remainingTime = getRemainingCooldownTime('paleto')
        local timeFormatted = formatTime(remainingTime)
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Banque en cooldown',
            description = 'Paleto Bank a été braquée récemment. Temps restant: ' .. timeFormatted,
            type = 'error'
        })
        return
    end

    -- Vérifier le cooldown global (système unifié)
    if globalCooldown then
        local remainingTime = math.max(0, math.ceil((cooldownTimers.global - GetGameTimer()) / 1000))
        local timeFormatted = formatTime(remainingTime)
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Cooldown global',
            description = 'Un braquage est en cours ailleurs. Temps restant: ' .. timeFormatted,
            type = 'error'
        })
        return
    end

    -- Vérifier le nombre de policiers
    if policeCount < requiredPolice then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Il n\'y a pas assez de policiers en service pour braquer cette banque.',
            type = 'error'
        })
        return
    end

    -- Vérifier si le joueur a l'item requis (double sécurité côté serveur)
    local hasItem = exports.ox_inventory:Search(src, 'count', requiredItem)
    if hasItem < 1 then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous avez besoin de: ' .. requiredItem,
            type = 'error'
        })
        return
    end
    -- Si toutes les conditions sont remplies, démarrer le minigame
    TriggerClientEvent('asc_robbery:startPaletoMinigame', src)
end)

-- Événement pour gérer le succès du minigame Paleto
RegisterNetEvent('asc_robbery:paletoMinigameSuccess')
AddEventHandler('asc_robbery:paletoMinigameSuccess', function()
    local src = source
    local requiredItem = Config.Paletobank.requiredItemsCutWire

    -- Déclencher la progress bar côté client
    TriggerClientEvent('asc_robbery:startWireCuttingProgress', src)
end)

-- Événement pour finaliser la coupure des fils après la progress bar
RegisterNetEvent('asc_robbery:finalizePaletoWireCut')
AddEventHandler('asc_robbery:finalizePaletoWireCut', function()
    local src = source
    local requiredItem = Config.Paletobank.requiredItemsCutWire

    -- Notification de succès
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Succès',
        description = 'Système de sécurité désactivé !',
        type = 'success'
    })

    -- Notifier tous les clients que les fils sont coupés
    TriggerClientEvent('asc_robbery:paletoWiresCut', -1)

    -- Mettre à jour l'état côté serveur
    paletoServerState.wiresCut = true

    -- Démarrer les cooldowns APRÈS le succès du minigame

    local paletoCooldownTime = Config.Paletobank.cooldown * 1000 -- Convertir en millisecondes
    local globalCooldownTime = Config.GlobalCooldown * 1000 -- Convertir en millisecondes


    -- Démarrer le cooldown de Paleto Bank
    paletoCooldownActive = true
    paletoCooldownStartTime = GetGameTimer()
    paletoCooldownTimer = SetTimeout(paletoCooldownTime, function()
        paletoCooldownActive = false
        paletoCooldownTimer = nil
        paletoCooldownStartTime = 0

        -- Reset complet de Paleto Bank après le cooldown
        paletoServerState = {
            wiresCut = false,
            c4DoorExists = true,
            mainDoorOpen = false,
            thermiteUsed = false,
            c4Used = false,
            vaultsDrilled = {}
        }

        -- Reset des verrous et coffres percés
        paletoVaultLocks = {}
        paletoVaultDrilled = {}

        -- Synchroniser le reset avec tous les joueurs
        TriggerClientEvent('asc_robbery:syncPaletoState', -1, paletoServerState)
    end)

    -- Démarrer le cooldown global (système unifié)
    globalCooldown = true
    globalCooldownStore = 'paleto_bank'
    cooldownTimers.global = GetGameTimer() + (Config.GlobalCooldown * 1000)

    -- Timer pour le cooldown global
    SetTimeout(globalCooldownTime, function()
        globalCooldown = false
        globalCooldownStore = nil
        cooldownTimers.global = nil
    end)

end)

-- Événement pour tenter de hacker la porte principale de Paleto Bank
RegisterNetEvent('asc_robbery:attemptPaletoMainDoorHack')
AddEventHandler('asc_robbery:attemptPaletoMainDoorHack', function()
    local src = source
    local policeCount = getPoliceCount()
    local requiredPolice = Config.Paletobank.minPolice
    local requiredItem = Config.Paletobank.requiredItemMainDoor


    -- Vérifier si la thermite a déjà été utilisée
    if paletoServerState.thermiteUsed then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Déjà utilisé',
            description = 'La thermite a déjà été utilisée sur cette porte',
            type = 'error'
        })
        return
    end

    -- Vérifier le nombre de policiers
    if policeCount < requiredPolice then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Il n\'y a pas assez de policiers en service pour braquer cette banque.',
            type = 'error'
        })
        return
    end

    -- Vérifier si le joueur a l'item requis (thermite)
    local hasItem = exports.ox_inventory:Search(src, 'count', requiredItem)
    if hasItem < 1 then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous avez besoin de: ' .. requiredItem,
            type = 'error'
        })
        return
    end

         TriggerClientEvent('asc_robbery:startPaletoThermiteMinigame', src)
end)

-- Événement pour gérer le succès du minigame thermite de la porte principale
RegisterNetEvent('asc_robbery:paletoThermiteSuccess')
AddEventHandler('asc_robbery:paletoThermiteSuccess', function()
    local src = source
        -- Retirer l'item thermite
    exports.ox_inventory:RemoveItem(src, Config.Paletobank.requiredItemMainDoor, 1)

    -- Déclencher la progress bar de piratage côté client
    TriggerClientEvent('asc_robbery:startSystemHackingProgress', src)
end)

-- Événement pour finaliser l'ouverture de la porte après la progress bar
RegisterNetEvent('asc_robbery:finalizePaletoMainDoorHack')
AddEventHandler('asc_robbery:finalizePaletoMainDoorHack', function()
    local src = source

    -- Notification de succès
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Succès',
        description = 'Système piraté ! La porte s\'ouvre...',
        type = 'success'
    })

    -- Mettre à jour l'état côté serveur
    paletoServerState.mainDoorOpen = true
    paletoServerState.thermiteUsed = true

    -- Ouvrir la porte pour tous les joueurs
    TriggerClientEvent('asc_robbery:openPaletoMainDoor', -1)

    -- Synchroniser l'état complet avec tous les joueurs
    TriggerClientEvent('asc_robbery:syncPaletoState', -1, paletoServerState)

end)

-- Événement pour tenter de placer le C4 sur la porte secondaire de Paleto Bank
RegisterNetEvent('asc_robbery:attemptPaletoC4Door')
AddEventHandler('asc_robbery:attemptPaletoC4Door', function()
    local src = source
    local policeCount = getPoliceCount()
    local requiredPolice = Config.Paletobank.minPolice
    local requiredItem = Config.Paletobank.requiredItemC4Door

    -- Vérifier si le C4 a déjà été utilisé
    if paletoServerState.c4Used then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Déjà utilisé',
            description = 'Le C4 a déjà été placé sur cette porte',
            type = 'error'
        })
        return
    end

    -- Vérifier le nombre de policiers
    if policeCount < requiredPolice then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Il n\'y a pas assez de policiers en service pour braquer cette banque.',
            type = 'error'
        })
        return
    end

    -- Vérifier si le joueur a l'item requis (C4)
    local hasItem = exports.ox_inventory:Search(src, 'count', requiredItem)
    if hasItem < 1 then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous avez besoin de: ' .. requiredItem,
            type = 'error'
        })
        return
    end


    -- Retirer l'item C4
    local success = exports.ox_inventory:RemoveItem(src, requiredItem, 1)
    if success then

        -- Démarrer le minigame C4
        TriggerClientEvent('asc_robbery:startPaletoC4Minigame', src)
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Erreur lors de l\'utilisation de l\'item',
            type = 'error'
        })
    end
end)

-- Événement pour gérer le succès du minigame C4
RegisterNetEvent('asc_robbery:paletoC4Success')
AddEventHandler('asc_robbery:paletoC4Success', function()
    local src = source

    -- Déclencher la progress bar de placement C4 côté client
    TriggerClientEvent('asc_robbery:startC4PlacementProgress', src)
end)

-- Événement pour finaliser l'explosion après le placement C4
RegisterNetEvent('asc_robbery:finalizeC4Explosion')
AddEventHandler('asc_robbery:finalizeC4Explosion', function()
    local src = source


    -- Notification de succès
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'C4 Placé',
        description = 'Le C4 va exploser dans 10 secondes !',
        type = 'warning'
    })

    -- Notifier tous les joueurs que le C4 a été placé
    TriggerClientEvent('asc_robbery:c4PlacedNotification', -1)

    -- Attendre 10 secondes puis faire exploser pour tous les joueurs
    CreateThread(function()
        Wait(10000)

        -- Mettre à jour l'état côté serveur
        paletoServerState.c4DoorExists = false
        paletoServerState.c4Used = true

        -- Synchroniser l'explosion avec tous les joueurs
        TriggerClientEvent('asc_robbery:explodePaletoC4Door', -1)

        -- Synchroniser l'état complet avec tous les joueurs
        TriggerClientEvent('asc_robbery:syncPaletoState', -1, paletoServerState)

    end)
end)

-- Événement pour donner des items de debug pour Paleto Bank
RegisterNetEvent('asc_robbery:givePaletoItem')
AddEventHandler('asc_robbery:givePaletoItem', function(itemName)
    local src = source

    if not itemName then
        return
    end

    local success = exports.ox_inventory:AddItem(src, itemName, 1)

    if success then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Debug',
            description = 'Vous avez reçu: ' .. itemName,
            type = 'success'
        })
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Impossible d\'ajouter l\'item: ' .. itemName,
            type = 'error'
        })
    end
end)

-- Fonction pour replacer la porte C4 de Paleto Bank
local function replacePaletoC4Door()

    -- Demander à tous les clients de vérifier et replacer la porte C4
    TriggerClientEvent('asc_robbery:checkAndReplaceC4Door', -1)
end

-- Événement au démarrage de la ressource
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(5000) -- Attendre que tout soit chargé
        replacePaletoC4Door()
    end
end)

-- Événement pour replacer la porte C4 manuellement
RegisterNetEvent('asc_robbery:requestReplaceC4Door')
AddEventHandler('asc_robbery:requestReplaceC4Door', function()
    local src = source
    replacePaletoC4Door()
end)

-- Événement pour mettre à jour l'état global quand un garde est menacé
RegisterNetEvent('asc_robbery:updateThreatenedGuard')
AddEventHandler('asc_robbery:updateThreatenedGuard', function(bankId, state)
    globalBankStates.hasThreatenedGuard[bankId] = state

    -- Synchroniser avec tous les clients dans la zone
    TriggerClientEvent('asc_robbery:syncThreatenedGuard', -1, bankId, state)
end)

-- Événement pour mettre à jour l'état global quand une carte est insérée
RegisterNetEvent('asc_robbery:updateInsertedCard')
AddEventHandler('asc_robbery:updateInsertedCard', function(bankId, state)
    globalBankStates.hasInsertedCard[bankId] = state

    -- Synchroniser avec tous les clients
    TriggerClientEvent('asc_robbery:syncInsertedCard', -1, bankId, state)
end)

-- Événement pour mettre à jour l'état global quand la thermite est utilisée
RegisterNetEvent('asc_robbery:updateUsedThermite')
AddEventHandler('asc_robbery:updateUsedThermite', function(bankId, state)
    globalBankStates.hasUsedThermite[bankId] = state

    -- Synchroniser avec tous les clients
    TriggerClientEvent('asc_robbery:syncUsedThermite', -1, bankId, state)
end)

-- Événement pour mettre à jour l'état global quand un coffre est percé
RegisterNetEvent('asc_robbery:updateDrilledVault')
AddEventHandler('asc_robbery:updateDrilledVault', function(bankId, vaultIndex, state)
    if not globalBankStates.drilledVaults[bankId] then
        globalBankStates.drilledVaults[bankId] = {}
    end
    globalBankStates.drilledVaults[bankId][vaultIndex] = state

    -- Synchroniser avec tous les clients
    TriggerClientEvent('asc_robbery:syncDrilledVault', -1, bankId, vaultIndex, state)
end)

-- Événement pour mettre à jour l'état de la porte
RegisterNetEvent('asc_robbery:updateDoorState')
AddEventHandler('asc_robbery:updateDoorState', function(bankId, isOpen)
    globalBankStates.doorStates[bankId] = isOpen

    -- Synchroniser avec tous les clients
    TriggerClientEvent('asc_robbery:syncDoorState', -1, bankId, isOpen)
end)

-- Événement pour fermer la porte de banque pour tous les joueurs
RegisterNetEvent('asc_robbery:closeBankDoorForAll')
AddEventHandler('asc_robbery:closeBankDoorForAll', function(bankId)
    local src = source

    -- Vérifier si la banque existe
    if not Config.Banks[bankId] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Banque non trouvée',
            type = 'error'
        })
        return
    end

    -- Trouver la porte de la banque
    local door = findBankDoor(bankId, Config.Banks[bankId])
    if door then
        -- Barrer la porte via ox_doorlock
        local success = exports.ox_doorlock:setDoorState(door.id, 1) -- 1 = barré
        if success then
            -- Fermer la porte physiquement pour tous les joueurs
            TriggerClientEvent('asc_robbery:closeBankDoor', -1, door.id, bankId)

            -- Mettre à jour l'état global de la porte
            TriggerEvent('asc_robbery:updateDoorState', bankId, false)

            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Succès',
                description = 'La porte est barrée et fermée',
                type = 'success'
            })
        else
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Erreur',
                description = 'Impossible de barrer la porte',
                type = 'error'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Aucune porte trouvée à proximité',
            type = 'error'
        })
    end
end)

-- Événement pour tenter de percer un coffre du vault de Paleto Bank
RegisterNetEvent('asc_robbery:attemptPaletoDrillVault')
AddEventHandler('asc_robbery:attemptPaletoDrillVault', function(vaultIndex, vaultCoords)
    local src = source

    local policeCount = getPoliceCount()
    local requiredPolice = Config.Paletobank.minPolice
    local requiredItem = Config.Paletobank.vaultRewardRequiredItem


    -- Vérifier si le coffre est déjà percé
    if paletoVaultDrilled[vaultIndex] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Coffre déjà percé',
            description = 'Ce coffre a déjà été percé',
            type = 'error'
        })
        return
    end

    -- Vérifier si le coffre est en cours de perçage par un autre joueur
    if paletoVaultLocks[vaultIndex] and paletoVaultLocks[vaultIndex] ~= src then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Coffre occupé',
            description = 'Un autre joueur est déjà en train de percer ce coffre',
            type = 'error'
        })
        return
    end

    -- Vérifier si les fils sont coupés (condition simplifiée)
    if not paletoServerState.wiresCut then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Accès refusé',
            description = 'Les fils du système de sécurité doivent être coupés d\'abord',
            type = 'error'
        })
        return
    end

    -- Verrouiller le coffre pour ce joueur
    paletoVaultLocks[vaultIndex] = src

    -- Vérifier le nombre de policiers
    if policeCount < requiredPolice then
        paletoVaultLocks[vaultIndex] = nil -- Déverrouiller en cas d'erreur
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Il n\'y a pas assez de policiers en service pour braquer cette banque.',
            type = 'error'
        })
        return
    end

    -- Vérifier si le joueur a l'item requis (drill)
    local hasItem = exports.ox_inventory:Search(src, 'count', requiredItem)
    if hasItem < 1 then
        paletoVaultLocks[vaultIndex] = nil -- Déverrouiller en cas d'erreur
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous avez besoin de: ' .. requiredItem,
            type = 'error'
        })
        return
    end


        -- Démarrer le minigame de perçage
        TriggerClientEvent('asc_robbery:startPaletoDrillMinigame', src, vaultIndex, vaultCoords)

        -- Déclencher l'alerte police
        exports['ps-dispatch']:PaletoBankRobbery()

end)

-- Événement pour gérer le succès du minigame de perçage
RegisterNetEvent('asc_robbery:paletoDrillSuccess')
AddEventHandler('asc_robbery:paletoDrillSuccess', function(vaultIndex)
    local src = source


    -- Vérifier que c'est bien le joueur qui a verrouillé ce coffre
    if paletoVaultLocks[vaultIndex] ~= src then
        return
    end

    -- Vérifier que le coffre n'a pas déjà été percé
    if paletoVaultDrilled[vaultIndex] then
        return
    end

    -- Retirer l'item drill maintenant que le minigame est réussi
    local requiredItem = Config.Paletobank.vaultRewardRequiredItem
    -- local success = exports.ox_inventory:RemoveItem(src, requiredItem, 1)
    -- if not success then
    --     paletoVaultLocks[vaultIndex] = nil -- Déverrouiller en cas d'erreur
    --     TriggerClientEvent('ox_lib:notify', src, {
    --         title = 'Erreur',
    --         description = 'Erreur lors de l\'utilisation de l\'item',
    --         type = 'error'
    --     })
    --     return
    -- end

    -- Marquer le coffre comme percé définitivement
    paletoVaultDrilled[vaultIndex] = true
    paletoVaultLocks[vaultIndex] = nil -- Libérer le verrou

    -- Donner la récompense
    local reward = Config.Paletobank.reward
    local rewardAmount = math.random(reward.minAmount, reward.maxAmount)

    local rewardSuccess = exports.ox_inventory:AddItem(src, reward.name, rewardAmount)

    if rewardSuccess then

        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Succès !',
            description = 'Vous avez obtenu ' .. rewardAmount .. ' ' .. reward.name .. ' !',
            type = 'success'
        })

        -- Marquer ce coffre comme percé
        TriggerEvent('asc_robbery:updateDrilledVault', 'paletobank', vaultIndex, true)

        -- Mettre à jour l'état côté serveur
        paletoServerState.vaultsDrilled[vaultIndex] = true

        -- Notifier tous les clients que ce coffre a été percé
        TriggerClientEvent('asc_robbery:paletoVaultDrilled', -1, vaultIndex)

        -- Synchroniser l'état complet avec tous les joueurs
        TriggerClientEvent('asc_robbery:syncPaletoState', -1, paletoServerState)

    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Impossible d\'ajouter la récompense',
            type = 'error'
        })
    end
end)

-- Événement pour gérer l'échec du minigame de perçage
RegisterNetEvent('asc_robbery:paletoDrillFailed')
AddEventHandler('asc_robbery:paletoDrillFailed', function(vaultIndex)
    local src = source


    -- Vérifier que c'est bien le joueur qui a verrouillé ce coffre
    if paletoVaultLocks[vaultIndex] == src then
        paletoVaultLocks[vaultIndex] = nil -- Déverrouiller le coffre
    end
end)

-- Reset de l'état de Paleto Bank
local function resetPaletoState()
    paletoServerState = {
        wiresCut = false,
        c4DoorExists = true,
        mainDoorOpen = false,
        thermiteUsed = false,
        c4Used = false,
        vaultsDrilled = {}
    }

    -- Reset des verrous et coffres percés
    paletoVaultLocks = {}
    paletoVaultDrilled = {}

    -- Synchroniser le reset avec tous les joueurs
    TriggerClientEvent('asc_robbery:syncPaletoState', -1, paletoServerState)
end

-- Événement pour synchroniser l'état de Paleto Bank avec un joueur spécifique
RegisterNetEvent('asc_robbery:requestPaletoSync')
AddEventHandler('asc_robbery:requestPaletoSync', function()
    local src = source

    -- Envoyer l'état complet de Paleto Bank au joueur
    TriggerClientEvent('asc_robbery:syncPaletoState', src, paletoServerState)

    -- Envoyer l'état des coffres percés
    TriggerClientEvent('asc_robbery:syncPaletoVaults', src, paletoVaultDrilled)

end)

-- Fonction pour démarrer les cooldowns de Paleto Bank
local function startPaletoCooldowns()
    local paletoCooldownTime = Config.Paletobank.cooldown * 1000 -- Convertir en millisecondes
    local globalCooldownTime = Config.GlobalCooldown * 1000 -- Convertir en millisecondes


    -- Démarrer le cooldown de Paleto Bank
    paletoCooldownActive = true
    paletoCooldownTimer = SetTimeout(paletoCooldownTime, function()
        paletoCooldownActive = false
        paletoCooldownTimer = nil

        -- Reset complet de Paleto Bank après le cooldown
        resetPaletoState()
    end)

    -- Démarrer le cooldown global
    globalCooldownActive = true
    globalCooldownTimer = SetTimeout(globalCooldownTime, function()
        globalCooldownActive = false
        globalCooldownTimer = nil
    end)

end

-- Reset au démarrage de la ressource
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(2000)
        resetPaletoState()
    end
end)

-- Commande pour reset manuel de Paleto Bank
RegisterCommand('resetpaleto', function(source, args, rawCommand)
    if source == 0 then -- Console seulement
        -- Reset des cooldowns aussi (système unifié)
        paletoCooldownActive = false
        paletoCooldownStartTime = 0
        globalCooldown = false
        globalCooldownStore = nil
        cooldownTimers.global = nil
        if paletoCooldownTimer then
            ClearTimeout(paletoCooldownTimer)
            paletoCooldownTimer = nil
        end

        resetPaletoState()
    end
end, true)

-- Commande pour vérifier l'état des cooldowns de Paleto
RegisterCommand('paletostatus', function(source, args, rawCommand)
    if source == 0 then -- Console seulement

        -- Afficher le cooldown Paleto avec temps restant
        if paletoCooldownActive then
            local remainingTime = getRemainingCooldownTime('paleto')
            local timeFormatted = formatTime(remainingTime)
        else
        end

        -- Afficher le cooldown global avec temps restant (système unifié)
        if globalCooldown then
            local remainingTime = getRemainingCooldownTime('global')
            local timeFormatted = formatTime(remainingTime)
        else
        end

        local drilledCount = 0
        for _ in pairs(paletoVaultDrilled) do
            drilledCount = drilledCount + 1
        end
    end
end, true)

-- Événement pour synchroniser tous les braquages à la connexion d'un joueur
RegisterNetEvent('asc_robbery:requestFullSync')
AddEventHandler('asc_robbery:requestFullSync', function()
    local src = source

    -- Synchroniser l'état de Paleto Bank
    local paletoState = {
        wiresCut = paletoServerState.wiresCut,
        c4DoorExists = paletoServerState.c4DoorExists,
        mainDoorOpen = paletoServerState.mainDoorOpen,
        vaultsDrilled = paletoServerState.vaultsDrilled
    }

    TriggerClientEvent('asc_robbery:syncPaletoState', src, paletoState)

    -- Synchroniser l'état des autres banques si nécessaire
    for bankId, bankState in pairs(globalBankStates.doorStates) do
        if bankState then
            TriggerClientEvent('asc_robbery:syncBankDoorState', src, bankId, true)
        end
    end
end)

-- Événement déclenché quand un joueur se connecte
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local src = source

    -- Programmer la synchronisation après que le joueur soit complètement connecté
    CreateThread(function()
        Wait(5000) -- Attendre 5 secondes que le joueur soit complètement chargé

        -- Vérifier si le joueur est toujours connecté
        if GetPlayerName(src) then
            TriggerEvent('asc_robbery:requestFullSync')
        end
    end)
end)

-- Alternative avec playerJoining (plus récent)
AddEventHandler('playerJoining', function(source)
    local src = source

    -- Programmer la synchronisation après que le joueur soit complètement connecté
    CreateThread(function()
        Wait(5000) -- Attendre 5 secondes que le joueur soit complètement chargé

        -- Vérifier si le joueur est toujours connecté
        if GetPlayerName(src) then
            TriggerEvent('asc_robbery:requestFullSync')
        end
    end)
end)

----------------------------------------------- VANGELICO

-- État global du braquage Vangelico
local vangelicoState = {
    isActive = false,
    isHacked = false,
    vitrines = {}, -- [id] = {isOpened = false, isBusy = false}
    cooldown = false,
    cooldownTimer = nil
}

-- Initialisation des vitrines
for i, vitrine in pairs(Config.Vangelico.vitrines) do
    vangelicoState.vitrines[i] = {isOpened = false, isBusy = false}
end

-- Fonction pour reset l'état du braquage Vangelico
local function resetVangelico()
    vangelicoState.isActive = false
    vangelicoState.isHacked = false
    vangelicoState.cooldown = false
    if vangelicoState.cooldownTimer then
        ClearTimeout(vangelicoState.cooldownTimer)
        vangelicoState.cooldownTimer = nil
    end
    for i, _ in pairs(vangelicoState.vitrines) do
        vangelicoState.vitrines[i].isOpened = false
        vangelicoState.vitrines[i].isBusy = false
    end
    -- Déverrouille la porte principale à chaque reset
    exports.ox_doorlock:setDoorState(Config.Vangelico.mainDoorID, 0)
    TriggerClientEvent('pc_robbery:syncVangelico', -1, vangelicoState)
end

-- Ajout de la fonction utilitaire pour obtenir le timestamp global
local function getGlobalCooldownEnd()
    if cooldownTimers and cooldownTimers.global then
        return math.floor((cooldownTimers.global / 1000) + os.time() - (GetGameTimer() / 1000))
    end
    return nil
end

-- Synchronisation à la connexion d'un joueur
RegisterNetEvent('pc_robbery:requestVangelicoSync', function()
    local src = source
    print("[VANGELICO][SERVER] Sync demandée par", src)
    vangelicoState.globalCooldownEnd = getGlobalCooldownEnd()
    TriggerClientEvent('pc_robbery:syncVangelico', src, vangelicoState)
end)

-- Ajout de la sync du cooldown global à chaque sync Vangelico
local function syncVangelicoAll()
    vangelicoState.globalCooldownEnd = getGlobalCooldownEnd()
    TriggerClientEvent('pc_robbery:syncVangelico', -1, vangelicoState)
end

-- Démarrage du braquage
RegisterNetEvent('pc_robbery:startVangelico', function()
    -- Vérification du nombre de policiers en service
    local src = source
    local policeCount = getPoliceCount()
    local requiredPolice = Config.Vangelico.minPolice or 0
    if policeCount < requiredPolice then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Il n\'y a pas assez de policiers en service pour hacker le système de sécurité.',
            type = 'error'
        })
        TriggerClientEvent('pc_robbery:failStartHack', src)
        return
    end
    if vangelicoState.cooldown or vangelicoState.isHacked then
        TriggerClientEvent('pc_robbery:syncVangelico', source, vangelicoState)
        TriggerClientEvent('pc_robbery:failStartHack', src)
        return
    end
    vangelicoState.isHacked = true -- Empêche d'autres hacks simultanés
    syncVangelicoAll()
    TriggerClientEvent('pc_robbery:startHackMinigame', source)
end)

-- Hack réussi
RegisterNetEvent('pc_robbery:successHackVangelico', function()
    vangelicoState.isActive = true
    vangelicoState.isHacked = true
    -- Retirer l'item requis au joueur (ox_inventory)
    local src = source
    local item = Config.Vangelico.requiredItem
    local removed = exports.ox_inventory:RemoveItem(src, item, 1)
    -- Barrer la porte principale
    exports.ox_doorlock:setDoorState(Config.Vangelico.mainDoorID, 1) -- 2 = verrouillé
    -- Démarrer le cooldown dès le succès du hack
    if vangelicoState.cooldownTimer then
        ClearTimeout(vangelicoState.cooldownTimer)
        vangelicoState.cooldownTimer = nil
    end
    vangelicoState.cooldown = true
    vangelicoState.cooldownEnd = os.time() + (Config.Vangelico.cooldown or 300)
    syncVangelicoAll()
    -- Déclenchement du cooldown global
    cooldownTimers.global = GetGameTimer() + (Config.GlobalCooldown * 1000)
    globalCooldown = true
    globalCooldownStore = 'vangelico'
    SetTimeout(Config.GlobalCooldown * 1000, function()
        globalCooldown = false
        globalCooldownStore = nil
        cooldownTimers.global = nil
    end)
    -- Déverrouiller la porte principale après le cooldown mainDoorCooldown
    SetTimeout((Config.Vangelico.mainDoorCooldown or 30) * 1000, function()
        exports.ox_doorlock:setDoorState(Config.Vangelico.mainDoorID, 0) -- 0 = déverrouillé
    end)
    vangelicoState.cooldownTimer = SetTimeout((Config.Vangelico.cooldown or 300) * 1000, function()
        vangelicoState.cooldown = false
        vangelicoState.cooldownEnd = nil
        vangelicoState.cooldownTimer = nil
        -- Reset complet de l'état
        vangelicoState.isActive = false
        vangelicoState.isHacked = false
        for i, _ in pairs(vangelicoState.vitrines) do
            vangelicoState.vitrines[i].isOpened = false
            vangelicoState.vitrines[i].isBusy = false
            vangelicoState.vitrines[i].locked = false
        end
        syncVangelicoAll()
    end)
end)

-- Nouvel event pour reset isHacked si hack échoué ou annulé
RegisterNetEvent('pc_robbery:failHackVangelico', function()
    vangelicoState.isHacked = false
    syncVangelicoAll()
end)

-- Casser une vitrine
RegisterNetEvent('pc_robbery:breakVitrine', function(vitrineId)
    local src = source
    if not vangelicoState.isActive or not vangelicoState.isHacked then return end
    if not vangelicoState.vitrines[vitrineId] then return end
    if vangelicoState.vitrines[vitrineId].isOpened or vangelicoState.vitrines[vitrineId].isBusy then return end -- Protection stricte
    -- Protection serveur par joueur
    if vitrineLocks[vitrineId] and vitrineLocks[vitrineId] ~= src then
        TriggerClientEvent('ox_lib:notify', src, {title = 'Vitrine', description = 'Cette vitrine est déjà en train d\'être cassée par un autre joueur.', type = 'info'})
        return
    end
    -- Vérification capacité inventaire pour au moins une récompense
    local canCarry = false
    local rewards = Config.Vangelico.rewards
    for _, reward in ipairs(rewards) do
        if exports.ox_inventory:CanCarryItem(src, reward.name, reward.min or 1) then
            canCarry = true
            break
        end
    end
    if not canCarry then
        TriggerClientEvent('ox_lib:notify', src, {title = 'Vitrine', description = 'Vous n\'avez pas assez de place dans votre inventaire pour récupérer la récompense.', type = 'error'})
        return
    end
    vitrineLocks[vitrineId] = src
    vangelicoState.vitrines[vitrineId].isBusy = true
    vangelicoState.vitrines[vitrineId].locked = true
    syncVangelicoAll()
    -- Timer pour simuler le cassage
    SetTimeout(1000, function()
        vangelicoState.vitrines[vitrineId].isOpened = true
        vangelicoState.vitrines[vitrineId].isBusy = false
        vangelicoState.vitrines[vitrineId].locked = false
        vitrineLocks[vitrineId] = nil -- Libère le lock
        syncVangelicoAll()
        -- Récompense (toujours au moins une)
        local atLeastOne = false
        for idx, reward in ipairs(rewards) do
            local give = math.random(1, 100) <= (reward.chance or 100)
            if idx == #rewards and not atLeastOne then
                give = true
            end
            if give then
                local amount = math.random(reward.min, reward.max)
                TriggerEvent('pc_robbery:giveReward', src, reward.name, amount)
                atLeastOne = true
            end
        end
    end)
end)

-- Fin du braquage (manuelle ou toutes vitrines ouvertes)
RegisterNetEvent('pc_robbery:endVangelico', function()
    vangelicoState.isActive = false
    vangelicoState.isHacked = false
    vangelicoState.cooldown = true
    syncVangelicoAll()
end)

-- Donner la récompense (à adapter selon ton système d'item)
RegisterNetEvent('pc_robbery:giveReward', function(src, item, amount)
    local success = exports.ox_inventory:AddItem(src, item, amount)
    if success then
        TriggerClientEvent('ox_lib:notify', src, {title = 'Vitrine', description = 'Vous avez reçu ' .. amount .. 'x ' .. item, type = 'success'})
    else
    end
end)

-- Reset au démarrage de la ressource
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(2000)
        resetVangelico()
        -- Sécurité : déverrouille la porte principale au start de la ressource
        exports.ox_doorlock:setDoorState(Config.Vangelico.mainDoorID, 0)
    end
end)

-- Callback pour vérifier la capacité d'inventaire avant l'animation côté client
lib.callback.register('pc_robbery:canCarryVitrineReward', function(source, vitrineId)
    local rewards = Config.Vangelico.rewards
    for _, reward in ipairs(rewards) do
        if exports.ox_inventory:CanCarryItem(source, reward.name, reward.min or 1) then
            return true
        end
    end
    return false
end)

RegisterNetEvent('pc_robbery:policeUnlockVangelicoDoor', function()
    local src = source
    -- -- Optionnel : vérification du job police côté serveur (sécurité)
    -- local xPlayer = exports['qb-core']:GetPlayer(src)
    -- if not xPlayer or (xPlayer.PlayerData.job.name ~= 'police') then
    --     TriggerClientEvent('ox_lib:notify', src, {title = 'Vangelico', description = 'Accès refusé : police uniquement.', type = 'error'})
    --     return
    -- end
    exports.ox_doorlock:setDoorState(Config.Vangelico.mainDoorID, 0) -- déverrouille
    TriggerClientEvent('ox_lib:notify', src, {title = 'Vangelico', description = 'Porte principale déverrouillée !', type = 'success'})
end)
------------------------- DÉBUT PACIFIC
-- Pacific Bank: gestion du ped employé (synchronisé pour tous)
local pacificPedSpawned = false

RegisterNetEvent('pc_robbery:pacific:playerEnteredZone', function()
    if not pacificPedSpawned then
        pacificPedSpawned = true
        TriggerClientEvent('pc_robbery:pacific:spawnPed', -1, true)
    end
end)

RegisterNetEvent('pc_robbery:pacific:playerLeftZone', function()
    if pacificPedSpawned then
        pacificPedSpawned = false
        TriggerClientEvent('pc_robbery:pacific:spawnPed', -1, false)
    end
end)

AddEventHandler('playerJoining', function(playerId)
    if pacificPedSpawned then
        TriggerClientEvent('pc_robbery:pacific:spawnPed', playerId, true)
    end
end)

-- Pacific Bank: synchronisation du ped networked
RegisterNetEvent('pc_robbery:pacific:pedSpawned', function(netId)
    TriggerClientEvent('pc_robbery:pacific:setPed', -1, netId)
end)

-- Pacific Bank: gestion du code à 4 chiffres pour le braquage
local pacificBankCode = nil

RegisterNetEvent('pc_robbery:pacific:requestCode', function()
    local src = source
    if not pacificBankCode then
        pacificBankCode = math.random(1000, 9999)
    end
    TriggerClientEvent('pc_robbery:pacific:receiveCode', src, pacificBankCode)
end)

RegisterNetEvent('pc_robbery:pacific:tryCode', function(code)
    local src = source
    if pacificBankCode and tonumber(code) == pacificBankCode then
        TriggerClientEvent('pc_robbery:pacific:codeResult', src, true)
        -- Déverrouille la porte Pacific Bank
        local doorId = Config.Pacific.keypadDoorId
        if doorId then
            exports.ox_doorlock:setDoorState(doorId, 0) -- 0 = déverrouillé
        end
    else
        TriggerClientEvent('pc_robbery:pacific:codeResult', src, false)
    end
end)

RegisterNetEvent('pc_robbery:pacific:unlockC4Door', function(doorId)
    if doorId then
        exports.ox_doorlock:setDoorState(doorId, 0)
    end
end)

RegisterNetEvent('pc_robbery:pacific:giveAccessCard', function()
    local src = source
    exports.ox_inventory:AddItem(src, Config.Pacific.rewardCardCoords, 1)
end)

RegisterNetEvent('pc_robbery:pacific:broadcastDeleteCardProp', function()
    TriggerClientEvent('pc_robbery:pacific:deleteCardProp', -1)
end)

RegisterNetEvent('pc_robbery:pacific:broadcastDeleteVaultMoneyProp', function()
    TriggerClientEvent('pc_robbery:pacific:deleteVaultMoneyProp', -1)
end)

RegisterNetEvent('pc_robbery:pacific:unlockSecondaryDoor', function(doorId)
    if doorId then
        exports.ox_doorlock:setDoorState(doorId, 0)
    end
end)

RegisterNetEvent('pc_robbery:pacific:openVaultDoor', function(vaultDoorObject, heading)
    local src = source
    -- Envoyer l'event au client pour ouvrir la porte
    TriggerClientEvent('pc_robbery:pacific:openVaultDoorClient', src, vaultDoorObject, heading)
end)

RegisterNetEvent('pc_robbery:pacific:unlockSecondVaultDoor', function(doorId)
    if doorId then
        exports.ox_doorlock:setDoorState(doorId, 0) -- 0 = déverrouillé
    end
end)

-- Events pour les guards Pacific Bank
RegisterNetEvent('pc_robbery:pacific:spawnGuards', function()
    -- Spawn des guards pour tous les joueurs
    TriggerClientEvent('pc_robbery:pacific:spawnGuardsClient', -1)
end)

RegisterNetEvent('pc_robbery:pacific:deleteGuards', function()
    -- Supprimer les guards pour tous les joueurs
    TriggerClientEvent('pc_robbery:pacific:deleteGuardsClient', -1)
end)

-- Event pour vérifier le job du joueur
RegisterNetEvent('pc_robbery:pacific:checkPlayerJob', function()
    local src = source
    local xPlayer = QBCore.Functions.GetPlayer(src)
    
    if xPlayer then
        local isPolice = (xPlayer.PlayerData.job.name == "police" and xPlayer.PlayerData.job.onduty)
        TriggerClientEvent('pc_robbery:pacific:receivePlayerJob', src, isPolice)
    else
        TriggerClientEvent('pc_robbery:pacific:receivePlayerJob', src, false)
    end
end)

-- ... code existant ...
local pacificEntities = {
    trolleys = {},
    trolleysEmpty = {}, -- <--- pour les trolleys vides
    keypad = nil,
    card = nil,
    guards = {},
}

function SpawnPacificEntities()
    -- Trolleys
    for trolleyId, trolleyConfig in pairs(Config.Pacific.trolleys) do
        local coords = trolleyConfig.coords
        local model = GetHashKey(trolleyConfig.model)
        RequestModel(model)
        while not HasModelLoaded(model) do
            Wait(10)
        end
        local trolley = CreateObject(model, coords.x, coords.y, coords.z, true, true, true)
        SetEntityHeading(trolley, coords.w or 0.0)
        FreezeEntityPosition(trolley, true)
        if trolley and DoesEntityExist(trolley) then
            local netId = NetworkGetNetworkIdFromEntity(trolley)
            pacificEntities.trolleys[trolleyId] = netId
        end
    end
    -- Keypad
    local k = Config.Pacific.keypadCoords
    local keypadModel = GetHashKey('prop_ld_keypad_01')
    RequestModel(keypadModel)
    while not HasModelLoaded(keypadModel) do
        Wait(10)
    end
    local keypad = CreateObject(keypadModel, k.x, k.y, k.z, true, true, false)
    if k.rot then
        SetEntityRotation(keypad, k.rot.x or 0.0, k.rot.y or 0.0, k.rot.z or 0.0, 2, true)
    end
    FreezeEntityPosition(keypad, true)
    if keypad and DoesEntityExist(keypad) then
        pacificEntities.keypad = NetworkGetNetworkIdFromEntity(keypad)
    end
    -- Card
    local c = Config.Pacific.cardCoords
    local cardModel = GetHashKey('p_ld_id_card_01')
    RequestModel(cardModel)
    while not HasModelLoaded(cardModel) do
        Wait(10)
    end
    local card = CreateObject(cardModel, c.x, c.y, c.z, true, true, false)
    SetEntityRotation(card, c.rot.x, c.rot.y, c.rot.z, 2, true)
    FreezeEntityPosition(card, true)
    if card and DoesEntityExist(card) then
        pacificEntities.card = NetworkGetNetworkIdFromEntity(card)
    end
    -- Guards
    local guardModel = GetHashKey(Config.Pacific.guards.model)
    RequestModel(guardModel)
    while not HasModelLoaded(guardModel) do
        Wait(10)
    end
    for i, coords in ipairs(Config.Pacific.guards.coords) do
        local guard = CreatePed(4, guardModel, coords.x, coords.y, coords.z, coords.w, true, true)
        SetPedArmour(guard, 100)
        SetPedMaxHealth(guard, 200)
        SetEntityHealth(guard, 200)
        GiveWeaponToPed(guard, GetHashKey(Config.Pacific.guards.weapon), 500, false, true)
        SetPedAmmo(guard, GetHashKey(Config.Pacific.guards.weapon), 500)
        SetPedCombatAttributes(guard, 46, true)
        SetPedFleeAttributes(guard, 0, false)
        SetPedCombatRange(guard, 2)
        SetPedCombatMovement(guard, 3)
        SetPedCombatAbility(guard, 100)
        SetPedAccuracy(guard, 60)
        if guard and DoesEntityExist(guard) then
            local netId = NetworkGetNetworkIdFromEntity(guard)
            pacificEntities.guards[i] = netId
        end
    end
    -- Broadcast aux clients
    TriggerClientEvent('pc_robbery:pacific:syncEntities', -1, pacificEntities)
end

function DeletePacificEntities()
    -- Trolleys pleins
    for _, netId in pairs(pacificEntities.trolleys) do
        local ent = NetworkGetEntityFromNetworkId(netId)
        if ent and DoesEntityExist(ent) then DeleteEntity(ent) end
    end
    pacificEntities.trolleys = {}
    -- Keypad
    if pacificEntities.keypad then
        local ent = NetworkGetEntityFromNetworkId(pacificEntities.keypad)
        if ent and DoesEntityExist(ent) then DeleteEntity(ent) end
        pacificEntities.keypad = nil
    end
    -- Card
    if pacificEntities.card then
        local ent = NetworkGetEntityFromNetworkId(pacificEntities.card)
        if ent and DoesEntityExist(ent) then DeleteEntity(ent) end
        pacificEntities.card = nil
    end
    -- Guards
    for _, netId in pairs(pacificEntities.guards) do
        local ent = NetworkGetEntityFromNetworkId(netId)
        if ent and DoesEntityExist(ent) then DeleteEntity(ent) end
    end
    pacificEntities.guards = {}
    -- Trolleys vides
    for _, netId in pairs(pacificEntities.trolleysEmpty) do
        local ent = NetworkGetEntityFromNetworkId(netId)
        if ent and DoesEntityExist(ent) then DeleteEntity(ent) end
    end
    pacificEntities.trolleysEmpty = {}
end

AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        DeletePacificEntities()
        Wait(1000)
        SpawnPacificEntities()
    end
end)

RegisterNetEvent('pc_robbery:pacific:requestEntities', function()
    local src = source
    TriggerClientEvent('pc_robbery:pacific:syncEntities', src, pacificEntities)
end)
-- ... code existant ...

-- ... code existant ...
local pacificTrolleyLooted = {}

RegisterNetEvent('pc_robbery:pacific:giveTrolleyReward', function(trolleyId)
    local src = source
    if pacificTrolleyLooted[trolleyId] then
        TriggerClientEvent('ox_lib:notify', src, {title = 'Pacific Bank', description = 'Ce trolley a déjà été vidé !', type = 'error'})
        return
    end
    pacificTrolleyLooted[trolleyId] = true
    local trolleyConfig = Config.Pacific.trolleys[trolleyId]
    if not trolleyConfig or not trolleyConfig.reward then
        print('[Pacific Bank] Erreur: reward non défini pour le trolleyId', trolleyId)
        return
    end
    local reward = trolleyConfig.reward

    -- Vérifie la capacité d'inventaire pour chaque item bonus
    if reward.items then
        for _, item in ipairs(reward.items) do
            if math.random(1, 100) <= (item.chance or 100) then
                local count = math.random(item.min or 1, item.max or 1)
                if not exports.ox_inventory:CanCarryItem(src, item.name, count) then
                    TriggerClientEvent('ox_lib:notify', src, {title = 'Pacific Bank', description = "Inventaire plein, impossible de prendre l'item bonus : "..item.name, type = 'error'})
                    return
                end
            end
        end
    end

    -- Donne l'argent (argent sale ou cash selon ton système)
    if reward.money then
        local amount = math.random(reward.money.min, reward.money.max)
        exports.ox_inventory:AddItem(src, 'black_money', amount)
    end

    -- Donne les items avec leur chance
    if reward.items then
        for _, item in ipairs(reward.items) do
            if math.random(1, 100) <= (item.chance or 100) then
                local count = math.random(item.min or 1, item.max or 1)
                exports.ox_inventory:AddItem(src, item.name, count)
            end
        end
    end
    TriggerClientEvent('ox_lib:notify', src, {title = 'Pacific Bank', description = 'Argent récupéré !', type = 'success'})
end)
-- ... code existant ...

-- ... code existant ...
RegisterNetEvent('pc_robbery:pacific:replaceTrolleyWithEmpty', function(trolleyId, trollyNetId, coords, rot)
    -- Supprime le trolley plein (plus de contrôle réseau côté serveur)
    local ent = NetworkGetEntityFromNetworkId(trollyNetId)
    if ent and DoesEntityExist(ent) then
        DeleteEntity(ent)
    end
    -- Crée le trolley vide
    local emptyModel = GetHashKey("hei_prop_hei_cash_trolly_03")
    local empty = CreateObject(emptyModel, coords.x, coords.y, coords.z - 0.985, true, true, true)
    SetEntityRotation(empty, rot.x, rot.y, rot.z, 2, true)
    -- SetEntityAsMissionEntity(empty, true, true) -- SUPPRIMÉ
    local netId = NetworkGetNetworkIdFromEntity(empty)
    pacificEntities.trolleysEmpty[#pacificEntities.trolleysEmpty+1] = netId
    -- Broadcast à tous pour supprimer le plein localement
    TriggerClientEvent('pc_robbery:pacific:trolleyEmptied', -1, trollyNetId)
    -- (Optionnel) : tu peux stocker le netId du vide si tu veux le suivre
end)
-- ... code existant ...

-- Nouvelle table pour stocker les netIds reçus des clients
local pacificEntities = {
    trolleys = {},
    trolleysEmpty = {},
    keypad = nil,
    card = nil,
    guards = {},
}

-- Broadcast la config d'entités à tous les clients
function SyncPacificEntitiesConfig()
    TriggerClientEvent('pc_robbery:pacific:spawnEntities', -1, Config.Pacific)
end

-- Quand un client a créé une entité, il envoie son netId au serveur
RegisterNetEvent('pc_robbery:pacific:entitySpawned', function(entityType, entityId, netId)
    if entityType == 'trolley' then
        pacificEntities.trolleys[entityId] = netId
    elseif entityType == 'trolleyEmpty' then
        table.insert(pacificEntities.trolleysEmpty, netId)
    elseif entityType == 'keypad' then
        pacificEntities.keypad = netId
    elseif entityType == 'card' then
        pacificEntities.card = netId
    elseif entityType == 'guard' then
        pacificEntities.guards[entityId] = netId
    end
end)

-- Reset/despawn : broadcast aux clients de supprimer toutes les entités Pacific
function DeletePacificEntities()
    TriggerClientEvent('pc_robbery:pacific:deleteEntities', -1)
    pacificEntities = {
        trolleys = {},
        trolleysEmpty = {},
        keypad = nil,
        card = nil,
        guards = {},
    }
end

-- OnResourceStart : reset puis broadcast la config
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        DeletePacificEntities()
        Wait(1000)
        SyncPacificEntitiesConfig()
    end
end)

-- Event pour qu'un client demande la config (ex: nouveau joueur)
RegisterNetEvent('pc_robbery:pacific:requestEntities', function()
    local src = source
    TriggerClientEvent('pc_robbery:pacific:spawnEntities', src, Config.Pacific)
end)

-- (La logique métier, loot, anti-double, etc. reste inchangée)
-- ... code existant ...