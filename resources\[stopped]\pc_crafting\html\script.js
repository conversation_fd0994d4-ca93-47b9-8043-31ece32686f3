let currentRecipes = [];
let currentCategories = [];
let selectedRecipe = null;
let currentCategory = 'all';

// Écouteur pour les messages du client
window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.action === 'openCrafting') {
        resetInterface();
        
        currentRecipes = data.recipes || [];
        currentCategories = getUniqueCategories(currentRecipes);
        
        displayCategories();
        displayRecipes(currentRecipes);
        
        document.getElementById('craftingTitle').textContent = data.stationName || 'Crafting';
        document.getElementById('crafting-container').classList.remove('hidden');
    }
});

// Fermeture de l'interface
document.getElementById('closeButton').addEventListener('click', function() {
    closeCrafting();
});

// Ajout de la gestion des boutons + et - pour la quantité
window.addEventListener('DOMContentLoaded', function() {
    const input = document.getElementById('craftQuantity');
    const btnPlus = document.getElementById('quantityPlus');
    const btnMinus = document.getElementById('quantityMinus');
    const quantityMaxSpan = document.createElement('span');
    quantityMaxSpan.id = 'quantityMaxInfo';
    quantityMaxSpan.style.marginLeft = '8px';
    quantityMaxSpan.style.color = '#ffd700';
    if (input && input.parentNode) {
        input.parentNode.appendChild(quantityMaxSpan);
    }
    if (input && btnPlus && btnMinus) {
        btnPlus.addEventListener('click', function() {
            let val = parseInt(input.value) || 1;
            let max = parseInt(input.max) || 999;
            if (val < max) input.value = val + 1;
        });
        btnMinus.addEventListener('click', function() {
            let val = parseInt(input.value) || 1;
            if (val > 1) input.value = val - 1;
        });
        input.addEventListener('input', function() {
            let val = parseInt(input.value) || 1;
            let max = parseInt(input.max) || 999;
            if (val < 1) input.value = 1;
            if (val > max) input.value = max;
        });
    }
});

// Fonction pour obtenir les catégories uniques
function getUniqueCategories(recipes) {
    const categories = ['all'];
    recipes.forEach(recipe => {
        if (!categories.includes(recipe.category)) {
            categories.push(recipe.category);
        }
    });
    return categories;
}

// Fonction pour afficher les catégories
function displayCategories() {
    const categoriesContainer = document.querySelector('.crafting-categories');
    categoriesContainer.innerHTML = '';
    
    currentCategories.forEach(category => {
        const categoryButton = document.createElement('button');
        categoryButton.className = 'category-button';
        categoryButton.textContent = getCategoryDisplayName(category);
        categoryButton.dataset.category = category;
        
        if (category === currentCategory) {
            categoryButton.classList.add('active');
        }
        
        categoryButton.addEventListener('click', function() {
            document.querySelector('.category-button.active').classList.remove('active');
            this.classList.add('active');
            currentCategory = this.dataset.category;
            filterRecipes();
        });
        
        categoriesContainer.appendChild(categoryButton);
    });
}

// Fonction pour obtenir le nom d'affichage de la catégorie
function getCategoryDisplayName(category) {
    const categoryNames = {
        'all': 'Toutes',
        'weapons': 'Armes',
        'tools': 'Outils',
        'misc': 'Divers',
        'food': 'Nourriture',
        'drinks': 'Boissons',
        'medicine': 'Médecine'
    };
    
    return categoryNames[category] || category;
}

// Fonction pour filtrer les recettes
function filterRecipes() {
    let filteredRecipes = currentRecipes;
    
    if (currentCategory !== 'all') {
        filteredRecipes = currentRecipes.filter(recipe => recipe.category === currentCategory);
    }
    
    displayRecipes(filteredRecipes);
}

// Fonction pour afficher les recettes
function displayRecipes(recipes) {
    const recipesContainer = document.querySelector('.crafting-recipes');
    recipesContainer.innerHTML = '';
    
    recipes.forEach(recipe => {
        const recipeElement = document.createElement('div');
        recipeElement.className = 'recipe-card';
        recipeElement.dataset.recipeName = recipe.name;
        
        // Créer les tags d'ingrédients
        const ingredientTags = Object.keys(recipe.ingredients).map(ingredient => {
            const required = recipe.ingredients[ingredient];
            const available = recipe.playerIngredients[ingredient] || 0;
            const isAvailable = available >= required;
            const ingredientLabel = recipe.ingredientInfo && recipe.ingredientInfo[ingredient] ? recipe.ingredientInfo[ingredient].label : ingredient;
            
            return `<span class="ingredient-tag ${isAvailable ? 'available' : ''}">${ingredientLabel}: ${available}/${required}</span>`;
        }).join('');
        
        recipeElement.innerHTML = `
            <div class="recipe-header">
                <div class="recipe-info">
                    <img src="nui://ox_inventory/web/images/${recipe.result.item}.png" alt="${recipe.result.item}" class="recipe-result-image" onerror="this.style.display='none'">
                    <div>
                        <span class="recipe-name">${recipe.label}</span>
                        <span class="recipe-category">${getCategoryDisplayName(recipe.category)}</span>
                    </div>
                </div>
            </div>
            <p class="recipe-description">${recipe.description ? recipe.description : ''}</p>
            <div class="recipe-ingredients-preview">
                ${ingredientTags}
            </div>
        `;
        
        recipeElement.addEventListener('click', function() {
            selectRecipe(recipe);
        });
        
        recipesContainer.appendChild(recipeElement);
    });
}

// Fonction pour sélectionner une recette
function selectRecipe(recipe) {
    // Retirer la sélection précédente
    document.querySelectorAll('.recipe-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Sélectionner la nouvelle recette
    const selectedCard = document.querySelector(`[data-recipe-name="${recipe.name}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');
    }
    
    selectedRecipe = recipe;
    displayRecipeInfo(recipe);
}

// Fonction pour afficher les informations de la recette
function displayRecipeInfo(recipe) {
    const infoContainer = document.querySelector('.selected-recipe-info');
    const noSelectionContainer = document.querySelector('.no-recipe-selected');
    
    infoContainer.classList.remove('hidden');
    noSelectionContainer.classList.add('hidden');
    
    // Mettre à jour les informations
    document.getElementById('selectedRecipeName').textContent = recipe.label;
    const descriptionElement = document.getElementById('selectedRecipeDescription');
    if (recipe.description) {
        descriptionElement.textContent = recipe.description;
        descriptionElement.style.display = 'block';
    } else {
        descriptionElement.style.display = 'none';
    }
    const resultItemLabel = recipe.label || recipe.result.item;
    document.getElementById('recipeResult').innerHTML = `
        <img src="nui://ox_inventory/web/images/${recipe.result.item}.png" alt="${recipe.result.item}" class="result-item-image" onerror="this.style.display='none'">
        <span>${recipe.result.amount}x ${resultItemLabel}</span>
    `;
    document.getElementById('craftingTime').textContent = `${recipe.time} secondes`;
    
    // Afficher les ingrédients
    displayIngredients(recipe);
    
    // Mettre à jour le bouton de craft
    updateCraftButton(recipe);

    // Calculer le max craftable
    const quantityInput = document.getElementById('craftQuantity');
    const quantityMaxSpan = document.getElementById('quantityMaxInfo');
    let maxCraft = 999;
    if (quantityInput) {
        maxCraft = Infinity;
        for (const ingredient in recipe.ingredients) {
            const required = recipe.ingredients[ingredient];
            const available = recipe.playerIngredients[ingredient] || 0;
            const possible = Math.floor(available / required);
            if (possible < maxCraft) maxCraft = possible;
        }
        if (!isFinite(maxCraft) || maxCraft < 1) maxCraft = 1;
        quantityInput.max = maxCraft;
        if (parseInt(quantityInput.value) > maxCraft) quantityInput.value = maxCraft;
        if (quantityMaxSpan) {
            quantityMaxSpan.textContent = `/ ${maxCraft} max`;
        }
    }
}

// Fonction pour afficher les ingrédients
function displayIngredients(recipe) {
    const ingredientsList = document.getElementById('ingredientsList');
    ingredientsList.innerHTML = '';
    
    Object.keys(recipe.ingredients).forEach(ingredient => {
        const required = recipe.ingredients[ingredient];
        const available = recipe.playerIngredients[ingredient] || 0;
        const isSufficient = available >= required;
        const ingredientLabel = recipe.ingredientInfo && recipe.ingredientInfo[ingredient] ? recipe.ingredientInfo[ingredient].label : ingredient;
        
        const ingredientElement = document.createElement('div');
        ingredientElement.className = 'ingredient-item';
        ingredientElement.innerHTML = `
            <div class="ingredient-info">
                <img src="nui://ox_inventory/web/images/${ingredient}.png" alt="${ingredient}" class="ingredient-image" onerror="this.style.display='none'">
                <span class="ingredient-name">${ingredientLabel}</span>
            </div>
            <span class="ingredient-amount ${isSufficient ? '' : 'insufficient'}">${available}/${required}</span>
        `;
        
        ingredientsList.appendChild(ingredientElement);
    });
}

// Fonction pour mettre à jour le bouton de craft
function updateCraftButton(recipe) {
    const craftButton = document.getElementById('craftButton');
    
    if (recipe.canCraft) {
        craftButton.disabled = false;
        craftButton.textContent = 'Crafter';
        craftButton.innerHTML = '<i class="fas fa-hammer"></i> Crafter';
    } else {
        craftButton.disabled = true;
        craftButton.textContent = 'Ingrédients manquants';
        craftButton.innerHTML = '<i class="fas fa-times"></i> Ingédients manquants';
    }
}

// Fonction pour démarrer le crafting
document.getElementById('craftButton').addEventListener('click', function() {
    if (!selectedRecipe) return;
    
    if (!selectedRecipe.canCraft) {
        showNotification('error', 'Vous n\'avez pas assez d\'ingrédients');
        return;
    }
    
    // Récupérer la quantité à crafter
    const quantityInput = document.getElementById('craftQuantity');
    let quantity = 1;
    if (quantityInput && quantityInput.value) {
        quantity = Math.max(1, parseInt(quantityInput.value));
    }
    
    // Envoyer la demande de craft au client
    fetch(`https://${GetParentResourceName()}/startCrafting`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            recipeName: selectedRecipe.name,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('success', 'Crafting démarré!');
            closeCrafting();
        } else {
            showNotification('error', data.message || 'Erreur lors du crafting');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('error', 'Erreur de communication');
    });
});

// Fonction pour afficher une notification
function showNotification(type, message) {
    // Créer une notification temporaire
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Supprimer la notification après 3 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Fonction pour réinitialiser l'interface
function resetInterface() {
    selectedRecipe = null;
    currentCategory = 'all';
    
    document.querySelector('.selected-recipe-info').classList.add('hidden');
    document.querySelector('.no-recipe-selected').classList.remove('hidden');
}

// Fonction pour fermer l'interface
function closeCrafting() {
    document.getElementById('crafting-container').classList.add('hidden');
    resetInterface();
    
    fetch(`https://${GetParentResourceName()}/closeCrafting`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    });
}

// Gestion des touches
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeCrafting();
    }
});

// Styles pour les notifications
const notificationStyles = `
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background-color: rgba(100, 255, 100, 0.9);
    border: 1px solid rgba(100, 255, 100, 0.5);
}

.notification.error {
    background-color: rgba(255, 100, 100, 0.9);
    border: 1px solid rgba(255, 100, 100, 0.5);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-content i {
    font-size: 16px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
`;

// Ajouter les styles de notification
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet); 