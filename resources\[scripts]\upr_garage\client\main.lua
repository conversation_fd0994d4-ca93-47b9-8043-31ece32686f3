CreateThread(function()
    Core, Config.Framework = GetCore()
end)
nuiLoaded = false
local garageZones = {}
local listenForKey = false
vehiclesData = {}
local key = false
CreateThread(function()
    while Core == nil do
        Wait(0)
    end
    while not nuiLoaded do
        if NetworkIsSessionStarted() then
            SendNUIMessage({
                action = "CHECK_NUI",
            })
        end

        if Config.Framework == 'qb' or Config.Framework == 'qbx' or Config.Framework == 'oldqb' then
            vehiclesData = Vehicles
        else
            vehiclesData = Vehicles
        end
        Wait(2000)
    end
end)


Citizen.CreateThread(function()
    while Core == nil and not nuiLoaded do
        Citizen.Wait(0)
    end
    NuiMessage('SERVER_NAME', Config.ServeName)
    NuiMessage('LOCALES', Config.Locales)
    NuiMessage('SERVER_MONEY_TYPE', Config.MoneyType)
end)

function checkNUI()
    while not nuiLoaded do
        Wait(0)
    end
end

function NuiMessage(action, payload)
    checkNUI()
    SendNUIMessage({
        action = action,
        payload = payload
    })
end

RegisterNUICallback("LoadedNUI", function(data, cb)
    nuiLoaded = true
    cb("ok")
end)

RegisterNetEvent("esx:playerLoaded")
AddEventHandler("esx:playerLoaded", function()
    Wait(1000)
    CreateBlipsZones()
end)

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    CreateBlipsZones()
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    CreateBlipsZones()
end)

AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    CreateBlipsZones()
end)

RegisterNetEvent('QBCore:Client:OnGangUpdate', function(gang)
    PlayerGang = gang
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(job)
    PlayerJob = job
end)

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    CleanupVehicleAndCamera()
end)

RegisterNUICallback('closeNUI', function()
    SetNuiFocus(false, false)
    CleanupVehicleAndCamera()
end)

function OpenGarageMenu(data)
    local result = TriggerCallback('tworst-garages:server:GetGarageVehicles', data)
    local playerData = TriggerCallback('tworst-garbage:server:getPlayerData')

    if result == nil then
        return sendNotification(Config.NotificationText['notingarage'].text,
            Config.NotificationText['notingarage'].type)
    end

    if playerData == nil then return sendNotification('PlayerData NotFound', 'errorNotify') end

    local formattedVehicles = {}
    if Config.vehicleSQL == 'player_vehicles' then
        for _, v in pairs(result) do
            local enginePercent = round(v.engine, 0)
            local bodyPercent = round(v.body, 0)
            local vname = nil
            local vehicle = v.vehicle
            local state = v.state
            pcall(function()
                vname = vehiclesData[vehicle].name
            end)
            if vehiclesData[vehicle] and vehiclesData[vehicle].brand then
                vname = vehiclesData[vehicle].brand .. " " .. vehiclesData[vehicle].name
            else
                vname = vehicle
            end
            local mods = json.decode(v.mods) or {}
            local shared = json.decode(v.shared)
            local owneridentifier = v.citizenid or v.identifier
            vname = v.vehicleLabel or vname
            local trueGarage = true
            if v.garage ~= data.indexgarage then
                trueGarage = false
            end
            formattedVehicles[#formattedVehicles + 1] = {
                vehicle = vehicle,
                vehicletype = vehiclesData[vehicle] and vehiclesData[vehicle].type or 'Unkown',
                vehClass = GetVehicleClassFromName(GetHashKey(vehicle)),
                vehicleLabel = vname or vehicle,
                plate = v.plate,
                plateIndex = mods.plateIndex or 0,
                state = state,
                fuel = v.fuel or 0,
                engine = enginePercent,
                body = bodyPercent,
                distance = v.drivingdistance or 0,
                garage = Config.Garages[data.indexgarage],
                type = data.type,
                index = data.indexgarage,
                depotPrice = v.depotprice or 0,
                balance = v.balance or 0,
                shared = shared,
                selectedVehicle = false,
                owneridentifier = owneridentifier,
                trueGarage = trueGarage
            }
        end
    else
        for _, v in pairs(result) do
            local mods = json.decode(v.vehicle) or {}
            local enginePercent = round(mods.engineHealth, 0) or mods.engineHealth
            local bodyPercent = round(mods.bodyHealth, 0) or mods.bodyHealth
            local vname = nil
            local vehicle = string.lower(GetDisplayNameFromVehicleModel(mods.model))
            local state = v.stored

            pcall(function()
                vname = vehiclesData[vehicle].name
            end)
            if vehiclesData[vehicle] and vehiclesData[vehicle].brand then
                vname = vehiclesData[vehicle].brand .. " " .. vehiclesData[vehicle].name
            else
                vname = vehicle
            end

            local shared = json.decode(v.shared) or {}
            local owneridentifier = v.owner or v.identifierf
            vname = v.vehicleLabel or vname
            local trueGarage = true
            if v.parking ~= data.indexgarage then
                trueGarage = false
            end
            formattedVehicles[#formattedVehicles + 1] = {
                vehicle = vehicle,
                vehicletype = vehiclesData[vehicle] and vehiclesData[vehicle].type or 'Unkown',
                vehClass = GetVehicleClassFromName(GetHashKey(vehicle)),
                vehicleLabel = vname or vehicle,
                plate = mods.plate,
                plateIndex = mods.plateIndex or 0,
                state = state,
                fuel = mods.fuelLevel or 0,
                engine = enginePercent,
                body = bodyPercent,
                garage = Config.Garages[data.indexgarage],
                type = data.type,
                depotPrice = v.depotprice or 0,
                index = data.indexgarage,
                shared = shared,
                selectedVehicle = false,
                owneridentifier = owneridentifier,
                trueGarage = trueGarage
            }
        end
    end
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'OPEN_GARAGE',
        garageLabel = Config.Garages[data.indexgarage].label,
        vehicles = formattedVehicles,
        vehNum = #formattedVehicles,
        resourceName = GetCurrentResourceName(),
        garageType = data.type,
        playerData = playerData
    })
end

function DepositVehicle(veh, data)
    local plate = GetVehicleNumberPlateText(veh)
    local clientCallbackData = {}
    clientCallbackData.plate = plate
    clientCallbackData.indexgarage = data.indexgarage
    clientCallbackData.garage = data.indexgarage
    clientCallbackData.type = data.type
    clientCallbackData.state = 1

    local canDeposit = TriggerCallback('tworst-garages:server:canDeposit', clientCallbackData)
    if canDeposit then
        if Config.vehicleSQL == 'owned_vehicles' then
            local vehicleProps = GetVehicleProperties(veh)
            TriggerServerEvent('upr_garage:updateOwnedVehicle', plate, vehicleProps)
            local model = GetDisplayNameFromVehicleModel(GetEntityModel(veh))
            Config.RemoveVehicleKey(plate, veh, model)
            CheckPlayers(veh)
            sendNotification(Config.NotificationText['succesparked'].text, Config.NotificationText['succesparked'].type)
        else
            local bodyDamage = math.ceil(GetVehicleBodyHealth(veh))
            local engineDamage = math.ceil(GetVehicleEngineHealth(veh))
            local totalFuel = Config.getFuel(veh)
            local props = GetVehicleProperties(veh)
            --TriggerServerEvent('qb-mechanicjob:server:SaveVehicleProps', GetVehicleProperties(veh))
            TriggerServerEvent('tworst-garages:server:updateVehicleStats', plate, totalFuel, engineDamage, bodyDamage,
                props)
            local model = GetDisplayNameFromVehicleModel(GetEntityModel(veh))
            Config.RemoveVehicleKey(plate, veh, model)
            CheckPlayers(veh)
            sendNotification(Config.NotificationText['succesparked'].text,
                Config.NotificationText['succesparked'].type)
        end
    else
        sendNotification(Config.NotificationText['vehiclenotyours'].text, Config.NotificationText['vehiclenotyours']
            .type)
    end
end

function CreateBlips(setloc)
    local Garage = AddBlipForCoord(setloc.takeVehicle.x, setloc.takeVehicle.y, setloc.takeVehicle.z)
    SetBlipSprite(Garage, setloc.blipNumber)
    SetBlipDisplay(Garage, 4)
    SetBlipScale(Garage, 0.60)
    SetBlipAsShortRange(Garage, true)
    SetBlipColour(Garage, setloc.blipColor)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentSubstringPlayerName(setloc.blipName)
    EndTextCommandSetBlipName(Garage)
end

function CreateZone(index, garage, zoneType, job)
    if zoneType == "job" then
        local zone = CircleZone:Create(garage.takeVehicle, 8.0, {
            name = zoneType .. '_' .. index,
            debugPoly = false,
            useZ = true,
            data = {
                indexgarage = index,
                type = garage.type,
                category = garage.category,
                job = job,
                takeVehicle = garage.takeVehicle
            }
        })
        return zone
    else
        local zone = CircleZone:Create(garage.takeVehicle, 8.0, {
            name = zoneType .. '_' .. index,
            debugPoly = false,
            useZ = true,
            data = {
                indexgarage = index,
                type = garage.type,
                category = garage.category,
                takeVehicle = garage.takeVehicle
            }
        })
        return zone
    end
end

local inZone = false
local displayCoords = false
function CreateBlipsZones()
    while Core == nil do Citizen.Wait(0) end
    PlayerData = GetPlayerData()
    PlayerGang = PlayerData.gang
    PlayerJob = PlayerData.job
    for index, garage in pairs(Config.Garages) do
        local zone
        if garage.showBlip then
            CreateBlips(garage)
        end
        if garage.type == 'job' or garage.type == 'gang' then
            zone = CreateZone(index, garage, 'job', garage.job)
        elseif garage.type == 'depot' then
            zone = CreateZone(index, garage, 'depot')
        elseif garage.type == 'public' then
            zone = CreateZone(index, garage, 'public')
        end
        if zone then
            garageZones[#garageZones + 1] = zone
        end
    end
    local comboZone = ComboZone:Create(garageZones, { name = 'garageCombo', debugPoly = false })
    comboZone:onPlayerInOut(function(isPointInside, _, zone)
        if isPointInside then
            if zone.data.job then
                if GetPlayerData().job.name == zone.data.job or GetPlayerData().gang.name == zone.data.job then
                else
                    return false
                end
            end
            listenForKey = true
            CreateThread(function()
                while listenForKey do
                    Citizen.Wait(0)
                    if IsControlJustReleased(0, 38) then
                        if GetVehiclePedIsUsing(PlayerPedId()) ~= 0 then
                            if zone.data.type == 'depot' then return end
                            local currentVehicle = GetVehiclePedIsUsing(PlayerPedId())
                            if not IsVehicleAllowed(zone.data.category, currentVehicle) then
                                sendNotification(Config.NotificationText['notmach'].text,
                                    Config.NotificationText['notmach'].type)
                                return
                            end
                            DepositVehicle(currentVehicle, zone.data)
                        else
                            OpenGarageMenu(zone.data)
                        end
                    end
                end
            end)

            local displayText = Config.Locales['garage']
            if zone.data.vehicle == 'sea' then
                displayText = Config.Locales['garagesea']
            elseif zone.data.vehicle == 'air' then
                displayText = Config.Locales['garageair']
            elseif zone.data.vehicle == 'rig' then
                displayText = Config.Locales['garage']
            elseif zone.data.type == 'depot' then
                displayText = Config.Locales['depot']
            end
             SendNUIMessage({ action = "textUI", show = true, text = displayText })

            --key = exports['ZSX_UIV2']:TextUI('E', displayText)

        else
            listenForKey = false
             SendNUIMessage({ action = "textUI", show = false })
            if key then
                exports['ZSX_UIV2']:TextUI_Remove(key, false)
            end
        end
    end)
end



Citizen.CreateThread(function()
    while true do
        local sleep = 1000
        local playerCoords = GetEntityCoords(PlayerPedId())
        
        for k, v in pairs(Config.Garages) do
            local markerCoords = vector3(v.takeVehicle.x, v.takeVehicle.y, v.takeVehicle.z)
            local distance = #(markerCoords - playerCoords)
            if distance < Config.GarageMarker.drawDistance then
                sleep = 0
                DrawMarker(
                    Config.GarageMarker.markerType, 
                    markerCoords.x, markerCoords.y, markerCoords.z, 
                    0, 0, 0, 
                    0, 0, 0, 
                    Config.GarageMarker.size.x, Config.GarageMarker.size.y, Config.GarageMarker.size.z, 
                    Config.GarageMarker.color.r, Config.GarageMarker.color.g, Config.GarageMarker.color.b, 200, 
                    false, false, 2, false, nil, nil, false
                )
            end
        end
        
        Citizen.Wait(sleep)
    end
end)



function doCarDamage(currentVehicle, stats, props)
    local engine = stats.engine + 0.0
    local body = stats.body + 0.0
    SetVehicleEngineHealth(currentVehicle, engine)
    SetVehicleBodyHealth(currentVehicle, body)
    if not next(props) then return end
    if props.doorStatus then
        for k, v in pairs(props.doorStatus) do
            if v then SetVehicleDoorBroken(currentVehicle, tonumber(k), true) end
        end
    end
    if props.tireBurstState then
        for k, v in pairs(props.tireBurstState) do
            if v then SetVehicleTyreBurst(currentVehicle, tonumber(k), true) end
        end
    end
    if props.windowStatus then
        for k, v in pairs(props.windowStatus) do
            if not v then SmashVehicleWindow(currentVehicle, tonumber(k)) end
        end
    end
end

function GetSpawnPoint(garage)
    local location = nil
    if #garage.spawnPoint > 0 then
        table.sort(garage.spawnPoint, function(a, b)
            local playerPos = GetEntityCoords(PlayerPedId())
            local distA = #(vector3(a.x, a.y, a.z) - playerPos)
            local distB = #(vector3(b.x, b.y, b.z) - playerPos)
            return distA < distB
        end)

        for _, spawnPoint in ipairs(garage.spawnPoint) do
            local isOccupied = IsPositionOccupied(
                spawnPoint.x,
                spawnPoint.y,
                spawnPoint.z,
                5.0,
                false,
                true,
                false,
                false,
                false,
                0,
                false
            )
            if not isOccupied then
                location = spawnPoint
                break
            end
        end
    end
    if not location then
        sendNotification(Config.NotificationText['notingarage'].text, Config.NotificationText['notingarage'].type)
    end
    return location
end

RegisterNetEvent('tworst-garages:client:trackVehicle', function(coords)
    SetNewWaypoint(coords.x, coords.y)
end)

function CheckPlate(vehicle, plateToSet)
    local vehiclePlate = promise.new()
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(500)
            if GetVehicleNumberPlateText(vehicle) == plateToSet then
                vehiclePlate:resolve(true)
                return
            else
                SetVehicleNumberPlateText(vehicle, plateToSet)
            end
        end
    end)
    return vehiclePlate
end

RegisterNetEvent('tworst-garages:client:takeOutGarage', function(data)
    CleanupVehicleAndCamera()
    local spawn = TriggerCallback('tworst-garages:server:IsSpawnOk', data)

    if spawn then
        local isvehiclevalid = false
        local vehs = GetVehicles()
        for k, v in pairs(vehs) do
            if GetVehicleNumberPlateText(v) == data.plate then
                isvehiclevalid = true
                break
            end
        end
        if isvehiclevalid then
            sendNotification(Config.NotificationText['notbeparedWaypoint'].text,
                Config.NotificationText['notbeparedWaypoint'].type)
            return
        end

        local location = GetSpawnPoint(Config.Garages[data.index])
        if not location then return end
        local NearbyVehicles = IsSpawnPointClear(location, 3.0)
        if not NearbyVehicles then return end
        local callbackData = {
            plate = data.plate,
            vehicle = data.vehicle,
            location = location,
            coords = location
        }

        local value = TriggerCallback('tworst-garages:server:spawnvehicle', callbackData)
        if value then
            local model = GetHashKey(data.vehicle)
            RequestModel(model)
            while not HasModelLoaded(model) do
                Wait(100)
            end
            veh = CreateVehicle(model, location.x, location.y, location.z, location.w, true, true)
            if veh == 0 then return end

            SetVehicleProperties(veh, value.vehProps)

            if Config.vehicleSQL == 'player_vehicles' then
                Citizen.Await(CheckPlate(veh, value.plate))
            end

            SetVehicleEngineOn(veh, true, true)
            SetEntityAsMissionEntity(veh, true, true)
            SetVehicleHasBeenOwnedByPlayer(veh, true)
            SetVehRadioStation(veh, "OFF")
            Config.setFuel(veh, data.fuel)
            Config.GiveVehicleKey(value.plate, veh)
            if Config.Warp then TaskWarpPedIntoVehicle(PlayerPedId(), veh, -1) end
            local stats = {
                engine = data.engine or 1000,
                body = data.body or 1000,
                fuel = data.fuel or 100
            }
            if Config.VisuallyDamageCars then doCarDamage(veh, stats, value.vehProps) end
            SetVehicleEngineOn(veh, true, true, false)
            TriggerServerEvent('tworst-garages:server:updateVehicleState', 0, data.plate)
        else
            sendNotification(Config.NotificationText['vehiclenotyours'].text,
                Config.NotificationText['vehiclenotyours'].type)
        end
    else
        local found = findVehFromPlateAndLocate(data.plate)
        if found then
            sendNotification(Config.NotificationText['notbeparedWaypoint'].text, Config.NotificationText['notbeparedWaypoint'].type)
        else
            sendNotification(Config.NotificationText['notbepared'].text, Config.NotificationText['notbepared'].type)
        end
    end
end)

function findVehFromPlateAndLocate(plate)
    local cleanedPlate = plate:gsub("%s+", "")

    local gameVehicles = GetVehicles()
    local found = false

    for i = 1, #gameVehicles do
        local vehicle = gameVehicles[i]
        if DoesEntityExist(vehicle) then
            local vehiclePlate = GetVehicleNumberPlateText(vehicle)
            local cleanedVehiclePlate = vehiclePlate:gsub("%s+", "")

            if tostring(cleanedVehiclePlate) == tostring(cleanedPlate) then
                local vehCoords = GetEntityCoords(vehicle)
                vehicleBlip = AddBlipForEntity(vehicle)
                SetBlipFlashes(vehicleBlip, true)
                SetBlipColour(vehicleBlip, 5)
                SetNewWaypoint(vehCoords.x, vehCoords.y)
                found = true

                Citizen.SetTimeout(60000, function()
                    if vehicleBlip ~= nil then
                        RemoveBlip(vehicleBlip)
                        vehicleBlip = nil
                    end
                end)
                break
            end
        end
    end
    return found
end

function GetVehicles()
    local vehicles = {}
    local veh = GetGamePool('CVehicle')
    for k, v in pairs(veh) do
        table.insert(vehicles, v)
    end

    return vehicles
end

RegisterNUICallback("takeOutVehicle", function(data)
    if not data then return end
    TriggerEvent('tworst-garages:client:takeOutGarage', data)
end)

RegisterNUICallback("takeOutDepot", function(data)
    if not data then return end
    local depotPrice = tonumber(data.depotPrice)
    if depotPrice ~= 0 then
        TriggerServerEvent('tworst-garages:server:PayDepotPrice', data)
    else
        TriggerEvent('tworst-garages:client:takeOutGarage', data)
    end
end)

RegisterNUICallback('changeVehicleName', function(data, cb)
    if not data then return end
    local result = TriggerCallback('tworst-garages:server:changeVehicleName', data)
    cb(result)
end)

RegisterNUICallback("shareVehicleKeys", function(data, cb)
    if not data then return end
    local result = TriggerCallback('tworst-garages:server:shareVehicleKeys', data)
    cb(result)
end)

RegisterNUICallback("removeSharedPlayer", function(data, cb)
    if not data then return end
    local result = TriggerCallback('tworst-garages:server:removeSharedPlayer', data)
    cb(result)
end)


local vehCam = false
local vehicle = nil
local camera = nil
local vehicleRotation = 0.0
local rotateLeft = false
local rotateRight = false

function SpawnVehicleWithCamera(vehicleData)
    local model = GetHashKey(vehicleData.vehicle)

    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(500)
    end
    if DoesEntityExist(vehicle) then
        DeleteEntity(vehicle)
    end

    local vehicleSpawnCoords, vehicleSpawnHeading

    if Config.Garages[vehicleData.index].showcar then
        vehicleSpawnCoords = vector3(Config.Garages[vehicleData.index].showcar.x,
            Config.Garages[vehicleData.index].showcar.y,
            Config.Garages[vehicleData.index].showcar.z)
        vehicleSpawnHeading = Config.Garages[vehicleData.index].showcar.w or 0.0
    else
        vehicleSpawnCoords = vector3(Config.Garages[vehicleData.index].spawnPoint[1].x,
            Config.Garages[vehicleData.index].spawnPoint[1].y,
            Config.Garages[vehicleData.index].spawnPoint[1].z)
        vehicleSpawnHeading = Config.Garages[vehicleData.index].spawnPoint[1].w or 0.0
    end

    local value = TriggerCallback('tworst-garages:server:getVehicleProperties', vehicleData.plate)
    if value then
        if Config.getAllVehicles and not vehicleData.trueGarage and not vehicleData.type == 'depot' then
            local garageName = Config.Garages[vehicleData.index].label
            NuiMessage('updateGarageName', garageName)
            return
        end
        if IsSpawnPointClear(vehicleSpawnCoords, 5.0) then
            RequestModel(model)
            while not HasModelLoaded(model) do
                Wait(100)
            end

            vehicle = CreateVehicle(model, vehicleSpawnCoords.x, vehicleSpawnCoords.y, vehicleSpawnCoords.z,
                vehicleSpawnHeading, false, false)
            SetVehicleNumberPlateText(vehicle, vehicleData.plate)
            SetVehicleOnGroundProperly(vehicle)
            SetEntityInvincible(vehicle, true)
            SetVehicleDoorsLocked(vehicle, 2)
            SetVehicleDoorsLockedForAllPlayers(vehicle, true)
            SetVehicleDoorsLockedForPlayer(vehicle, PlayerId(), true)
            SetEntityAsMissionEntity(vehicle, true, true)
            SetModelAsNoLongerNeeded(model)
            SetVehicleEngineOn(vehicle, true, true)
            SetVehicleProperties(vehicle, value)
            local stats, topspeed, garageName = calculateStats(vehicle, vehicleData.index)
            SendNUIMessage({
                action = "updateVehicleStats",
                payload = { stats = stats, topspeed = topspeed, garageName = garageName }
            })

            if not vehCam then
                camera = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
                if Config.Garages[vehicleData.index].camera then
                    local cameraCoords = vector3(Config.Garages[vehicleData.index].camera.x,
                        Config.Garages[vehicleData.index].camera.y,
                        Config.Garages[vehicleData.index].camera.z)
                    SetCamCoord(camera, cameraCoords.x, cameraCoords.y, cameraCoords.z)
                    SetCamRot(camera, Config.Garages[vehicleData.index].camera.rotationX,
                        Config.Garages[vehicleData.index].camera.rotationY,
                        Config.Garages[vehicleData.index].camera.rotationZ)
                else
                    local cameraOffsetDistance = 7.0
                    local cameraHeight = 2.0

                    local offsetX = math.sin(math.rad(vehicleSpawnHeading)) * cameraOffsetDistance * -1
                    local offsetY = math.cos(math.rad(vehicleSpawnHeading)) * cameraOffsetDistance * -1

                    local cameraCoords = vector3(vehicleSpawnCoords.x + offsetX,
                        vehicleSpawnCoords.y + offsetY,
                        vehicleSpawnCoords.z + cameraHeight)

                    SetCamCoord(camera, cameraCoords.x, cameraCoords.y, cameraCoords.z)

                    SetCamRot(camera, 0.0, 0.0, vehicleSpawnHeading - 180.0, 2)
                    PointCamAtEntity(camera, vehicle, 0.0, 0.0, 0.0, true)
                end

                SetCamActive(camera, true)
                RenderScriptCams(1, 1, 750, 1, 1)
                vehCam = true
            end
        else
            DeleteVehicle(vehicle)
        end
    end
end

function CleanupVehicleAndCamera()
    transitionActive = false
    if vehCam then
        SetCamActive(camera, false)
        RenderScriptCams(false, false, 0, true, true)
        DestroyCam(camera, true)

        if DoesEntityExist(vehicle) then
            DeleteEntity(vehicle)
        end

        camera = nil
        vehicle = nil
        vehCam = false
        rotateLeft = false
        rotateRight = false

        EnableAllControlActions(0)
    end
end

RegisterNUICallback("rotateVehicle", function(data, cb)
    if vehCam and vehicle and data.direction and data.action then
        if data.direction == "left" then
            if data.action == "start" then
                rotateLeft = true
            elseif data.action == "stop" then
                rotateLeft = false
            end
        elseif data.direction == "right" then
            if data.action == "start" then
                rotateRight = true
            elseif data.action == "stop" then
                rotateRight = false
            end
        elseif data.direction == "down" then
            if data.action == "start" then
                if camera then
                    local fov = GetCamFov(camera)
                    if fov < 90 then
                        SetCamFov(camera, fov + 2.0)
                    end
                end
            elseif data.action == "stop" then

            end
        elseif data.direction == "up" then
            if camera then
                if data.action == "start" then
                    local fov = GetCamFov(camera)
                    if fov > 20 then
                        SetCamFov(camera, fov - 2.0)
                    end
                elseif data.action == "stop" then

                end
            end
        end
    end
    cb('ok')
end)

CreateThread(function()
    while true do
        Wait(0)
        if vehCam and vehicle then
            if rotateLeft then
                vehicleRotation = vehicleRotation + 1.5
                if vehicleRotation >= 360.0 then
                    vehicleRotation = vehicleRotation - 360.0
                end
                SetEntityHeading(vehicle, vehicleRotation)
            elseif rotateRight then
                vehicleRotation = vehicleRotation - 1.5
                if vehicleRotation < 0.0 then
                    vehicleRotation = vehicleRotation + 360.0
                end
                SetEntityHeading(vehicle, vehicleRotation)
            else
                Wait(100)
            end
        else
            Wait(500)
        end
    end
end)

RegisterNUICallback("vehicleCameraPOW", function(data)
    if data == false then
        CleanupVehicleAndCamera()
    else
        SpawnVehicleWithCamera(data)
    end
end)


function getField(field, vehicle)
    return GetVehicleHandlingFloat(vehicle, 'CHandlingData', field)
end

local statsCache = {}
function calculateStats(vehicle, index)
    local model = GetEntityModel(vehicle)
    local info = statsCache[model]

    if info then
        return info.info, info.topSpeed, info.garageName
    end

    info = {}

    local braking = getField('fBrakeForce', vehicle) * getField('fBrakeBiasFront', vehicle) * 100
    local acceleration = getField('fInitialDriveMaxFlatVel', vehicle) * getField('fInitialDriveForce', vehicle)
    local handling = (getField('fSteeringLock', vehicle) + getField('fTractionBiasFront', vehicle) - getField('fTractionLossMult', vehicle)) *
        2

    info = {
        braking = {
            value = braking,
        },
        acceleration = {
            value = acceleration,
        },
        handling = {
            value = handling,
        },
    }

    local topSpeedKM = math.floor(GetVehicleEstimatedMaxSpeed(vehicle) * 3.6)
    if tonumber(topSpeedKM) == 0 then
        topSpeedKM = 0
    end
    local garageName = Config.Garages[index].label
    statsCache[model] = { info = info, topSpeed = topSpeedKM, garageName = garageName }
    return info, topSpeedKM, garageName
end
