local QBCore = exports['qb-core']:GetCoreObject()
local isUIOpen = false
local currentShop = nil
local shopPeds = {}
local shopBlips = {}

-- Importer ox_lib
local lib = exports.ox_lib

-- Variable pour stocker le callback en attente
local pendingCallback = nil

-- Fonction pour vérifier les permissions du job
local function HasJobPermission(shopData)
    if not shopData.jobs then return true end -- Si pas de restrictions de jobs, tout le monde peut accéder
    
    local Player = QBCore.Functions.GetPlayerData()
    if not Player then return false end
    
    local playerJob = Player.job.name
    local playerGrade = Player.job.grade.level
    
    for jobName, requiredGrade in pairs(shopData.jobs) do
        if playerJob == jobName and playerGrade >= requiredGrade then
            -- Vérifier si le shop est fermé quand le job est en service
            if Config.EnableJobServiceCheck and shopData.jobsClosedOnDuty and shopData.jobsClosedOnDuty[playerJob] and Player.job.onduty then
                return false
            end
            return true
        end
    end
    
    return false
end

-- Fonction pour vérifier si un shop est accessible (pour tous les shops)
local function IsShopAccessible(shopData)
    local Player = QBCore.Functions.GetPlayerData()
    if not Player then return false end
    
    -- Si le shop a des restrictions de jobs, utiliser HasJobPermission
    if shopData.jobs then
        return HasJobPermission(shopData)
    end
    
    -- Si pas de restrictions de jobs, vérifier seulement si le shop est fermé en service pour le job du joueur
    if Config.EnableJobServiceCheck and shopData.jobsClosedOnDuty and shopData.jobsClosedOnDuty[Player.job.name] and Player.job.onduty then
        return false
    end
    
    return true
end

-- Fonction pour créer un PED
local function CreateShopPed(pedData, shopData, shopKey)
    if not pedData.enabled then return end
    RequestModel(GetHashKey(pedData.model))
    while not HasModelLoaded(GetHashKey(pedData.model)) do Wait(1) end
    local ped = CreatePed(4, GetHashKey(pedData.model), pedData.coords.x, pedData.coords.y, pedData.coords.z, pedData.coords.w, false, true)
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    if pedData.scenario then TaskStartScenarioInPlace(ped, pedData.scenario, 0, true) end
    -- Ajoute l'interaction seulement si le joueur a le job requis
    if HasJobPermission(shopData) then
        exports.ox_target:addLocalEntity(ped, {
            {
                name = 'shop_ped_' .. shopData.label,
                icon = 'fas fa-shopping-cart',
                label = 'Ouvrir le magasin',
                radius = 0.5,
                distance = 2.5,
                debug = Config.Debug,
                onSelect = function()
                    TriggerEvent('asc_shops:client:openShop', shopKey)
                end
            }
        })
    end
    return ped
end

-- Fonction pour créer un point d'interaction
local function CreateShopPoint(coords, shopData, shopKey)
    -- Ajoute l'interaction seulement si le joueur a le job requis
    if HasJobPermission(shopData) then
        exports.ox_target:addBoxZone({
            coords = coords,
            size = vec3(0.5, 0.5, 0.5),
            rotation = 0,
            debug = Config.Debug,
            options = {
                {
                    name = 'shop_point_' .. shopData.label,
                    icon = 'fas fa-shopping-cart',
                    label = 'Accéder au magasin',
                    distance = 2.5,
                    onSelect = function()
                        TriggerEvent('asc_shops:client:openShop', shopKey)
                    end
                }
            }
        })
    end
end

-- Fonction pour supprimer tous les PEDs
local function DeleteAllPeds()
    for _, pedGroup in pairs(shopPeds) do
        if type(pedGroup) == 'table' then
            for _, ped in ipairs(pedGroup) do
                if DoesEntityExist(ped) then
                    DeleteEntity(ped)
                end
            end
        elseif DoesEntityExist(pedGroup) then
            DeleteEntity(pedGroup)
        end
    end
    shopPeds = {}
end

-- Fonction pour créer un blip
local function CreateShopBlip(blipData, coords, label)
    if not blipData.enabled then return end
    
    local blip = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(blip, blipData.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, blipData.scale)
    SetBlipColour(blip, blipData.color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(label or blipData.label)
    EndTextCommandSetBlipName(blip)
    
    return blip
end

-- Créer les blips au démarrage
CreateThread(function()
    for shopName, shopData in pairs(Config.Shops) do
        if shopData.blip and shopData.blip.enabled then
            shopBlips[shopName] = {}
            
            -- Créer un blip pour chaque coordonnée
            if type(shopData.coords) == 'table' then
                for i, coords in ipairs(shopData.coords) do
                    local blip = CreateShopBlip(shopData.blip, coords, shopData.blip.label)
                    if blip then
                        table.insert(shopBlips[shopName], blip)
                    end
                end
            end

            -- Créer un blip pour chaque PED
            if shopData.peds then
                for i, pedData in ipairs(shopData.peds) do
                    if pedData.enabled then
                        local blip = CreateShopBlip(shopData.blip, pedData.coords, shopData.blip.label)
                        if blip then
                            table.insert(shopBlips[shopName], blip)
                        end
                    end
                end
            end
        end
    end
end)

-- Fonction pour supprimer tous les blips
local function DeleteAllBlips()
    for _, blipGroup in pairs(shopBlips) do
        if type(blipGroup) == 'table' then
            for _, blip in ipairs(blipGroup) do
                if DoesBlipExist(blip) then
                    RemoveBlip(blip)
                end
            end
        elseif DoesBlipExist(blipGroup) then
            RemoveBlip(blipGroup)
        end
    end
    shopBlips = {}
end

-- Créer les PEDs et points d'interaction au démarrage
CreateThread(function()
    Wait(1000) -- Attendre que le joueur soit complètement chargé
    if LocalPlayer.state.isLoggedIn then
        CreateAllShopInteractions()
    end
end)

-- Fonction pour créer toutes les interactions
function CreateAllShopInteractions()
    -- Supprimer les interactions existantes
    DeleteAllPeds()
    
    -- Recréer toutes les interactions
    for shopKey, shopData in pairs(Config.Shops) do
        if shopData.peds then
            shopPeds[shopKey] = {}
            local hasEnabledPeds = false
            
            for _, pedData in ipairs(shopData.peds) do
                if pedData.enabled then
                    hasEnabledPeds = true
                    local ped = CreateShopPed(pedData, shopData, shopKey)
                    if ped then
                        table.insert(shopPeds[shopKey], ped)
                    end
                end
            end
            
            -- Si aucun ped n'est activé, créer des points d'interaction pour chaque coordonnée
            if not hasEnabledPeds and type(shopData.coords) == 'table' then
                for _, coords in ipairs(shopData.coords) do
                    CreateShopPoint(coords, shopData, shopKey)
                end
            end
        else
            -- Si pas de peds, créer des points d'interaction pour chaque coordonnée
            if type(shopData.coords) == 'table' then
                for _, coords in ipairs(shopData.coords) do
                    CreateShopPoint(coords, shopData, shopKey)
                end
            end
        end
    end
end

-- Événement quand le joueur se connecte
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    CreateAllShopInteractions()
end)

-- Événement quand le joueur se déconnecte
RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    DeleteAllPeds()
end)

-- Événement pour mettre à jour les interactions quand le job change
RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    CreateAllShopInteractions()
end)

-- Événement pour mettre à jour les interactions quand le statut de service change
RegisterNetEvent('QBCore:Client:SetDuty', function(duty)
    CreateAllShopInteractions()
    TriggerServerEvent('pc_shops:playerDutyChanged')
end)

-- Nettoyer les PEDs et blips à l'arrêt de la ressource
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        DeleteAllPeds()
        DeleteAllBlips()
    end
end)

-- Fonction pour préparer les données des items
local function PrepareItemsData(shopData)
    local items = {}
    for _, item in ipairs(shopData.items) do
        local itemInfo = exports.ox_inventory:Items(item.name)
        if itemInfo then
            -- Récupérer la quantité d'items du joueur avec Search
            local playerAmount = exports.ox_inventory:Search('count', item.name) or 0
            table.insert(items, {
                name = item.name,
                label = itemInfo.label,
                description = itemInfo.description or '',
                price = item.price,
                image = itemInfo.image or 'nui://ox_inventory/web/images/' .. item.name .. '.png',
                weight = itemInfo.weight or 0,
                type = itemInfo.type or 'item',
                playerAmount = playerAmount -- Ajouter la quantité du joueur
            })
        else
        end
    end
    return items
end

-- Événement pour vérifier le statut de fermeture du shop
RegisterNetEvent('pc_shops:checkShopClosedResult', function(isClosed, shopName)
    if isClosed then
        exports.ox_lib:notify({
            title = 'Magasin',
            description = 'Le magasin est actuellement fermé (service en cours)',
            type = 'error',
            position = 'top-right',
            duration = 3000
        })
    else
        OpenShopUIContinue(shopName)
    end
end)

-- Fonction pour ouvrir l'interface du magasin
function OpenShopUI(shopName)
    local shopData = Config.Shops[shopName]
    if not shopData then return end

    if shopData.shopClosedOnDuty then
        TriggerServerEvent('pc_shops:checkShopClosed', shopName)
        return
    end
    OpenShopUIContinue(shopName)
end

-- Sépare la logique d'ouverture effective du shop
function OpenShopUIContinue(shopName)
    local shopData = Config.Shops[shopName]
    if not shopData then return end
    -- Préparer les données des items
    local items = PrepareItemsData(shopData)
    local Player = QBCore.Functions.GetPlayerData()
    local cash = Player.money.cash
    local bank = Player.money.bank
    local blackMoney = exports.ox_inventory:Search('count', 'black_money') or 0
    local shopType = 'buy'
    if shopData.type == 'sell' then
        shopType = 'sell'
    elseif shopData.type == 'both' then
        shopType = 'both'
    end
    SendNUIMessage({
        action = 'openShop',
        shopName = shopName,
        type = shopType,
        payment = shopData.payment or {cash = true, bank = true, black_money = false},
        items = items,
        money = {
            cash = cash,
            bank = bank,
            black_money = blackMoney
        }
    })
    SetNuiFocus(true, true)
end

-- Événement pour ouvrir le magasin
RegisterNetEvent('asc_shops:client:openShop', function(shopName)
    OpenShopUI(shopName)
end)

-- Callback NUI pour afficher les notifications
RegisterNUICallback('showNotification', function(data, cb)
    if not data then return cb('ok') end
    
    exports.ox_lib:notify({
        title = 'Magasin',
        description = data.message,
        type = data.type,
        position = 'top-right',
        duration = 3000
    })
    cb('ok')
end)

-- Callback NUI pour fermer l'interface
RegisterNUICallback('closeShop', function(data, cb)
    isUIOpen = false
    SetNuiFocus(false, false)
    cb('ok')
end)

-- Callback NUI pour vérifier l'argent du joueur
RegisterNUICallback('checkMoney', function(data, cb)
    TriggerServerEvent('asc_shops:server:checkMoney', data.amount, data.paymentType, data.shopName)
    cb('ok')
end)

-- Écouteur pour la réponse de vérification d'argent
RegisterNetEvent('asc_shops:client:checkMoneyResponse', function(hasEnough)
    SendNUIMessage({
        action = "moneyCheckResponse",
        success = hasEnough
    })
end)

-- Callback NUI pour acheter des items
RegisterNUICallback('buyItems', function(data, cb)
    if not data.items or not data.shopName or not data.paymentType or not data.type then
        cb({success = false, message = 'Données invalides'})
        return
    end
    TriggerServerEvent('asc_shops:server:buyItems', data.items, data.shopName, data.paymentType, data.type)
    cb({success = true})
end)

-- Réception de la réponse d'achat
RegisterNetEvent('asc_shops:client:buyResponse', function(success, message)
    -- Envoyer la réponse au NUI
    SendNUIMessage({
        action = 'buyResponse',
        success = success,
        message = message
    })

    -- Afficher une notification si l'inventaire est plein
    if message == 'Inventaire plein' then
        exports.ox_lib:notify({
            title = 'Magasin',
            description = 'Votre inventaire est plein',
            type = 'error',
            position = 'top-right',
            duration = 3000
        })
    end

    -- Afficher une notification si pas assez d'argent
    if message == 'Pas assez d\'argent liquide' or message == 'Pas assez d\'argent en banque' or message == 'Pas assez d\'argent sale' then
        exports.ox_lib:notify({
            title = 'Magasin',
            description = 'Vous n\'avez pas assez d\'argent',
            type = 'error',
            position = 'top-right',
            duration = 3000
        })
    end

    -- Afficher une notification si pas assez d'items
    if message == 'Vous n\'avez pas assez de cet item' then
        exports.ox_lib:notify({
            title = 'Magasin',
            description = 'Vous n\'avez pas assez d\'items',
            type = 'error',
            position = 'top-right',
            duration = 3000
        })
    end
end)

-- Callback pour la réponse de vente
RegisterNUICallback('sellItems', function(data, cb)
    if not data.items or not data.shopName or not data.paymentType or not data.type then
        cb({success = false, message = 'Données invalides'})
        return
    end

    TriggerServerEvent('asc_shops:server:sellItems', data.items, data.shopName, data.paymentType, data.type)
    cb({success = true})
end)

-- Rafraîchir les shops pour tous les joueurs quand un joueur change de service
RegisterNetEvent('pc_shops:refreshShops', function()
    CreateAllShopInteractions()
end) 