let currentShop = null;
let currentCategory = 'all';
let cart = [];
let currentPaymentType = null;

// Écouteur pour les messages du client
window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.action === 'openShop') {
        // Réinitialiser l'interface avant d'ouvrir le magasin
        resetInterface();
        
        currentShop = {
            name: data.shopName,
            type: data.type || 'buy',
            payment: data.payment,
            items: data.items
        };
        
        // Gérer l'affichage des boutons d'action
        const buyButton = document.getElementById('buyButton');
        const sellButton = document.getElementById('sellButton');
        
        if (data.type === 'both') {
            buyButton.classList.remove('hidden');
            sellButton.classList.remove('hidden');
            currentShop.type = 'buy'; // Par défaut, on commence en mode achat
        } else {
            buyButton.classList.add('hidden');
            sellButton.classList.add('hidden');
            if (data.type === 'buy') {
                buyButton.classList.remove('hidden');
            } else if (data.type === 'sell') {
                sellButton.classList.remove('hidden');
            }
        }
        
        displayItems(data.items);
        updateCart();
        document.getElementById('shop-container').classList.remove('hidden');
        updateMoneyDisplay(data.money);
    }
});

// Écouteur pour la réponse de vérification d'argent
window.addEventListener('message', function(event) {
    if (event.data.action === "moneyCheckResponse") {
        if (event.data.success) {
            // Continuer avec les achats
            const cartItems = [...cart];
            cart = [];
            updateCart();
            
            let successCount = 0;
            const totalItems = cartItems.length;
            
            function processNextItem(index) {
                if (index >= cartItems.length) {
                    if (successCount === totalItems) {
                        closeShop();
                    }
                    return;
                }

                const item = cartItems[index];
                fetch(`https://${GetParentResourceName()}/buyItem`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        item: item.name,
                        amount: item.quantity,
                        shopName: currentShop.name,
                        paymentType: currentPaymentType,
                        type: currentShop.type
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        successCount++;
                    }
                    processNextItem(index + 1);
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    processNextItem(index + 1);
                });
            }

            processNextItem(0);
        } else {
            sendNotification('error', 'Vous n\'avez pas assez d\'argent');
            hidePaymentOptions();
        }
    }
});

// Fermeture du shop
document.getElementById('closeButton').addEventListener('click', function() {
    closeShop();
});

// Gestion des catégories
document.querySelectorAll('.category-button').forEach(button => {
    button.addEventListener('click', function() {
        document.querySelector('.category-button.active').classList.remove('active');
        this.classList.add('active');
        currentCategory = this.dataset.category;
        displayItems(currentShop.items);
    });
});

// Fonction pour afficher les items
function displayItems(items) {
    const itemsContainer = document.querySelector('.shop-items');
    itemsContainer.innerHTML = '';

    items.forEach(item => {
        const itemElement = document.createElement('div');
        itemElement.className = 'item-card';
        itemElement.innerHTML = `
            <div class="item-image">
                <img src="${item.image}" alt="${item.label}" onerror="this.src='nui://ox_inventory/web/images/${item.name}.png'">
            </div>
            <div class="item-info">
                <h3>${item.label}</h3>
                <p class="item-description">${item.description || ''}</p>
                <p class="item-price">${item.price}</p>
                <p class="item-player-amount">En votre possession: ${item.playerAmount}</p>
            </div>
            <div class="item-actions">
                <input type="number" min="1" max="5000" value="1" class="item-amount">
                <button class="add-to-cart-button" data-item-name="${item.name}" data-item-label="${item.label}">
                    <i class="fas fa-cart-plus"></i> Ajouter au panier
                </button>
            </div>
        `;
        
        // Add event listener for the button
        const addButton = itemElement.querySelector('.add-to-cart-button');
        addButton.addEventListener('click', function() {
            addToCart(this.dataset.itemName, this.dataset.itemLabel);
        });
        
        itemsContainer.appendChild(itemElement);
    });
}

// Fonction pour ajouter un item au panier
function addToCart(itemName, itemLabel) {
    // Find the button that was clicked using event.target
    const button = event.target.closest('.add-to-cart-button');
    if (!button) return;
    
    const itemCard = button.closest('.item-card');
    if (!itemCard) return;
    
    const amountInput = itemCard.querySelector('.item-amount');
    const amount = parseInt(amountInput.value);
    
    if (amount > 0 && amount <= 5000) {
        const item = currentShop.items.find(i => i.name === itemName);
        if (item) {
            const existingItem = cart.find(i => i.name === itemName);
            if (existingItem) {
                existingItem.quantity += amount;
            } else {
                cart.push({
                    name: itemName,
                    label: itemLabel,
                    price: item.price,
                    quantity: amount
                });
            }
            updateCart();
            // Réinitialiser l'input à 1 après l'ajout
            amountInput.value = 1;
        }
    }
}

// Fonction pour mettre à jour le panier
function updateCart() {
    const cartContainer = document.querySelector('.cart-items');
    const totalElement = document.getElementById('cart-total-price');
    cartContainer.innerHTML = '';

    let total = 0;

    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;

        const cartItemElement = document.createElement('div');
        cartItemElement.className = 'cart-item';
        cartItemElement.innerHTML = `
            <div class="cart-item-info">
                <span class="cart-item-name">${item.label}</span>
                <span class="cart-item-price">${item.price} x ${item.quantity}</span>
            </div>
            <div class="cart-item-quantity">
                <button onclick="updateCartItemQuantity('${item.name}', ${item.quantity - 1})">-</button>
                <span>${item.quantity}</span>
                <button onclick="updateCartItemQuantity('${item.name}', ${item.quantity + 1})">+</button>
            </div>
        `;
        cartContainer.appendChild(cartItemElement);
    });

    totalElement.textContent = `${total}`;
    
    // Mettre à jour le bouton de paiement
    const checkoutButton = document.querySelector('.checkout-button');
    if (checkoutButton) {
        checkoutButton.disabled = cart.length === 0;
        checkoutButton.textContent = currentShop.type === 'sell' ? 'Vendre' : 'Acheter';
    }
}

// Fonction pour mettre à jour la quantité d'un item dans le panier
function updateCartItemQuantity(itemName, newQuantity) {
    if (newQuantity <= 0) {
        cart = cart.filter(item => item.name !== itemName);
    } else if (newQuantity <= 5000) {
        const item = cart.find(i => i.name === itemName);
        if (item) {
            item.quantity = newQuantity;
        }
    }
    updateCart();
}

function updateCartTotal() {
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    document.getElementById('cart-total-price').textContent = `${total}`;
}

function showPaymentOptions() {
    const paymentOptions = document.getElementById('payment-options');
    const shopData = currentShop;
    
    // Vider les options existantes
    paymentOptions.innerHTML = '';
    
    // Vérifier si les options de paiement existent
    if (!shopData.payment) {
        console.error('Options de paiement non définies pour ce magasin');
        sendNotification('error', 'Ce magasin n\'accepte aucun mode de paiement');
        return;
    }
    
    // Ajouter les options de paiement en fonction de la configuration
    if (shopData.payment.cash) {
        const cashOption = document.createElement('div');
        cashOption.className = 'payment-option';
        cashOption.innerHTML = `
            <i class="fas fa-money-bill"></i>
            <span>Espèces</span>
        `;
        cashOption.onclick = () => selectPaymentType('cash');
        paymentOptions.appendChild(cashOption);
    }
    
    if (shopData.payment.bank) {
        const bankOption = document.createElement('div');
        bankOption.className = 'payment-option';
        bankOption.innerHTML = `
            <i class="fas fa-credit-card"></i>
            <span>Carte bancaire</span>
        `;
        bankOption.onclick = () => selectPaymentType('bank');
        paymentOptions.appendChild(bankOption);
    }
    
    if (shopData.payment.black_money) {
        const blackMoneyOption = document.createElement('div');
        blackMoneyOption.className = 'payment-option';
        blackMoneyOption.innerHTML = `
            <i class="fas fa-money-bill-wave"></i>
            <span>Argent sale</span>
        `;
        blackMoneyOption.onclick = () => selectPaymentType('black_money');
        paymentOptions.appendChild(blackMoneyOption);
    }
    
    paymentOptions.classList.remove('hidden');
}

// Fonction pour traiter les achats
function processPurchases() {
    const cartItems = [...cart];
    cart = [];
    updateCart();
    
    fetch(`https://${GetParentResourceName()}/buyItems`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            items: cartItems,
            shopName: currentShop.name,
            paymentType: currentPaymentType,
            type: currentShop.type
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Erreur réseau');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            closeShop();
        } else {
            sendNotification('error', data.message || 'Erreur lors de l\'achat');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        sendNotification('error', 'Erreur lors de l\'achat');
    });
}

// Fonction pour traiter les ventes
function processSales() {
    const cartItems = [...cart];
    cart = [];
    updateCart();
    
    fetch(`https://${GetParentResourceName()}/sellItems`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            items: cartItems,
            shopName: currentShop.name,
            paymentType: currentPaymentType,
            type: 'sell'
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Erreur réseau');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            closeShop();
        } else {
            sendNotification('error', data.message || 'Erreur lors de la vente');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        sendNotification('error', 'Erreur lors de la vente');
    });
}

// Gestion des boutons d'action
document.getElementById('buyButton').addEventListener('click', function() {
    if (cart.length === 0) {
        sendNotification('error', 'Il n\'y a rien dans votre panier');
        return;
    }
    currentShop.type = 'buy';
    showPaymentOptions();
});

document.getElementById('sellButton').addEventListener('click', function() {
    if (cart.length === 0) {
        sendNotification('error', 'Il n\'y a rien dans votre panier');
        return;
    }
    currentShop.type = 'sell';
    showPaymentOptions();
});

// Gestion des touches
document.addEventListener('keyup', function(event) {
    if (event.key === 'Escape') {
        closeShop();
    }
});

// Fonction pour afficher les notifications
function showNotification(type, message) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(notification);

    // Supprimer la notification après 3 secondes
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Fonction pour cacher les options de paiement
function hidePaymentOptions() {
    const paymentButtons = document.querySelector('.payment-buttons');
    if (paymentButtons) {
        const checkoutButton = document.createElement('button');
        checkoutButton.className = 'checkout-button';
        checkoutButton.textContent = 'Payer';
        checkoutButton.onclick = showPaymentOptions;
        paymentButtons.replaceWith(checkoutButton);
    }
}

// Fonction pour envoyer une notification
function sendNotification(type, message) {
    fetch(`https://${GetParentResourceName()}/showNotification`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: type,
            message: message
        })
    }).catch(error => {
        console.error('Erreur lors de l\'envoi de la notification:', error);
    });
}

// Fonction pour réinitialiser l'interface
function resetInterface() {
    // Réinitialiser le panier
    cart = [];
    updateCart();
    
    // Réinitialiser le type de paiement
    currentPaymentType = null;
    
    // Cacher les options de paiement
    const paymentOptions = document.getElementById('payment-options');
    if (paymentOptions) {
        paymentOptions.classList.add('hidden');
    }
    
    // Réinitialiser le type de magasin
    if (currentShop) {
        currentShop.type = currentShop.type === 'both' ? 'buy' : currentShop.type;
    }
}

// Fonction pour fermer l'interface
function closeShop() {
    document.getElementById('shop-container').classList.add('hidden');
    resetInterface();
    fetch(`https://${GetParentResourceName()}/closeShop`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).catch(error => {
        console.error('Erreur lors de la fermeture:', error);
    });
}

function formatMoney(amount) {
    return '$' + amount.toLocaleString('fr-FR');
}

function updateMoneyDisplay(money) {
    // On récupère la config de paiement du shop courant
    const payment = currentShop && currentShop.payment ? currentShop.payment : {cash: true, bank: true, black_money: false};

    // Cash
    const cashDiv = document.querySelector('.money-item:not(.black-money)');
    if (payment.cash) {
        cashDiv.style.display = '';
        document.getElementById('cashAmount').textContent = `$${money.cash}`;
    } else {
        cashDiv.style.display = 'none';
    }

    // Bank
    const bankDiv = document.querySelector('.money-item:nth-child(2)');
    if (payment.bank) {
        bankDiv.style.display = '';
        document.getElementById('bankAmount').textContent = `$${money.bank}`;
    } else {
        bankDiv.style.display = 'none';
    }

    // Black money
    const blackDiv = document.querySelector('.money-item.black-money');
    if (payment.black_money) {
        blackDiv.style.display = '';
        document.getElementById('blackMoneyAmount').textContent = `$${money.black_money}`;
    } else {
        blackDiv.style.display = 'none';
    }
}

// Fonction pour sélectionner le type de paiement
function selectPaymentType(paymentType) {
    currentPaymentType = paymentType;
    const paymentOptions = document.getElementById('payment-options');
    paymentOptions.classList.add('hidden');
    
    if (currentShop.type === 'buy') {
        processPurchases();
    } else if (currentShop.type === 'sell') {
        processSales();
    }
} 