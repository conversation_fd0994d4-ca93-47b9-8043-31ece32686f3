-- Variables globales pour le vidage progressif des chariots
SavedPhases = {}

-- Tables locales pour suivre les entités Pacific
local spawnedTrolleys = {}
local spawnedTrolleysEmpty = {}
local spawnedGuards = {}
local spawnedKeypad = nil
local spawnedCard = nil

-- Spawn de l'employé Pacific Bank au chargement du script
local pacificPed = nil
local pacificPedNetId = nil
local pedModel = 'u_m_m_bankman'
local coords = Config.Pacific.pedCoords
local zoneRadius = 50.0

function isPlayerInPacific()
    local playerCoords = GetEntityCoords(PlayerPedId())
    return #(playerCoords - vector3(coords.x, coords.y, coords.z)) < zoneRadius
end

-- Gestion du ped synchronisé
local lastPacificPassword = nil

function ShowPacificPedConfirmation()
    local amount = Config.Pacific.pedChargeAmount or 5000
    local alert = lib.alertDialog({
        header = 'Confirmation',
        content = ("L'employé a une information pour vous... Voulez-vous vraiment payer %d$ en argent sale pour obtenir l'information ?"):format(amount),
        centered = true,
        cancel = true
    })
    if alert ~= 'confirm' then
        return
    end
    -- Vérifie l'argent sale (argent sale = 'markedbills' ou 'black_money' selon ton système)
    local dirty = exports.ox_inventory:Search('count', 'black_money')
    if dirty < amount then
        exports.ox_lib:notify({title = 'Pacific', description = "Vous n'avez pas assez d'argent sale !", type = 'error'})
        return
    end
    -- Retire l'argent sale
    TriggerServerEvent('ox_inventory:removeItem', 'black_money', amount)
    -- Demande le code au serveur
    TriggerServerEvent('pc_robbery:pacific:requestCode')
end

-- Ajout de l'ox_target sur le ped synchronisé
function AddPacificPedTarget()
    if pacificPed and DoesEntityExist(pacificPed) then
        exports.ox_target:addLocalEntity(pacificPed, {
            {
                name = 'pacific_ped_talk',
                icon = 'fa-solid fa-comments',
                label = "Obtenir l'information",
                distance = 2.0,
                onSelect = function()
                    ShowPacificPedConfirmation()
                end
            }
        })
    end
end

-- Fonction pour spawn le ped Pacific côté client (networked)
function SpawnPacificPedIfNeeded()
    if not pacificPed or not DoesEntityExist(pacificPed) then
        local pedModel = GetHashKey('u_m_m_bankman')
        RequestModel(pedModel)
        while not HasModelLoaded(pedModel) do Wait(10) end
        local coords = Config.Pacific.pedCoords
        local ped = CreatePed(0, pedModel, coords.x, coords.y, coords.z, coords.w, true, true)
        SetEntityInvincible(ped, true)
        SetBlockingOfNonTemporaryEvents(ped, true)
        SetEntityAsMissionEntity(ped, true, true)
        FreezeEntityPosition(ped, true)
        local netId = NetworkGetNetworkIdFromEntity(ped)
        pacificPed = ped
        pacificPedNetId = netId
        TriggerServerEvent('pc_robbery:pacific:pedSpawned', netId)
    end
end

-- Quand le serveur diffuse le netId, tous les clients ajoutent l'ox_target
RegisterNetEvent('pc_robbery:pacific:setPed', function(netId)
    pacificPedNetId = netId
    pacificPed = NetworkGetEntityFromNetworkId(netId)
    if pacificPed and DoesEntityExist(pacificPed) then
        AddPacificPedTarget()
    end
end)

-- Au chargement du script, si le ped n'existe pas, tente de le spawn (premier joueur)
CreateThread(function()
    Wait(2000)
    if not pacificPed or not DoesEntityExist(pacificPed) then
        SpawnPacificPedIfNeeded()
    end
end)

-- Détection entrée/sortie zone côté client
CreateThread(function()
    local wasInZone = false
    while true do
        Wait(1000)
        local inZone = isPlayerInPacific()
        if inZone and not wasInZone then
            TriggerServerEvent('pc_robbery:pacific:playerEnteredZone')
        elseif not inZone and wasInZone then
            TriggerServerEvent('pc_robbery:pacific:playerLeftZone')
        end
        wasInZone = inZone
    end
end)

local lastPacificPassword = nil -- déjà utilisé pour le code donné par le peds
local keypadEntity = nil

-- Ajoute un ox_target sur le keypad pour entrer le code
function AddKeypadTarget()
    if keypadEntity and DoesEntityExist(keypadEntity) then
        exports.ox_target:addLocalEntity(keypadEntity, {
            {
                name = 'pacific_keypad_enter',
                icon = 'fa-solid fa-keyboard',
                label = "Entrer le code",
                distance = 2.0,
                onSelect = function()
                    local input = lib.inputDialog('Entrer le code', {
                        {type = 'input', label = 'Code à 4 chiffres', password = true, icon = 'hashtag', min = 4, max = 4}
                    })
                    if not input or not input[1] then return end
                    local code = tonumber(input[1])
                    if not code then return end
                    TriggerServerEvent('pc_robbery:pacific:tryCode', code)
                end
            }
        })
    end
end

-- Spawn du prop keypad à la position définie dans la config
CreateThread(function()
    local propModel = 'prop_ld_keypad_01'
    local coords = Config.Pacific.keypadCoords
    -- Supprimer tout ancien keypad à la position
    local handle, entity = FindFirstObject()
    local hash = GetHashKey(propModel)
    local success, deleted = true, false
    repeat
        if entity and GetEntityModel(entity) == hash then
            local entCoords = GetEntityCoords(entity)
            local dist = #(vector3(coords.x, coords.y, coords.z) - entCoords)
            if dist < 1.0 then
                SetEntityAsMissionEntity(entity, true, true)
                DeleteEntity(entity)
                deleted = true
            end
        end
        success, entity = FindNextObject(handle)
    until not success
    EndFindObject(handle)
    -- Créer le nouveau keypad
    RequestModel(propModel)
    while not HasModelLoaded(propModel) do Wait(10) end
    keypadEntity = CreateObject(GetHashKey(propModel), coords.x, coords.y, coords.z, false, false, false)
    if coords.rot then
        SetEntityRotation(keypadEntity, coords.rot.x or 0.0, coords.rot.y or 0.0, coords.rot.z or 0.0, 2, true)
    end
    FreezeEntityPosition(keypadEntity, true)
    SetEntityInvincible(keypadEntity, true)
    SetEntityAsMissionEntity(keypadEntity, true, true)
    -- Ajoute le target sur le keypad
    AddKeypadTarget()
end)

-- Fonction utilitaire pour afficher un texte 3D
function DrawText3D(x, y, z, text)
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(true)
    AddTextComponentString(text)
    SetDrawOrigin(x, y, z, 0)
    DrawText(0.0, 0.0)
    ClearDrawOrigin()
end 

RegisterNetEvent('pc_robbery:pacific:receiveCode', function(code)
    exports.ox_lib:notify({title = 'Pacific', description = ("Mot de passe reçu : %04d"):format(code), type = 'success', duration = 10000})
end)

-- Flag local pour savoir si le code a été entré
local pacificCodeUnlocked = true
local pacificHackCompleted = true
local pacificC4Exploded = true
local pacificCardTaken = true
local pacificCardPickedUp = true
local thermitePlaced = true
local c4Placed = true -- Variable pour suivre si le C4 a été placé
local vaultMoneyProp = nil -- Variable pour le prop d'argent du coffre

RegisterNetEvent('pc_robbery:pacific:codeResult', function(success)
    if success then
        pacificCodeUnlocked = true
        exports['ps-dispatch']:PacificBankRobbery()
        exports.ox_lib:notify({title = 'Pacific', description = 'Code correct !', type = 'success'})
        -- Ici tu pourras déclencher l'ouverture de la porte, etc.
    else
        exports.ox_lib:notify({title = 'Pacific', description = 'Code incorrect.', type = 'error'})
    end
end) 

CreateThread(function()
    local coords = Config.Pacific.hackCoords
    exports.ox_target:addSphereZone({
        coords = coords,
        radius = 1.2,
        debug = false,
        options = {
            {
                name = 'pacific_hack_connect',
                icon = 'fa-solid fa-terminal',
                label = 'Connexion au système',
                distance = 2.0,
                canInteract = function()
                    return pacificCodeUnlocked
                end,
                onSelect = function()
                    local ped = PlayerPedId()
                    -- Animation de typing
                    RequestAnimDict('anim@heists@prison_heiststation@cop_reactions')
                    while not HasAnimDictLoaded('anim@heists@prison_heiststation@cop_reactions') do Wait(10) end
                    TaskPlayAnim(ped, 'anim@heists@prison_heiststation@cop_reactions', 'cop_b_idle', 8.0, -8.0, -1, 49, 0, false, false, false)
                    -- Progressbar
                    local success = lib.progressBar({
                        duration = 7000,
                        label = 'Connexion au système...',
                        useWhileDead = false,
                        canCancel = false,
                        disable = {move = true, car = true, combat = true}
                    })
                    if success then
                        -- Lancer le minigame glitch-minigames
                        local minigameSuccess = exports['glitch-minigames']:StartCircuitBreaker(4, 1)
                        if minigameSuccess then
                            pacificHackCompleted = true
                            exports.ox_lib:notify({title = 'Pacific', description = 'Système de sécurité désactivé', type = 'success'})
                            -- Ici tu pourras déclencher la suite du braquage
                        else
                            exports.ox_lib:notify({title = 'Pacific', description = 'Hack échoué !', type = 'error'})
                        end
                        ClearPedTasks(ped)
                    end
                end
            }
        }
    })
end) 

-- Ox_target pour placer le C4
CreateThread(function()
    local coords = Config.Pacific.c4DoorCoords
    local c4DoorId = Config.Pacific.c4DoorId
    exports.ox_target:addSphereZone({
        coords = coords,
        radius = 1.5,
        debug = false,
        options = {
            {
                name = 'pacific_c4_place',
                icon = 'fa-solid fa-bomb',
                label = 'Placer C4',
                distance = 2.0,
                canInteract = function()
                    return pacificHackCompleted and not pacificC4Exploded
                end,
                onSelect = function()
                    local ped = PlayerPedId()
                    -- Vérifier l'item requis
                    local hasItem = exports.ox_inventory:Search('count', Config.Pacific.c4DoorRequiredItem)
                    if hasItem < 1 then
                        exports.ox_lib:notify({title = 'Pacific', description = 'Vous n\'avez pas de C4 !', type = 'error'})
                        return
                    end

                    -- Minigame avant le placement du C4
                    local minigameSuccess = exports['glitch-minigames']:StartCircuitBreaker(3, 0)
                    if not minigameSuccess then
                        -- Fail : explosion immédiate
                        AddExplosion(coords.x, coords.y, coords.z, 'EXPLOSION_TANKER', 2.0, true, false, 1.0)
                        -- Déverrouille la porte
                        if c4DoorId then
                            TriggerServerEvent('pc_robbery:pacific:unlockC4Door', c4DoorId)
                        end
                        -- Tue le joueur si trop près
                        local dist = #(GetEntityCoords(ped) - coords)
                        print('DEBUG C4 FAIL - Distance joueur: ' .. dist .. 'm')
                        if dist < 4.0 then
                            SetEntityHealth(ped, 0)
                            print('DEBUG C4 FAIL - Joueur tué à ' .. dist .. 'm')
                        end
                        exports.ox_lib:notify({title = 'Pacific', description = 'Le C4 a explosé prématurément !', type = 'error'})
                        return
                    end

                    -- Succès : placement du C4
                    RequestAnimDict('anim@heists@ornate_bank@thermal_charge')
                    while not HasAnimDictLoaded('anim@heists@ornate_bank@thermal_charge') do Wait(10) end
                    TaskPlayAnim(ped, 'anim@heists@ornate_bank@thermal_charge', 'thermal_charge', 8.0, -8.0, -1, 49, 0, false, false, false)
                    local success = lib.progressBar({
                        duration = 5000,
                        label = 'Placement du C4...',
                        useWhileDead = false,
                        canCancel = false,
                        disable = {move = true, car = true, combat = true}
                    })
                    if success then
                        -- Retirer l'item
                        TriggerServerEvent('ox_inventory:removeItem', Config.Pacific.c4DoorRequiredItem, 1)
                        ClearPedTasks(ped)
                        exports.ox_lib:notify({title = 'Pacific', description = 'C4 placé ! Explosion dans 10 secondes, éloignez-vous !', type = 'warning'})
                        -- Déverrouille la porte
                        if c4DoorId then
                            TriggerServerEvent('pc_robbery:pacific:unlockC4Door', c4DoorId)
                        end
                        -- Timer avant explosion
                        Wait(10000)
                        AddExplosion(coords.x, coords.y, coords.z, 'EXPLOSION_TANKER', 2.0, true, false, 1.0)
                        -- Tue le joueur si trop près
                        local dist = #(GetEntityCoords(ped) - coords)
                        print('DEBUG C4 SUCCESS - Distance joueur: ' .. dist .. 'm')
                        if dist < 4.0 then
                            SetEntityHealth(ped, 0)
                            print('DEBUG C4 SUCCESS - Joueur tué à ' .. dist .. 'm')
                        end
                        pacificC4Exploded = true
                        c4Placed = true -- Marquer que le C4 a été placé
                        exports.ox_lib:notify({title = 'Pacific', description = 'Le C4 a explosé !', type = 'success'})
                    end
                end
            }
        }
    })
end) 

-- Ox_target pour prendre la carte d'accès
CreateThread(function()
    local coords = Config.Pacific.cardCoords
    exports.ox_target:addSphereZone({
        coords = coords,
        radius = 1.0,
        debug = false,
        options = {
            {
                name = 'pacific_take_card',
                icon = 'fa-solid fa-credit-card',
                label = 'Prendre carte d\'accès',
                distance = 2.0,
                canInteract = function()
                    return pacificC4Exploded and not pacificCardPickedUp
                end,
                onSelect = function()
                    local ped = PlayerPedId()
                    -- Progressbar
                    local success = lib.progressBar({
                        duration = 3000,
                        label = 'Récupération de la carte...',
                        useWhileDead = false,
                        canCancel = false,
                        disable = {move = true, car = true, combat = true}
                    })
                    if success then
                        -- Donner la carte d'accès
                        TriggerServerEvent('pc_robbery:pacific:giveAccessCard')
                        pacificCardTaken = true
                        pacificCardPickedUp = true
                        exports.ox_lib:notify({title = 'Pacific', description = 'Carte d\'accès récupérée !', type = 'success'})
                        -- Supprimer le prop pour tous
                        TriggerServerEvent('pc_robbery:pacific:broadcastDeleteCardProp')
                    end
                end
            }
        }
    })
end) 

-- Gestion du prop carte d'accès
local cardProp = nil
local cardPropNetId = nil

function SpawnPacificCardProp()
    if cardProp and DoesEntityExist(cardProp) then return end
    
    -- Supprimer tous les anciens props carte avant d'en créer un nouveau
    local handle, entity = FindFirstObject()
    local hash = GetHashKey('p_ld_id_card_01')
    local coords = Config.Pacific.cardCoords
    local searchRadius = 2.0 -- Distance de recherche limitée
    local success = true
    repeat
        if entity and GetEntityModel(entity) == hash then
            local entityCoords = GetEntityCoords(entity)
            local dist = #(vector3(coords.x, coords.y, coords.z) - entityCoords)
            if dist < searchRadius then
                SetEntityAsMissionEntity(entity, true, true)
                DeleteEntity(entity)
            end
        end
        success, entity = FindNextObject(handle)
    until not success
    EndFindObject(handle)
    
    local model = GetHashKey('p_ld_id_card_01')
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(10) end
    cardProp = CreateObject(model, coords.x, coords.y, coords.z, true, true, false)
    SetEntityRotation(cardProp, coords.rot.x, coords.rot.y, coords.rot.z, 2, true)
    SetEntityAsMissionEntity(cardProp, true, true)
    FreezeEntityPosition(cardProp, true)
    cardPropNetId = NetworkGetNetworkIdFromEntity(cardProp)
    SetNetworkIdExistsOnAllMachines(cardPropNetId, true)
    SetNetworkIdCanMigrate(cardPropNetId, false)
end

function DeletePacificCardProp()
    if cardProp and DoesEntityExist(cardProp) then
        DeleteEntity(cardProp)
        cardProp = nil
        cardPropNetId = nil
    end
end

-- Suppression des fonctions legacy :
-- - DeleteAllTrolleys
-- - SpawnAllTrolleys
-- - SpawnPacificCardProp
-- - SpawnPacificGuards
-- - AddTrolleyTarget (remplacé par ox_target inline)
-- - Tous les CreateThread de spawn automatique pour trolleys, keypad, carte, guards
-- - Handler onResourceStart qui appelait DeleteAllTrolleys/SpawnAllTrolleys

-- Ox-target pour déverrouiller la porte secondaire
CreateThread(function()
    local coords = Config.Pacific.secondaryDoorCoords
    local secondaryDoorId = Config.Pacific.secondaryDoorId
    exports.ox_target:addSphereZone({
        coords = coords,
        radius = 1.5,
        debug = false,
        options = {
            {
                name = 'pacific_unlock_secondary_door',
                icon = 'fa-solid fa-unlock',
                label = 'Déverrouiller porte secondaire',
                distance = 1.0,
                onSelect = function()
                    if secondaryDoorId then
                        TriggerServerEvent('pc_robbery:pacific:unlockSecondaryDoor', secondaryDoorId)
                        exports.ox_lib:notify({title = 'Pacific', description = 'Porte secondaire déverrouillée !', type = 'success'})
                    end
                end
            }
        }
    })
end) 

-- Ox-target pour insérer la carte et ouvrir le coffre
CreateThread(function()
    local coords = Config.Pacific.vaultDoorCoords
    local vaultDoorObject = Config.Pacific.vaultDoorObject
    local vaultDoorHeading = Config.Pacific.vaultDoorHeading
    exports.ox_target:addSphereZone({
        coords = coords,
        radius = 1.5,
        debug = false,
        options = {
            {
                name = 'pacific_insert_card_vault',
                icon = 'fa-solid fa-credit-card',
                label = 'Insérer la carte',
                distance = 2.0,
                canInteract = function()
                    return pacificCardTaken
                end,
                onSelect = function()
                    local ped = PlayerPedId()
                    -- Vérifier que le joueur a la carte d'accès
                    local hasCard = exports.ox_inventory:Search('count', Config.Pacific.rewardCardCoords)
                    if hasCard < 1 then
                        exports.ox_lib:notify({title = 'Pacific', description = 'Vous n\'avez pas de carte d\'accès !', type = 'error'})
                        return
                    end
                    
                    -- Lancer le minigame
                    local minigameSuccess = exports['glitch-minigames']:StartCircuitBreaker(4, 0)
                    if minigameSuccess then
                        -- Retirer la carte
                        TriggerServerEvent('ox_inventory:removeItem', Config.Pacific.rewardCardCoords, 1)
                        -- Ouvrir la porte du coffre
                        TriggerServerEvent('pc_robbery:pacific:openVaultDoor', vaultDoorObject, vaultDoorHeading.open)
                        exports.ox_lib:notify({title = 'Pacific', description = 'Coffre ouvert !', type = 'success'})
                    else
                        exports.ox_lib:notify({title = 'Pacific', description = 'Hack échoué !', type = 'error'})
                    end
                end
            }
        }
    })
end) 

-- Event client pour ouvrir la porte du coffre
RegisterNetEvent('pc_robbery:pacific:openVaultDoorClient', function(vaultDoorObject, heading)
    local coords = Config.Pacific.vaultDoorCoords
    local door = GetClosestObjectOfType(coords.x, coords.y, coords.z, 5.0, GetHashKey(vaultDoorObject), false, false, false)
    if door and door ~= 0 then
        SetEntityHeading(door, heading)
        exports.ox_lib:notify({title = 'Pacific Bank', description = 'Porte du coffre ouverte !', type = 'success'})
        
        -- Spawn des guards après l'ouverture de la porte
        TriggerServerEvent('pc_robbery:pacific:spawnGuards')
    else
        exports.ox_lib:notify({title = 'Pacific Bank', description = 'Erreur : Porte du coffre non trouvée', type = 'error'})
    end
end)

-- Système de spawn des guards
local spawnedGuards = {}

function SpawnPacificGuards()
    -- Supprimer les guards existants
    DeletePacificGuards()
    
    -- Charger le modèle des guards
    local guardModel = GetHashKey(Config.Pacific.guards.model)
    RequestModel(guardModel)
    while not HasModelLoaded(guardModel) do
        Wait(10)
    end
    
    -- Spawn des guards
    for i, coords in ipairs(Config.Pacific.guards.coords) do
        local guard = CreatePed(4, guardModel, coords.x, coords.y, coords.z, coords.w, true, true)
        
        -- Configuration du guard
        SetPedArmour(guard, 100)
        SetPedMaxHealth(guard, 200)
        SetEntityHealth(guard, 200)
        SetPedCombatAttributes(guard, 46, true)
        SetPedCombatAttributes(guard, 5, true)
        SetPedCombatAttributes(guard, 0, true)
        SetPedCombatRange(guard, 2)
        SetPedCombatMovement(guard, 3)
        SetPedCombatAbility(guard, 100)
        SetPedAccuracy(guard, 60)
        
        -- Donner l'arme au guard
        GiveWeaponToPed(guard, GetHashKey(Config.Pacific.guards.weapon), 500, false, true)
        SetPedAmmo(guard, GetHashKey(Config.Pacific.guards.weapon), 500)
        
        -- Rendre le guard hostile
        SetPedCombatAttributes(guard, 46, true)
        SetPedFleeAttributes(guard, 0, false)
        SetPedCombatRange(guard, 2)
        SetPedCombatMovement(guard, 3)
        SetPedCombatAbility(guard, 100)
        SetPedAccuracy(guard, 60)
        
        -- Ajouter le guard à la liste
        table.insert(spawnedGuards, guard)
        
        -- Synchroniser le guard pour tous les joueurs
        local netId = NetworkGetNetworkIdFromEntity(guard)
        SetNetworkIdExistsOnAllMachines(netId, true)
        SetNetworkIdCanMigrate(netId, false)
    end
    
    SetModelAsNoLongerNeeded(guardModel)
    
    -- Démarrer la boucle de vérification des joueurs
    StartGuardCombatLoop()
    
    -- Notification
    exports.ox_lib:notify({title = 'Pacific Bank', description = 'Guards de sécurité activés !', type = 'error'})
end

function DeletePacificGuards()
    for _, guard in ipairs(spawnedGuards) do
        if DoesEntityExist(guard) then
            DeleteEntity(guard)
        end
    end
    spawnedGuards = {}
end

-- Event pour spawn les guards depuis le serveur
RegisterNetEvent('pc_robbery:pacific:spawnGuardsClient', function()
    SpawnPacificGuards()
end)

-- Event pour supprimer les guards depuis le serveur
RegisterNetEvent('pc_robbery:pacific:deleteGuardsClient', function()
    DeletePacificGuards()
end)

-- Boucle de combat des guards
local guardCombatActive = false
local playerIsPolice = false

function StartGuardCombatLoop()
    if guardCombatActive then return end
    guardCombatActive = true
    
    CreateThread(function()
        while guardCombatActive and #spawnedGuards > 0 do
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            
            -- Vérifier si le joueur est police
            TriggerServerEvent('pc_robbery:pacific:checkPlayerJob')
            
            -- Pour chaque guard
            for _, guard in ipairs(spawnedGuards) do
                if DoesEntityExist(guard) then
                    local guardCoords = GetEntityCoords(guard)
                    local distance = #(playerCoords - guardCoords)
                    
                    -- Si le joueur est proche (15 mètres) et n'est pas police
                    if distance < 15.0 and not playerIsPolice then
                        -- Faire attaquer le guard
                        TaskCombatPed(guard, playerPed, 0, 16)
                        SetPedCombatAttributes(guard, 46, true)
                        SetPedCombatAttributes(guard, 5, true)
                        SetPedCombatAttributes(guard, 0, true)
                    elseif distance < 15.0 and playerIsPolice then
                        -- Si c'est un policier, ne pas attaquer
                        ClearPedTasks(guard)
                        SetPedCombatAttributes(guard, 46, false)
                    end
                end
            end
            
            Wait(1000) -- Vérifier toutes les secondes
        end
    end)
end

function StopGuardCombatLoop()
    guardCombatActive = false
end

-- Event pour recevoir le statut police du serveur
RegisterNetEvent('pc_robbery:pacific:receivePlayerJob', function(isPolice)
    playerIsPolice = isPolice
end) 

-- Ox-target pour placer la thermite sur la porte secondaire du coffre
CreateThread(function()
    local coords = Config.Pacific.secondVaultDoorCoords
    local secondVaultDoorId = Config.Pacific.secondVaultDoorId
    local requiredItem = Config.Pacific.secondVaultDoorRequiredItem
    
    exports.ox_target:addSphereZone({
        coords = coords,
        radius = 1.5,
        debug = false,
        options = {
            {
                name = 'pacific_place_thermite_second_vault',
                icon = 'fa-solid fa-fire',
                label = 'Placer thermite',
                distance = 2.0,
                canInteract = function()
                    -- Vérifier que le joueur a l'item requis
                    local hasItem = exports.ox_inventory:Search('count', requiredItem)
                    return hasItem > 0
                end,
                onSelect = function()
                    local ped = PlayerPedId()
                    
                    -- Animation de placement
                    local dict = "anim@heists@ornate_bank@thermal_charge"
                    local anim = "thermal_charge"
                    
                    RequestAnimDict(dict)
                    while not HasAnimDictLoaded(dict) do
                        Wait(10)
                    end
                    
                    TaskPlayAnim(ped, dict, anim, 8.0, -8.0, -1, 0, 0, false, false, false)
                    
                    -- Progressbar
                    local success = lib.progressBar({
                        duration = 5000,
                        label = 'Placement de la thermite...',
                        useWhileDead = false,
                        canCancel = false,
                        disable = {move = true, car = true, combat = true},
                        anim = {
                            dict = dict,
                            clip = anim
                        }
                    })
                    
                    if success then
                        -- Retirer l'item
                        TriggerServerEvent('ox_inventory:removeItem', requiredItem, 1)
                        
                        -- Animation de fumée après un délai
                        SetTimeout(2000, function()
                            -- Créer l'effet de fumée
                            local smokeCoords = vector3(coords.x, coords.y, coords.z)
                            
                            -- Créer quelques points d'effet subtils
                            local effectPoints = {
                                vector3(coords.x, coords.y, coords.z),
                                vector3(coords.x + 0.1, coords.y, coords.z),
                                vector3(coords.x - 0.1, coords.y, coords.z)
                            }
                            
                            -- Créer un petit feu discret
                            local smallFire = StartScriptFire(coords.x, coords.y, coords.z, 1, false)
                            
                            -- Arrêter le feu après 3 secondes pour éviter qu'il se propage
                            SetTimeout(3000, function()
                                RemoveScriptFire(smallFire)
                            end)
                            
                            -- Son de brûlure/thermite
                            PlaySoundFromCoord(-1, "Burning", coords.x, coords.y, coords.z, "DLC_HEIST_FLEECA_SOUNDSET", 0, 0, 0)
                            
                            -- Déverrouiller la porte secondaire du coffre après 2 secondes de fumée
                            SetTimeout(2000, function()
                                if secondVaultDoorId then
                                    TriggerServerEvent('pc_robbery:pacific:unlockSecondVaultDoor', secondVaultDoorId)
                                end
                                
                                -- Activer l'accès aux récompenses du coffre
                                thermitePlaced = true
                                
                                -- L'argent est déjà spawné depuis le début, pas besoin de le recréer
                                
                                exports.ox_lib:notify({title = 'Pacific Bank', description = 'Porte secondaire du coffre détruite par la thermite !', type = 'success'})
                            end)
                        end)
                    else
                        exports.ox_lib:notify({title = 'Pacific Bank', description = 'Placement de la thermite annulé !', type = 'error'})
                    end
                    
                    ClearPedTasks(ped)
                end
            }
        }
    })
end) 

-- Fonction pour spawner tous les trolleys au démarrage
function SpawnAllTrolleys()
    -- Supprimer les anciens trolleys avant d'en créer de nouveaux
    DeleteAllTrolleys()
    
    if Config.Pacific and Config.Pacific.trolleys then
        for trolleyId, trolleyConfig in pairs(Config.Pacific.trolleys) do
            local coords = trolleyConfig.coords
            local model = trolleyConfig.model
            
            -- Charger le modèle
            RequestModel(model)
            while not HasModelLoaded(model) do
                Wait(1)
            end
            
            -- Créer le trolley
            local trolley = CreateObject(model, coords.x, coords.y, coords.z, true, true, true)
            SetEntityHeading(trolley, coords.w or 0.0)
            SetEntityAsMissionEntity(trolley, true, true)
            FreezeEntityPosition(trolley, true)
            SetEntityInvincible(trolley, true)
            
            -- Stocker le trolley
            spawnedTrolleys[trolleyId] = trolley
            
            -- Ajouter l'ox_target sur le trolley pour "Prendre l'argent"
            AddTrolleyTarget(trolley, trolleyId)
        end
        print('[Pacific Bank] Tous les trolleys ont été spawnés avec succès')
    else
        print('[Pacific Bank] Erreur: Config.Pacific.trolleys est nil !')
    end
end

-- Fonction pour ajouter l'ox_target sur un trolley
function AddTrolleyTarget(trolley, trolleyId)
    if trolley and DoesEntityExist(trolley) then
        print('[Pacific Debug] Ajout ox_target sur trolleyId:', trolleyId, trolley)
        exports.ox_target:addLocalEntity(trolley, {
            {
                name = 'pacific_trolley_loot_' .. trolleyId,
                icon = 'fa-solid fa-money-bill',
                label = 'Prendre l\'argent du trolley',
                distance = 2.0,
                canInteract = function()
                    print('[Pacific Debug] canInteract c4Placed:', c4Placed)
                    return c4Placed
                end,
                onSelect = function()
                    StartPacificTrolleyGrab(trolleyId, NetworkGetNetworkIdFromEntity(trolley))
                end
            }
        })
    else
        print('[Pacific Debug] Trolley inexistant pour AddTrolleyTarget', trolleyId, trolley)
    end
end 

function DeleteAllTrolleys()
    -- Supprime les trolleys référencés
    if spawnedTrolleys then
        for trolleyId, trolley in pairs(spawnedTrolleys) do
            if trolley and DoesEntityExist(trolley) then
                DeleteEntity(trolley)
            end
        end
        spawnedTrolleys = {}
    end

    -- Supprime tous les trolleys du monde dans la zone Pacific
    local trolleyModel = GetHashKey("hei_prop_hei_cash_trolly_01")
    local handle, entity = FindFirstObject()
    local success = true
    repeat
        if entity and GetEntityModel(entity) == trolleyModel then
            local entityCoords = GetEntityCoords(entity)
            -- Vérifie si c'est dans la zone de la Pacific Bank (ajuste le centre/rayon si besoin)
            if #(entityCoords - vec3(263.59, 214.03, 101.68)) < 50.0 then
                SetEntityAsMissionEntity(entity, true, true)
                DeleteEntity(entity)
            end
        end
        success, entity = FindNextObject(handle)
    until not success
    EndFindObject(handle)
end 

-- Synchronisation des entités Pacific depuis le serveur
RegisterNetEvent('pc_robbery:pacific:syncEntities', function(entities)
    -- Trolleys
    for trolleyId, netId in pairs(entities.trolleys or {}) do
        local trolley = NetworkGetEntityFromNetworkId(netId)
        if trolley and DoesEntityExist(trolley) then
            AddTrolleyTarget(trolley, trolleyId)
        end
    end
    -- Keypad
    if entities.keypad then
        local keypad = NetworkGetEntityFromNetworkId(entities.keypad)
        if keypad and DoesEntityExist(keypad) then
            -- Ajoute ici l'ox_target ou l'interaction pour le keypad
        end
    end
    -- Carte
    if entities.card then
        local card = NetworkGetEntityFromNetworkId(entities.card)
        if card and DoesEntityExist(card) then
            -- Ajoute ici l'ox_target ou l'interaction pour la carte
        end
    end
    -- Guards : rien à faire côté client sauf si tu veux ajouter une interaction
end)

-- Demande la sync au serveur à la connexion du client
CreateThread(function()
    Wait(2000)
    TriggerServerEvent('pc_robbery:pacific:requestEntities')
end) 

function StartPacificTrolleyGrab(trolleyId, trollyNetId)
    disableinput = true
    local ped = PlayerPedId()
    local model = "hei_prop_heist_cash_pile"
    local trollyobj = NetworkGetEntityFromNetworkId(trollyNetId)
    local emptyobj = GetHashKey("hei_prop_hei_cash_trolly_03")
    local baghash = GetHashKey("hei_p_m_bag_var22_arm_s")

    local CashAppear = function()
        local pedCoords = GetEntityCoords(ped)
        local grabmodel = GetHashKey(model)
        RequestModel(grabmodel)
        while not HasModelLoaded(grabmodel) do Citizen.Wait(100) end
        local grabobj = CreateObject(grabmodel, pedCoords, true)
        FreezeEntityPosition(grabobj, true)
        SetEntityInvincible(grabobj, true)
        SetEntityNoCollisionEntity(grabobj, ped)
        SetEntityVisible(grabobj, false, false)
        AttachEntityToEntity(grabobj, ped, GetPedBoneIndex(ped, 60309), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, false, false, false, false, 0, true)
        local startedGrabbing = GetGameTimer()
        Citizen.CreateThread(function()
            while GetGameTimer() - startedGrabbing < 37000 do
                Citizen.Wait(1)
                DisableControlAction(0, 73, true)
                if HasAnimEventFired(ped, GetHashKey("CASH_APPEAR")) then
                    if not IsEntityVisible(grabobj) then
                        SetEntityVisible(grabobj, true, false)
                    end
                end
                if HasAnimEventFired(ped, GetHashKey("RELEASE_CASH_DESTROY")) then
                    if IsEntityVisible(grabobj) then
                        SetEntityVisible(grabobj, false, false)
                        TriggerServerEvent("pc_robbery:pacific:giveTrolleyReward", trolleyId)
                    end
                end
            end
            DeleteObject(grabobj)
        end)
    end

    if IsEntityPlayingAnim(trollyobj, "anim@heists@ornate_bank@grab_cash", "cart_cash_dissapear", 3) then
        return
    end

    RequestAnimDict("anim@heists@ornate_bank@grab_cash")
    RequestModel(baghash)
    RequestModel(emptyobj)
    while not HasAnimDictLoaded("anim@heists@ornate_bank@grab_cash") or not HasModelLoaded(emptyobj) or not HasModelLoaded(baghash) do
        Citizen.Wait(100)
    end
    while not NetworkHasControlOfEntity(trollyobj) do
        Citizen.Wait(1)
        NetworkRequestControlOfEntity(trollyobj)
    end
    local bag = CreateObject(baghash, GetEntityCoords(ped), true, false, false)
    local scene1 = NetworkCreateSynchronisedScene(GetEntityCoords(trollyobj), GetEntityRotation(trollyobj), 2, false, false, **********, 0, 1.3)
    NetworkAddPedToSynchronisedScene(ped, scene1, "anim@heists@ornate_bank@grab_cash", "intro", 1.5, -4.0, 1, 16, **********, 0)
    NetworkAddEntityToSynchronisedScene(bag, scene1, "anim@heists@ornate_bank@grab_cash", "bag_intro", 4.0, -8.0, 1)
    SetPedComponentVariation(ped, 5, 0, 0, 0)
    NetworkStartSynchronisedScene(scene1)
    Citizen.Wait(1500)
    CashAppear()
    local scene2 = NetworkCreateSynchronisedScene(GetEntityCoords(trollyobj), GetEntityRotation(trollyobj), 2, false, false, **********, 0, 1.3)
    NetworkAddPedToSynchronisedScene(ped, scene2, "anim@heists@ornate_bank@grab_cash", "grab", 1.5, -4.0, 1, 16, **********, 0)
    NetworkAddEntityToSynchronisedScene(bag, scene2, "anim@heists@ornate_bank@grab_cash", "bag_grab", 4.0, -8.0, 1)
    NetworkAddEntityToSynchronisedScene(trollyobj, scene2, "anim@heists@ornate_bank@grab_cash", "cart_cash_dissapear", 4.0, -8.0, 1)
    NetworkStartSynchronisedScene(scene2)
    Citizen.Wait(37000)
    local scene3 = NetworkCreateSynchronisedScene(GetEntityCoords(trollyobj), GetEntityRotation(trollyobj), 2, false, false, **********, 0, 1.3)
    NetworkAddPedToSynchronisedScene(ped, scene3, "anim@heists@ornate_bank@grab_cash", "exit", 1.5, -4.0, 1, 16, **********, 0)
    NetworkAddEntityToSynchronisedScene(bag, scene3, "anim@heists@ornate_bank@grab_cash", "bag_exit", 4.0, -8.0, 1)
    NetworkStartSynchronisedScene(scene3)
    -- À la place de créer le prop vide localement, demande au serveur de le faire
    TriggerServerEvent("pc_robbery:pacific:replaceTrolleyWithEmpty", trolleyId, trollyNetId, GetEntityCoords(trollyobj), GetEntityRotation(trollyobj))
    Citizen.Wait(1800)
    DeleteObject(bag)
    SetPedComponentVariation(ped, 5, 45, 0, 0)
    RemoveAnimDict("anim@heists@ornate_bank@grab_cash")
    SetModelAsNoLongerNeeded(emptyobj)
    SetModelAsNoLongerNeeded(baghash)
    disableinput = false
end 

-- Event pour supprimer le prop plein localement si encore présent
RegisterNetEvent('pc_robbery:pacific:trolleyEmptied', function(trolleyNetId)
    local trolley = NetworkGetEntityFromNetworkId(trolleyNetId)
    if trolley and DoesEntityExist(trolley) then
        DeleteEntity(trolley)
    end
end) 