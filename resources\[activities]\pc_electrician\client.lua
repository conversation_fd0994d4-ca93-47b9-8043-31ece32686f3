local QBCore = exports['qb-core']:GetCoreObject()

-- Initialisation de la langue
local Lang = Lang or Locale:new({
    phrases = Translations,
    warnOnMissing = true
})

local ped = nil
local isWorking = false
local currentGroup = nil
local currentVehicle = nil
local repairBlips = {}
local playerNames = {}
local repairedPoints = {} -- Table pour suivre les points déjà réparés
local endActivityOption = nil -- Pour stocker l'option de fin d'activité
local repairZones = {} -- Table pour stocker les zones de réparation
local toolbox = nil -- Pour stocker la boîte à outils
local toolboxCoords = nil -- Pour stocker la position de la boîte à outils
local currentRun = nil -- Pour stocker la run actuelle
local repairingPoints = {} -- Table pour suivre les points en cours de réparation
local startPed = nil -- Variable pour stocker le PNJ de départ
local hasToolbox = false -- Variable pour suivre si le joueur a récupéré la boîte à outils
local toolboxSpawned = false -- Variable pour suivre si la boîte à outils a été créée
local toolboxOnGround = false -- Variable pour suivre si la boîte à outils est au sol
local toolboxGroundCoords = nil -- Coordonnées de la boîte à outils au sol
local weldingTorch = nil -- Variable pour le prop de soudage
local animationThread = nil -- Variable pour contrôler le thread d'animation
local weldingAnimationActive = false -- Variable pour contrôler l'animation de soudage

-- Fonctions utilitaires
local function GetPlayerName(source)
    return playerNames[source] or "Inconnu"
end

local function ShowGroupMenu()
    if not currentGroup then return end
    
    local options = {
        {
            title = "Menu du groupe",
            description = '',
            disabled = true
        }
    }
    
    -- Ajouter le leader
    options[#options + 1] = {
        title = "Leader: " .. GetPlayerName(currentGroup.leader),
        description = "ID: " .. currentGroup.leader,
        disabled = true
    }
    
    -- Ajouter les membres
    for _, memberId in ipairs(currentGroup.members) do
        if memberId ~= currentGroup.leader then
            options[#options + 1] = {
                title = "Membre: " .. GetPlayerName(memberId),
                description = "ID: " .. memberId,
                onSelect = function()
                    if currentGroup.leader == GetPlayerServerId(PlayerId()) then
                        -- Demander confirmation avant de kick
                        local alert = lib.alertDialog({
                            header = 'Confirmation',
                            content = 'Voulez-vous vraiment exclure ' .. GetPlayerName(memberId) .. ' du groupe ?',
                            centered = true,
                            cancel = true
                        })
                        
                        if alert == 'confirm' then
                            TriggerServerEvent('pc_electrician:server:kickMember', memberId)
                        end
                    end
                end,
                canInteract = function()
                    return currentGroup.leader == GetPlayerServerId(PlayerId())
                end
            }
        end
    end
    
    -- Ajouter l'option pour inviter un joueur (uniquement pour le leader)
    if currentGroup.leader == GetPlayerServerId(PlayerId()) then
        options[#options + 1] = {
            title = "Inviter un joueur",
            description = '',
            onSelect = function()
                TriggerServerEvent('pc_electrician:server:getPlayersList')
            end
        }
    end
    
    -- Ajouter l'option pour quitter le groupe
    options[#options + 1] = {
        title = "Quitter le groupe",
        description = '',
        onSelect = function()
            TriggerEvent('pc_electrician:client:leaveGroup')
        end
    }
    
    lib.registerContext({
        id = 'electrician_group_menu',
        title = "Menu du groupe",
        options = options
    })
    
    lib.showContext('electrician_group_menu')
end

local function ShowInviteMenu()
    if not currentGroup then return end
    
    local myId = GetPlayerServerId(PlayerId())
    if currentGroup.leader ~= myId then
        lib.notify({
            title = 'Erreur',
            description = Lang:t('error.not_group_leader'),
            type = 'error'
        })
        return
    end
    
    -- Demander la liste des joueurs au serveur
    TriggerServerEvent('pc_electrician:server:getPlayersList')
end

-- Fonction pour créer la boîte à outils
local function CreateToolbox(coords)
    if toolboxSpawned then
        return
    end
    
    if not currentVehicle then
        return
    end
    
    
    -- Ajouter une zone ox_target sur le véhicule pour récupérer la boîte à outils
    exports.ox_target:addLocalEntity(currentVehicle, {
        {
            name = 'pickup_toolbox',
            icon = 'fas fa-toolbox',
            label = 'Récupérer la boîte à outils',
            distance = 3.0,
            onSelect = function()
                if hasToolbox then
                    lib.notify({
                        title = 'Information',
                        description = 'Vous avez déjà la boîte à outils',
                        type = 'info'
                    })
                    return
                end
                
                -- Créer l'objet boîte à outils et l'attacher au joueur
    local model = GetHashKey('prop_tool_box_04')
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    
    local ped = PlayerPedId()
                toolbox = CreateObject(model, 0.0, 0.0, 0.0, true, true, true)
    
    -- Attacher la boîte à outils au joueur
                AttachEntityToEntity(toolbox, ped, GetPedBoneIndex(ped, 28422), 0.0, 0.0, -0.3, 0.0, 0.0, 130.0, true, true, false, true, 1, true)
    
    -- Charger et jouer l'animation
                RequestAnimDict("anim@heists@narcotics@trash")
                while not HasAnimDictLoaded("anim@heists@narcotics@trash") do
        Wait(0)
    end
    
                TaskPlayAnim(ped, "anim@heists@narcotics@trash", "idle", 8.0, -8.0, -1, 49, 0, false, false, false)
    
    -- Créer une boucle pour maintenir l'animation
                animationThread = true
    CreateThread(function()
                    while animationThread and toolbox and DoesEntityExist(toolbox) and IsEntityAttachedToEntity(toolbox, ped) do
                        if not IsEntityPlayingAnim(ped, "anim@heists@narcotics@trash", "idle", 3) then
                            TaskPlayAnim(ped, "anim@heists@narcotics@trash", "idle", 8.0, -8.0, -1, 49, 0, false, false, false)
            end
            Wait(0)
        end
                    if animationThread then
        ClearPedTasks(ped)
                    end
                end)
                
                hasToolbox = true
                lib.notify({
                    title = 'Succès',
                    description = 'Vous avez récupéré la boîte à outils',
                    type = 'success'
                })
            end,
            canInteract = function()
                -- Vérifier si le joueur est derrière le véhicule
                local playerCoords = GetEntityCoords(PlayerPedId())
                local vehicleCoords = GetEntityCoords(currentVehicle)
                local vehicleHeading = GetEntityHeading(currentVehicle)
                
                -- Calculer la position derrière le véhicule
                local behindVehicle = vector3(
                    vehicleCoords.x - math.sin(math.rad(vehicleHeading)) * 2.0,
                    vehicleCoords.y - math.cos(math.rad(vehicleHeading)) * 2.0,
                    vehicleCoords.z
                )
                
                -- Vérifier si le joueur est dans la zone derrière le véhicule
                local distanceToBehind = #(playerCoords - behindVehicle)
                return distanceToBehind <= 3.0 and not hasToolbox
            end
        },
        {
            name = 'return_toolbox',
            icon = 'fas fa-undo',
            label = 'Remettre la boîte à outils',
            distance = 3.0,
            onSelect = function()
                if not hasToolbox then
                    lib.notify({
                        title = 'Information',
                        description = 'Vous n\'avez pas la boîte à outils',
                        type = 'info'
                    })
                    return
                end
                
                -- Arrêter l'animation et nettoyer
                local ped = PlayerPedId()
                ClearPedTasks(ped)
                
                -- Arrêter le thread d'animation
                if animationThread then
                    animationThread = false
                end
                
                -- Arrêter l'animation de soudage
                weldingAnimationActive = false
                
                -- Supprimer l'objet boîte à outils
                if toolbox then
                    DeleteEntity(toolbox)
                    toolbox = nil
                end
                
                -- Réinitialiser les variables
                hasToolbox = false
                toolboxOnGround = false
                toolboxGroundCoords = nil
                
                lib.notify({
                    title = 'Succès',
                    description = 'Vous avez remis la boîte à outils dans le véhicule',
                    type = 'success'
                })
            end,
            canInteract = function()
                -- Vérifier si le joueur est derrière le véhicule
                local playerCoords = GetEntityCoords(PlayerPedId())
                local vehicleCoords = GetEntityCoords(currentVehicle)
                local vehicleHeading = GetEntityHeading(currentVehicle)
                
                -- Calculer la position derrière le véhicule
                local behindVehicle = vector3(
                    vehicleCoords.x - math.sin(math.rad(vehicleHeading)) * 2.0,
                    vehicleCoords.y - math.cos(math.rad(vehicleHeading)) * 2.0,
                    vehicleCoords.z
                )
                
                -- Vérifier si le joueur est dans la zone derrière le véhicule
                local distanceToBehind = #(playerCoords - behindVehicle)
                return distanceToBehind <= 3.0 and hasToolbox
            end
        }
    })
    
    toolboxSpawned = true
end

-- Fonction pour supprimer la boîte à outils
local function DeleteToolbox()
    
    -- Supprimer l'objet boîte à outils s'il existe
    if toolbox then
        -- Supprimer la zone ox_target de la boîte au sol si elle existe
        if toolboxOnGround then
            exports.ox_target:removeLocalEntity(toolbox, 'pickup_toolbox_ground')
    end
    
    DeleteEntity(toolbox)
    toolbox = nil
    end
    
    -- Supprimer les zones ox_target du véhicule
    if currentVehicle and toolboxSpawned then
        exports.ox_target:removeLocalEntity(currentVehicle, 'pickup_toolbox')
        exports.ox_target:removeLocalEntity(currentVehicle, 'return_toolbox')
    end
    
    toolboxCoords = nil
    toolboxSpawned = false
    hasToolbox = false
    toolboxOnGround = false
    toolboxGroundCoords = nil
end

-- Fonction pour porter la boîte à outils
local function CarryToolbox()
    if not toolbox then return end
    
    local ped = PlayerPedId()
    
    -- Charger l'animation
    RequestAnimDict("anim@heists@narcotics@trash")
    while not HasAnimDictLoaded("anim@heists@narcotics@trash") do
        Wait(0)
    end
    
    -- Attacher la boîte au joueur
    AttachEntityToEntity(toolbox, ped, GetPedBoneIndex(ped, 28422), 0.0, 0.0, -0.3, 0.0, 0.0, 130.0, true, true, false, true, 1, true)
    
    -- Créer une boucle pour maintenir l'animation
    animationThread = true
    CreateThread(function()
        while animationThread and toolbox and DoesEntityExist(toolbox) and IsEntityAttachedToEntity(toolbox, ped) do
            if not IsEntityPlayingAnim(ped, "anim@heists@narcotics@trash", "idle", 3) then
                TaskPlayAnim(ped, "anim@heists@narcotics@trash", "idle", 8.0, -8.0, -1, 49, 0, false, false, false)
            end
            Wait(0)
        end
        if animationThread then
            ClearPedTasks(ped)
        end
    end)
end

-- Fonction pour poser la boîte à outils au sol
local function DropToolboxOnGround()
    if not toolbox or not hasToolbox then return end
    
    local ped = PlayerPedId()
    local playerCoords = GetEntityCoords(ped)
    local playerHeading = GetEntityHeading(ped)
    
    -- Calculer la position à droite du joueur
    local rightSide = vector3(
        playerCoords.x + math.sin(math.rad(playerHeading - 90)) * 1.0,
        playerCoords.y + math.cos(math.rad(playerHeading - 90)) * 1.0,
        playerCoords.z - 1.0
    )
    
    -- Détacher la boîte du joueur
    DetachEntity(toolbox, true, true)
    
    -- Poser la boîte à droite du joueur
    SetEntityCoords(toolbox, rightSide.x, rightSide.y, rightSide.z, true, true, true, false)
    
    -- Arrêter l'animation
    ClearPedTasks(ped)
    
    -- Marquer la boîte comme au sol
    toolboxOnGround = true
    toolboxGroundCoords = rightSide
    hasToolbox = false
    
    -- Ajouter une zone ox_target sur la boîte au sol
    exports.ox_target:addLocalEntity(toolbox, {
        {
            name = 'pickup_toolbox_ground',
            icon = 'fas fa-hand-paper',
            label = 'Récupérer la boîte à outils',
            distance = 2.0,
            onSelect = function()
                PickupToolboxFromGround()
            end
        }
    })
    
    
    lib.notify({
        title = 'Information',
        description = 'Boîte à outils posée au sol',
        type = 'info'
    })
end

-- Fonction pour récupérer la boîte à outils du sol
local function PickupToolboxFromGround()
    if not toolbox or not toolboxOnGround then return end
    
    local ped = PlayerPedId()
    local playerCoords = GetEntityCoords(ped)
    
    -- Vérifier si le joueur est proche de la boîte
    local distance = #(playerCoords - toolboxGroundCoords)
    if distance > 3.0 then
        lib.notify({
            title = 'Erreur',
            description = 'Vous devez être plus proche de la boîte à outils',
            type = 'error'
        })
        return
    end
    
    -- Animation de récupération
    RequestAnimDict("anim@heists@narcotics@trash")
    while not HasAnimDictLoaded("anim@heists@narcotics@trash") do
        Wait(0)
    end
    
    -- Attacher la boîte au joueur
    AttachEntityToEntity(toolbox, ped, GetPedBoneIndex(ped, 28422), 0.0, 0.0, -0.3, 0.0, 0.0, 130.0, true, true, false, true, 1, true)
    
    -- Créer une boucle pour maintenir l'animation
    animationThread = true
    CreateThread(function()
        while animationThread and toolbox and DoesEntityExist(toolbox) and IsEntityAttachedToEntity(toolbox, ped) do
            if not IsEntityPlayingAnim(ped, "anim@heists@narcotics@trash", "idle", 3) then
                TaskPlayAnim(ped, "anim@heists@narcotics@trash", "idle", 8.0, -8.0, -1, 49, 0, false, false, false)
            end
            Wait(0)
        end
        if animationThread then
        ClearPedTasks(ped)
        end
    end)
    
    -- Marquer la boîte comme portée
    toolboxOnGround = false
    toolboxGroundCoords = nil
    hasToolbox = true
    
    
    lib.notify({
        title = 'Succès',
        description = 'Boîte à outils récupérée',
        type = 'success'
    })
end

-- Fonction pour créer le prop de soudage
local function CreateWeldingTorch()
    if weldingTorch then return end
    
    local ped = PlayerPedId()
    local model = GetHashKey('prop_weld_torch')
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    
    weldingTorch = CreateObject(model, 0.0, 0.0, 0.0, true, true, true)
    AttachEntityToEntity(weldingTorch, ped, GetPedBoneIndex(ped, 28422), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, true, true, false, true, 1, true)
    
end

-- Fonction pour supprimer le prop de soudage
local function DeleteWeldingTorch()
    if not weldingTorch then return end
    
    DeleteEntity(weldingTorch)
    weldingTorch = nil
    
end

-- Fonction pour lâcher la boîte à outils
local function DropToolbox()
    if not toolbox then
        return
    end
    
    DetachEntity(toolbox, true, true)
    
    -- Placer la boîte à outils à droite du joueur
    local playerCoords = GetEntityCoords(PlayerPedId())
    local playerHeading = GetEntityHeading(PlayerPedId())
    
    -- Calculer la position à droite du joueur
    local rightSide = vector3(
        playerCoords.x + math.sin(math.rad(playerHeading - 90)) * 1.0,
        playerCoords.y + math.cos(math.rad(playerHeading - 90)) * 1.0,
        playerCoords.z - 1.0
    )
    
    SetEntityCoords(toolbox, rightSide.x, rightSide.y, rightSide.z, true, true, true, false)
end

-- Fonction pour créer le waypoint
local function CreateWaypoint(coords)
    ClearGpsPlayerWaypoint()
    SetNewWaypoint(coords.x, coords.y)
end

-- Fonction pour supprimer le waypoint
local function RemoveWaypoint()
    ClearGpsPlayerWaypoint()
end

-- Fonction de nettoyage
local function CleanupActivity()
    
    -- Supprimer le véhicule
    if currentVehicle then
        QBCore.Functions.DeleteVehicle(currentVehicle)
        currentVehicle = nil
    end
    
    -- Supprimer la boîte à outils
    DeleteToolbox()
    
    -- Supprimer le prop de soudage
    DeleteWeldingTorch()
    
    -- Supprimer les blips
    for _, blip in pairs(repairBlips) do
        RemoveBlip(blip)
    end
    repairBlips = {}
    
    -- Supprimer les zones de réparation
    for _, zone in pairs(repairZones) do
        exports.ox_target:removeZone(zone)
    end
    repairZones = {}
    
    -- Supprimer l'option de fin d'activité
    if endActivityOption then
        exports.ox_target:removeLocalEntity(ped, 'electrician_end_activity')
        endActivityOption = nil
    end
    
    -- Supprimer le waypoint
    RemoveWaypoint()
    
    -- Réinitialiser les variables
    isWorking = false
    repairedPoints = {}
    repairingPoints = {}
    hasToolbox = false
    toolboxSpawned = false
    toolboxOnGround = false
    toolboxGroundCoords = nil
    weldingTorch = nil
    animationThread = nil
    weldingAnimationActive = false
end

-- Fonction pour sélectionner une run aléatoire
local function SelectRandomRun()
    local totalChance = 0
    for _, run in ipairs(Config.Runs) do
        totalChance = totalChance + run.chance
    end
    
    local random = math.random(1, totalChance)
    local currentChance = 0
    
    for _, run in ipairs(Config.Runs) do
        currentChance = currentChance + run.chance
        if random <= currentChance then
            return run
        end
    end
    
    return Config.Runs[1] -- Retourner la première run par défaut
end

-- Fonction pour créer le PNJ de départ
local function CreateStartPed()
    if startPed then return end
    
    local model = `s_m_y_construct_01`
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    
    startPed = CreatePed(4, model, Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y, Config.StartPoint.ped.coords.z - 1.0, Config.StartPoint.ped.coords.w, false, true)
    FreezeEntityPosition(startPed, true)
    SetEntityInvincible(startPed, true)
    SetBlockingOfNonTemporaryEvents(startPed, true)
    
    -- Ajouter l'option pour démarrer l'activité
    exports.ox_target:addLocalEntity(startPed, {
        {
            name = 'start_activity',
            icon = 'fas fa-bolt',
            label = 'Démarrer l\'activité',
            onSelect = function()
                TriggerServerEvent('pc_electrician:server:startActivity')
            end,
            canInteract = function()
                return not isWorking
            end
        },
        {
            name = 'end_activity',
            icon = 'fas fa-times',
            label = 'Terminer l\'activité',
            onSelect = function()
                if lib.alertDialog({
                    header = 'Confirmation',
                    content = 'Êtes-vous sûr de vouloir terminer l\'activité ?',
                    centered = true,
                    cancel = true
                }) then
                    TriggerServerEvent('pc_electrician:server:endActivity')
                    CleanupActivity()
                end
            end,
            canInteract = function()
                return isWorking and isLeader
            end
        }
    })
end

-- Création du ped
CreateThread(function()
    -- Chargement du modèle
    local model = GetHashKey(Config.StartPoint.ped.model)
    
    -- Vérification et chargement du modèle
    if not IsModelInCdimage(model) then
        return
    end
    
    RequestModel(model)
    local timeout = 0
    while not HasModelLoaded(model) and timeout < 50 do
        timeout = timeout + 1
        Wait(100)
    end
    
    if not HasModelLoaded(model) then
        return
    end

    -- Création du ped
    ped = CreatePed(4, model, Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y, Config.StartPoint.ped.coords.z - 1.0, Config.StartPoint.ped.coords.w, false, true)
    
    if not DoesEntityExist(ped) then
        return
    end
    
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    SetPedCanRagdoll(ped, false)
    SetPedCanBeTargetted(ped, false)
    SetPedCanBeDraggedOut(ped, false)
    SetPedCanRagdollFromPlayerImpact(ped, false)
    SetEntityAsMissionEntity(ped, true, true)

    -- Création du blip
    local blip = AddBlipForCoord(Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y, Config.StartPoint.ped.coords.z)
    SetBlipSprite(blip, Config.StartPoint.blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.StartPoint.blip.scale)
    SetBlipColour(blip, Config.StartPoint.blip.color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.StartPoint.blip.label)
    EndTextCommandSetBlipName(blip)

    -- Ajout des options ox_target
    exports.ox_target:addLocalEntity(ped, {
        {
            name = 'electrician_create_group',
            icon = 'fas fa-users',
            label = Lang:t('info.create_group'),
            distance = 1.2,
            onSelect = function()
                TriggerServerEvent('pc_electrician:server:createGroup')
            end,
            canInteract = function()
                return currentGroup == nil
            end
        },
        {
            name = 'electrician_view_group',
            icon = 'fas fa-users',
            label = Lang:t('info.view_group'),
            distance = 1.2,
            onSelect = function()
                ShowGroupMenu()
            end,
            canInteract = function()
                return currentGroup ~= nil
            end
        },
        {
            name = 'electrician_start_activity',
            icon = 'fas fa-truck',
            label = Lang:t('info.start_activity'),
            distance = 1.2,
            onSelect = function()
                if not currentGroup then
                    lib.notify({
                        title = 'Erreur',
                        description = Lang:t('error.not_in_group'),
                        type = 'error'
                    })
                    return
                end
                TriggerServerEvent('pc_electrician:server:startActivity')
            end,
            canInteract = function()
                return currentGroup ~= nil and not isWorking
            end
        }
    })
    
    -- Libération du modèle
    SetModelAsNoLongerNeeded(model)
end)

-- Événements
RegisterNetEvent('pc_electrician:client:groupCreated', function(groupId)
    currentGroup = {
        id = groupId,
        leader = GetPlayerServerId(PlayerId()),
        members = {GetPlayerServerId(PlayerId())}
    }
end)

RegisterNetEvent('pc_electrician:client:groupUpdated', function(group)
    if not group then
        currentGroup = nil
        return
    end
    
    currentGroup = group
    lib.notify({
        title = 'Information',
        description = Lang:t('info.group_members', {count = #group.members}),
        type = 'info'
    })
end)

RegisterNetEvent('pc_electrician:client:leaveGroup', function()
    if not currentGroup then return end
    TriggerServerEvent('pc_electrician:server:leaveGroup')
end)

RegisterNetEvent('pc_electrician:client:invitePlayer', function(data)
    if not data or not data.targetId then return end
    TriggerServerEvent('pc_electrician:server:invitePlayer', data.targetId)
end)

RegisterNetEvent('pc_electrician:client:groupInvitation', function(groupId)
    local options = {
        {
            title = Lang:t('info.group_invitation'),
            description = '',
            disabled = true
        },
        {
            title = Lang:t('info.accept_invitation'),
            description = '',
            onSelect = function()
                TriggerServerEvent('pc_electrician:server:acceptInvitation', groupId)
            end
        },
        {
            title = Lang:t('info.decline_invitation'),
            description = '',
            onSelect = function()
                lib.notify({
                    title = 'Information',
                    description = Lang:t('info.invitation_declined'),
                    type = 'info'
                })
            end
        }
    }
    
    lib.registerContext({
        id = 'electrician_invitation_menu',
        title = Lang:t('info.group_invitation'),
        options = options
    })
    
    lib.showContext('electrician_invitation_menu')
end)

RegisterNetEvent('pc_electrician:client:updatePlayerNames', function(names)
    playerNames = names
end)

RegisterNetEvent('pc_electrician:client:showInviteMenu', function(players)
    if not currentGroup then 
        return 
    end
    
    
    local options = {
        {
            title = "Inviter un joueur",
            description = "Sélectionnez un joueur à proximité",
            disabled = true
        }
    }
    
    local hasPlayers = false
    for playerId, playerName in pairs(players) do
        hasPlayers = true
        options[#options + 1] = {
            title = playerName,
            description = "ID: " .. playerId,
            onSelect = function()
                TriggerServerEvent('pc_electrician:server:invitePlayer', playerId)
            end
        }
    end
    
    if not hasPlayers then
        options[#options + 1] = {
            title = "Aucun joueur à proximité",
            description = "Les joueurs doivent être dans un rayon de 10 mètres",
            disabled = true
        }
    end
    
    lib.registerContext({
        id = 'electrician_invite_menu',
        title = "Inviter un joueur",
        options = options
    })
    
    lib.showContext('electrician_invite_menu')
end)

RegisterNetEvent('pc_electrician:client:activityStarted', function(isLeader)
    if isWorking then return end
    isWorking = true
    repairedPoints = {} -- Réinitialiser les points réparés
    hasToolbox = false -- Réinitialiser la boîte à outils
    toolboxSpawned = false -- Réinitialiser le statut de création de la boîte à outils
    
    -- Sélectionner une run aléatoire
    currentRun = SelectRandomRun()
    
    -- Partager la run sélectionnée avec le serveur
    TriggerServerEvent('pc_electrician:server:setCurrentRun', currentRun.name)
    
    
    -- Spawn du véhicule pour le leader
    if isLeader then
        local vehicleConfig = Config.Vehicles[1]
        QBCore.Functions.SpawnVehicle(vehicleConfig.model, function(vehicle)
            SetEntityHeading(vehicle, vehicleConfig.coords.w)
            -- Gestion du carburant
            local plate = QBCore.Functions.GetPlate(vehicle)
            TriggerServerEvent('qb-fuel:server:SetFuel', plate, 100.0)
            -- Donner les clés du véhicule
            TriggerEvent('vehiclekeys:client:SetOwner', plate)
            currentVehicle = vehicle
            
            -- Partager l'information du véhicule avec les autres membres du groupe
            local attempts = 0
            local maxAttempts = 10
            
            CreateThread(function()
                while attempts < maxAttempts do
                    local netId = NetworkGetNetworkIdFromEntity(vehicle)
                    if netId and netId > 0 then
                        TriggerServerEvent('pc_electrician:server:shareVehicleInfo', netId)
                        break
                    else
                        attempts = attempts + 1
                        Wait(1000)
                    end
                end
            end)
            
            -- Créer la boîte à outils après le spawn du véhicule
            Wait(1000) -- Attendre un peu pour s'assurer que le véhicule est bien créé
            CreateToolbox()
        end, vehicleConfig.coords, true)
    end
    
    -- Création des blips pour les points de réparation
    for i, point in ipairs(currentRun.repairPoints) do
        -- Création du blip
        local blip = AddBlipForCoord(point.coords.x, point.coords.y, point.coords.z)
        SetBlipSprite(blip, 1)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.7)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(point.label)
        EndTextCommandSetBlipName(blip)
        repairBlips[i] = blip
        
        -- Ajout du point de réparation
        local zone = exports.ox_target:addSphereZone({
            coords = point.coords,
            radius = 2.0,
            options = {
                {
                    name = 'repair_electrician_' .. i,
                    icon = 'fas fa-wrench',
                    label = Lang:t('info.repairing'),
                    distance = 1.2,
                    onSelect = function()
                        if not isWorking then 
                            return 
                        end
                        
                        if not currentVehicle then 
                            return 
                        end
                        
                        -- Vérifier si le point a déjà été réparé
                        if repairedPoints[i] then
                            lib.notify({
                                title = 'Information',
                                description = 'Ce point a déjà été réparé',
                                type = 'info'
                            })
                            return
                        end
                        
                        -- Vérifier si le joueur a la boîte à outils
                        if not hasToolbox then
                            lib.notify({
                                title = 'Erreur',
                                description = 'Vous devez d\'abord récupérer la boîte à outils derrière le véhicule',
                                type = 'error'
                            })
                            return
                        end
                        
                        -- Vérifier si le joueur est à proximité du camion
                        local playerCoords = GetEntityCoords(PlayerPedId())
                        local vehicleCoords = GetEntityCoords(currentVehicle)
                        local distance = #(playerCoords - vehicleCoords)
                        
                        if distance > 50.0 then
                            lib.notify({
                                title = 'Erreur',
                                description = 'Vous devez être à proximité du camion pour réparer',
                                type = 'error'
                            })
                            return
                        end
                        
                        -- Vérifier si le point est disponible
                        TriggerServerEvent('pc_electrician:server:checkPointAvailable', i)
                    end
                }
            }
        })
        repairZones[i] = zone
    end
    
    -- Créer le waypoint vers le premier point
    if #currentRun.repairPoints > 0 then
        TriggerServerEvent('pc_electrician:server:updateWaypoint', 1)
    end
    
    -- Notification de début
    lib.notify({
        title = 'Information',
        description = Lang:t('success.activity_started'),
        type = 'success'
    })
    
    -- Ajouter l'option pour terminer l'activité dès le début
    if not endActivityOption then
        endActivityOption = exports.ox_target:addLocalEntity(ped, {
            {
                name = 'electrician_end_activity',
                icon = 'fas fa-check-circle',
                label = 'Terminer l\'activité',
                onSelect = function()
                    if not isWorking then return end
                    
                    -- Vérifier si tous les points sont réparés
                    local allPointsRepaired = true
                    local remainingPoints = 0
                    for i = 1, #currentRun.repairPoints do
                        if not repairedPoints[i] then
                            allPointsRepaired = false
                            remainingPoints = remainingPoints + 1
                        end
                    end
                    
                    -- Vérifier la distance au véhicule
                    local isNearVehicle = false
                    if currentVehicle then
                        local playerCoords = GetEntityCoords(PlayerPedId())
                        local vehicleCoords = GetEntityCoords(currentVehicle)
                        local distance = #(playerCoords - vehicleCoords)
                        isNearVehicle = distance <= 50.0
                    end
                    
                    -- Déterminer le message de confirmation
                    local alertMessage = ""
                    local shouldConfirm = false
                    
                    if not allPointsRepaired and not isNearVehicle then
                        alertMessage = "Vous n'avez pas réparé tous les points (" .. remainingPoints .. " restant(s)) ET le véhicule est trop loin. Si vous terminez maintenant, vous ne recevrez aucune récompense. Voulez-vous quand même terminer ?"
                        shouldConfirm = true
                    elseif not allPointsRepaired then
                        alertMessage = "Vous n'avez pas réparé tous les points (" .. remainingPoints .. " restant(s)). Si vous terminez maintenant, vous ne recevrez aucune récompense. Voulez-vous quand même terminer ?"
                        shouldConfirm = true
                    elseif not isNearVehicle then
                        alertMessage = "Le véhicule est trop loin. Si vous terminez maintenant, vous ne recevrez aucune récompense. Voulez-vous quand même terminer ?"
                        shouldConfirm = true
                    end
                    
                    if shouldConfirm then
                        local alert = lib.alertDialog({
                            header = 'Confirmation',
                            content = alertMessage,
                            centered = true,
                            cancel = true
                        })
                        
                        if alert == 'confirm' then
                            TriggerServerEvent('pc_electrician:server:endActivity', false)
                            CleanupActivity()
                        end
                    else
                        -- Conditions optimales : tous les points réparés et véhicule proche
                        TriggerServerEvent('pc_electrician:server:endActivity', true)
                        CleanupActivity()
                    end
                end,
                canInteract = function()
                    return isWorking
                end
            }
        })
    end
end)

-- Nouvel événement pour mettre à jour le waypoint
RegisterNetEvent('pc_electrician:client:updateWaypoint', function(pointIndex)
    if not currentRun or not currentRun.repairPoints[pointIndex] then return end
    
    local coords = currentRun.repairPoints[pointIndex].coords
    SetNewWaypoint(coords.x, coords.y)
end)

RegisterNetEvent('pc_electrician:client:pointRepaired', function(pointIndex)
    repairedPoints[pointIndex] = true
    
    -- Supprimer le blip et la zone
    if repairBlips[pointIndex] then
        RemoveBlip(repairBlips[pointIndex])
        repairBlips[pointIndex] = nil
    end
    
    if repairZones[pointIndex] then
        exports.ox_target:removeZone(repairZones[pointIndex])
        repairZones[pointIndex] = nil
    end
    
    -- Récupérer automatiquement la boîte à outils si elle est au sol
    if toolboxOnGround and toolbox then
        local ped = PlayerPedId()
        local playerCoords = GetEntityCoords(ped)
        local distance = #(playerCoords - toolboxGroundCoords)
        
        if distance <= 5.0 then
            PickupToolboxFromGround()
        else
            lib.notify({
                title = 'Information',
                description = 'Récupérez la boîte à outils pour continuer',
                type = 'info'
            })
        end
    end
    
    -- Vérifier s'il reste des points à réparer
    local remainingPoints = 0
    local nextPoint = nil
    for i = 1, #currentRun.repairPoints do
        if not repairedPoints[i] then
            remainingPoints = remainingPoints + 1
            if not nextPoint then
                nextPoint = i
            end
        end
    end
    
    if nextPoint then
        -- Mettre à jour le waypoint vers le prochain point pour tous les joueurs
        TriggerServerEvent('pc_electrician:server:updateWaypoint', nextPoint)
        
        lib.notify({
            title = 'Information',
            description = 'Point réparé ! ' .. remainingPoints .. ' point(s) restant(s)',
            type = 'success'
        })
    else
        -- Tous les points ont été réparés
        ClearGpsPlayerWaypoint()
        -- Mettre à jour le waypoint vers le point de départ
        SetNewWaypoint(Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y)
        
        lib.notify({
            title = 'Félicitations',
            description = Lang:t('success.all_points_repaired'),
            type = 'success'
        })
    end
end)

-- Nouvel événement pour gérer la disponibilité des points
RegisterNetEvent('pc_electrician:client:pointAvailable', function(pointIndex)
    local point = currentRun.repairPoints[pointIndex]
    if not point then 
        return 
    end
    
    -- Vérifier si le joueur a la boîte à outils
    if not hasToolbox and not toolboxOnGround then
        lib.notify({
            title = 'Erreur',
            description = 'Vous devez d\'abord récupérer la boîte à outils derrière le véhicule',
            type = 'error'
        })
        return 
    end
    
    -- Vérifier si le joueur est à proximité du camion
    local playerCoords = GetEntityCoords(PlayerPedId())
    local vehicleCoords = GetEntityCoords(currentVehicle)
    local distance = #(playerCoords - vehicleCoords)
    
    if distance > 50.0 then
        lib.notify({
            title = 'Erreur',
            description = 'Vous devez être à proximité du camion pour réparer',
            type = 'error'
        })
        return
    end
    
    -- Poser la boîte à outils au sol si le joueur la porte
    if hasToolbox then
        DropToolboxOnGround()
    end
    
    
    -- Créer le prop de soudage
    CreateWeldingTorch()
    
    -- Démarrer l'animation de soudage
    local ped = PlayerPedId()
    RequestAnimDict("amb@world_human_welding@male@base")
    while not HasAnimDictLoaded("amb@world_human_welding@male@base") do
        Wait(0)
    end
    
    -- Créer une boucle pour maintenir l'animation pendant le minigame
    weldingAnimationActive = true
    CreateThread(function()
        TaskPlayAnim(ped, "amb@world_human_welding@male@base", "base", 8.0, -8.0, -1, 49, 0, false, false, false)
        
        -- Maintenir l'animation pendant le minigame
        while weldingAnimationActive do
            if not IsEntityPlayingAnim(ped, "amb@world_human_welding@male@base", "base", 3) then
                TaskPlayAnim(ped, "amb@world_human_welding@male@base", "base", 8.0, -8.0, -1, 49, 0, false, false, false)
            end
            Wait(0)
        end
        ClearPedTasks(ped)
    end)
    
    -- Minigame de réparation électrique
    local success = exports["ez_electricminigame"]:WiringFix(30) -- 30 secondes de countdown
    
    -- Arrêter l'animation après le minigame
    weldingAnimationActive = false
    ClearPedTasks(ped)
    
    -- Supprimer le prop de soudage
    DeleteWeldingTorch()
    
    if success then
        TriggerServerEvent('pc_electrician:server:completeRepair', pointIndex)
    else
        lib.notify({
            title = 'Erreur',
            description = 'Réparation échouée ou annulée',
            type = 'error'
        })
    end
end)

-- Nouvel événement pour gérer la disponibilité des points
RegisterNetEvent('pc_electrician:client:checkPointAvailable', function(pointIndex)
    local point = currentRun.repairPoints[pointIndex]
    if not point then 
        return 
    end
    
    -- Vérifier si le point est déjà réparé
    if repairedPoints[pointIndex] then
        TriggerClientEvent('pc_electrician:client:pointNotAvailable', source, 'already_repaired')
        return
    end
    
    -- Vérifier si le point est en cours de réparation
    if repairingPoints[pointIndex] then
        TriggerClientEvent('pc_electrician:client:pointNotAvailable', source, 'being_repaired')
        return
    end
    
    -- Marquer le point comme en cours de réparation
    repairingPoints[pointIndex] = true
    
    -- Notifier le client que le point est disponible
    TriggerClientEvent('pc_electrician:client:pointAvailable', source, pointIndex)
end)

RegisterNetEvent('pc_electrician:client:pointNotAvailable', function(reason)
    if reason == 'already_repaired' then
        lib.notify({
            title = 'Information',
            description = 'Ce point a déjà été réparé',
            type = 'info'
        })
    elseif reason == 'being_repaired' then
        lib.notify({
            title = 'Information',
            description = 'Ce point est en cours de réparation par un autre joueur',
            type = 'info'
        })
    end
end)

-- Nouvel événement pour recevoir l'information du véhicule
RegisterNetEvent('pc_electrician:client:setVehicle', function(netId)
    
    -- Attendre que le véhicule soit disponible
    local attempts = 0
    local function tryGetVehicle()
        if attempts >= 10 then
            return
        end
        
        local vehicle = NetworkGetEntityFromNetworkId(netId)
        if DoesEntityExist(vehicle) then
            currentVehicle = vehicle
            
            -- Créer la boîte à outils pour les autres joueurs
            Wait(1000) -- Attendre un peu pour s'assurer que le véhicule est bien synchronisé
            CreateToolbox()
        else
            attempts = attempts + 1
            Wait(1000)
            tryGetVehicle()
        end
    end
    
    tryGetVehicle()
end)

-- Nettoyage
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName ~= GetCurrentResourceName() then return end
    CleanupActivity()
end) 