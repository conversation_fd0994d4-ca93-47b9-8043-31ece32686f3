fx_version 'cerulean'
game 'gta5'
lua54 'yes'

author 'President <PERSON>'
description 'Système de braquage de dépanneurs'
version '1.0.0'

shared_scripts {
	'@ox_lib/init.lua',
    'config.lua'
}

client_scripts {
    'bank.lua',
    'store.lua',
    'paleto.lua',
    'vangelico.lua',
    'pacific.lua'
}

server_scripts {
    'server.lua',
    'create_bank_doors.lua'
}

dependencies {
    'qb-core',
    'ox_target',
    'ox_lib',
    'ps-ui'
}
