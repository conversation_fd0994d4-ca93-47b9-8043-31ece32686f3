local QBCore = exports['qb-core']:GetCoreObject()

-- Initialisation de la langue
local Lang = Lang or Locale:new({
    phrases = Translations,
    warnOnMissing = true
})

local ped = nil
local isWorking = false
local currentGroup = nil
local currentVehicle = nil
local deliveryBlips = {}
local playerNames = {}
local deliveredPoints = {} -- Table pour suivre les points déjà livrés
local endActivityOption = nil -- Pour stocker l'option de fin d'activité
local deliveryZones = {} -- Table pour stocker les zones de livraison
local deliveryBox = nil -- Pour stocker la boîte de livraison
local deliveryBoxCoords = nil -- Pour stocker la position de la boîte de livraison
local currentRun = nil -- Pour stocker la run actuelle

-- Fonctions utilitaires
local function GetPlayerName(source)
    return playerNames[source] or "Inconnu"
end

local function ShowGroupMenu()
    if not currentGroup then return end
    
    local options = {
        {
            title = "Menu du groupe",
            description = '',
            disabled = true
        }
    }
    
    -- Ajouter le leader
    options[#options + 1] = {
        title = "Leader: " .. GetPlayerName(currentGroup.leader),
        description = "ID: " .. currentGroup.leader,
        disabled = true
    }
    
    -- Ajouter les membres
    for _, memberId in ipairs(currentGroup.members) do
        if memberId ~= currentGroup.leader then
            options[#options + 1] = {
                title = "Membre: " .. GetPlayerName(memberId),
                description = "ID: " .. memberId,
                onSelect = function()
                    if currentGroup.leader == GetPlayerServerId(PlayerId()) then
                        -- Demander confirmation avant de kick
                        local alert = lib.alertDialog({
                            header = 'Confirmation',
                            content = 'Voulez-vous vraiment exclure ' .. GetPlayerName(memberId) .. ' du groupe ?',
                            centered = true,
                            cancel = true
                        })
                        
                        if alert == 'confirm' then
                            TriggerServerEvent('pc_delivery:server:kickMember', memberId)
                        end
                    end
                end,
                canInteract = function()
                    return currentGroup.leader == GetPlayerServerId(PlayerId())
                end
            }
        end
    end
    
    -- Ajouter l'option pour inviter un joueur (uniquement pour le leader)
    if currentGroup.leader == GetPlayerServerId(PlayerId()) then
        options[#options + 1] = {
            title = "Inviter un joueur",
            description = '',
            onSelect = function()
                TriggerServerEvent('pc_delivery:server:getPlayersList')
            end
        }
    end
    
    -- Ajouter l'option pour quitter le groupe
    options[#options + 1] = {
        title = "Quitter le groupe",
        description = '',
        onSelect = function()
            TriggerEvent('pc_delivery:client:leaveGroup')
        end
    }
    
    lib.registerContext({
        id = 'garbage_group_menu',
        title = "Menu du groupe",
        options = options
    })
    
    lib.showContext('garbage_group_menu')
end

local function ShowInviteMenu()
    if not currentGroup then return end
    
    local myId = GetPlayerServerId(PlayerId())
    if currentGroup.leader ~= myId then
        lib.notify({
            title = 'Erreur',
            description = Lang:t('error.not_group_leader'),
            type = 'error'
        })
        return
    end
    
    -- Demander la liste des joueurs au serveur
    TriggerServerEvent('pc_delivery:server:getPlayersList')
end

-- Fonction pour créer la boîte de livraison
local function CreateDeliveryBox(coords)
    if deliveryBox then
        DeleteObject(deliveryBox)
    end
    
    local model = GetHashKey('prop_cs_box_clothes') -- Boîte de vêtements
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    
    local ped = PlayerPedId()
    deliveryBox = CreateObject(model, coords.x, coords.y, coords.z, true, true, true)
    
    -- Attacher directement la boîte au joueur (position ajustée)
    AttachEntityToEntity(deliveryBox, ped, GetPedBoneIndex(ped, 28422), 0.0, -0.1, -0.1, 0.0, 0.0, 0.0, true, true, false, true, 1, true)
    
    -- Charger et jouer l'animation de portage
    RequestAnimDict("anim@heists@box_carry@")
    while not HasAnimDictLoaded("anim@heists@box_carry@") do
        Wait(0)
    end
    
    TaskPlayAnim(ped, "anim@heists@box_carry@", "idle", 8.0, -8.0, -1, 49, 0, false, false, false)
    
    -- Créer une boucle pour maintenir l'animation
    CreateThread(function()
        while deliveryBox and DoesEntityExist(deliveryBox) and IsEntityAttachedToEntity(deliveryBox, ped) do
            if not IsEntityPlayingAnim(ped, "anim@heists@box_carry@", "idle", 3) then
                TaskPlayAnim(ped, "anim@heists@box_carry@", "idle", 8.0, -8.0, -1, 49, 0, false, false, false)
            end
            Wait(0)
        end
        ClearPedTasks(ped)
    end)
    
    deliveryBoxCoords = coords
end

-- Fonction pour supprimer la boîte de livraison
local function DeleteDeliveryBox()
    if deliveryBox then
        DeleteObject(deliveryBox)
        deliveryBox = nil
        deliveryBoxCoords = nil
    end
end

-- Fonction pour ajouter l'ox_target de prise de boîte au véhicule
local function AddBoxTargetToVehicle(vehicle)
    if not vehicle or not DoesEntityExist(vehicle) then
        return
    end
        
    -- Ajouter l'ox_target directement sur le véhicule
    exports.ox_target:addLocalEntity(vehicle, {
        {
            name = 'take_box_from_truck',
            icon = 'fas fa-box',
            label = 'Prendre une boîte',
            onSelect = function()
                if deliveryBox then
                    lib.notify({
                        title = 'Information',
                        description = 'Vous portez déjà une boîte',
                        type = 'info'
                    })
                    return
                end
                
                -- Barre de progression pour prendre la boîte
                if lib.progressBar({
                    duration = 3000,
                    label = 'Prise de la boîte...',
                    useWhileDead = false,
                    canCancel = true,
                    disable = {
                        car = true,
                        move = true,
                        combat = true
                    },
                    anim = {
                        dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
                        clip = 'machinic_loop_mechandplayer'
                    },
                }) then
                    -- Créer la boîte et l'attacher au joueur
                    CreateDeliveryBox(GetEntityCoords(PlayerPedId()))
                    
                    lib.notify({
                        title = 'Information',
                        description = 'Boîte prise ! Allez à un point de livraison.',
                        type = 'success'
                    })
                end
            end,
            canInteract = function()
                
                if not isWorking then
                    return false
                end
                
                -- Vérifier si le joueur est derrière le véhicule
                local playerPed = PlayerPedId()
                local playerCoords = GetEntityCoords(playerPed)
                local vehicleCoords = GetEntityCoords(vehicle)
                local vehicleHeading = GetEntityHeading(vehicle)
                
                -- Calculer la position relative du joueur par rapport au véhicule
                local relativeX = playerCoords.x - vehicleCoords.x
                local relativeY = playerCoords.y - vehicleCoords.y
                
                -- Convertir en coordonnées locales du véhicule
                local headingRad = math.rad(vehicleHeading)
                local localX = relativeX * math.cos(-headingRad) - relativeY * math.sin(-headingRad)
                local localY = relativeX * math.sin(-headingRad) + relativeY * math.cos(-headingRad)
                
                -- Vérifier si le joueur est derrière le véhicule (localY négatif) et à une distance raisonnable
                local isBehind = localY < -1.0 and localY > -10.0 -- Entre 1 et 10 mètres derrière (plus permissif)
                local isClose = math.abs(localX) < 4.0 -- À moins de 4 mètres sur les côtés (plus permissif)
                local distance = #(playerCoords - vehicleCoords)
                local isInRange = distance <= 5.0 -- Distance maximale de 5 mètres (plus permissif)
                                
                return isBehind and isClose and isInRange and deliveryBox == nil
            end
        },
        {
            name = 'return_box_to_truck',
            icon = 'fas fa-undo',
            label = 'Remettre la boîte',
            onSelect = function()
                
                -- Barre de progression pour remettre la boîte
                if lib.progressBar({
                    duration = 2000,
                    label = 'Remise de la boîte...',
                    useWhileDead = false,
                    canCancel = true,
                    disable = {
                        car = true,
                        move = true,
                        combat = true
                    },
                    anim = {
                        dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
                        clip = 'machinic_loop_mechandplayer'
                    },
                }) then
                    -- Supprimer la boîte
                    DeleteDeliveryBox()
                    
                    lib.notify({
                        title = 'Information',
                        description = 'Boîte remise dans le camion.',
                        type = 'success'
                    })
                end
            end,
            canInteract = function()
                if not isWorking then
                    return false
                end
                
                -- Vérifier si le joueur est derrière le véhicule
                local playerPed = PlayerPedId()
                local playerCoords = GetEntityCoords(playerPed)
                local vehicleCoords = GetEntityCoords(vehicle)
                local vehicleHeading = GetEntityHeading(vehicle)
                
                -- Calculer la position relative du joueur par rapport au véhicule
                local relativeX = playerCoords.x - vehicleCoords.x
                local relativeY = playerCoords.y - vehicleCoords.y
                
                -- Convertir en coordonnées locales du véhicule
                local headingRad = math.rad(vehicleHeading)
                local localX = relativeX * math.cos(-headingRad) - relativeY * math.sin(-headingRad)
                local localY = relativeX * math.sin(-headingRad) + relativeY * math.cos(-headingRad)
                
                -- Vérifier si le joueur est derrière le véhicule (localY négatif) et à une distance raisonnable
                local isBehind = localY < -1.0 and localY > -10.0 -- Entre 1 et 10 mètres derrière (plus permissif)
                local isClose = math.abs(localX) < 4.0 -- À moins de 4 mètres sur les côtés (plus permissif)
                local distance = #(playerCoords - vehicleCoords)
                local isInRange = distance <= 5.0 -- Distance maximale de 5 mètres (plus permissif)
                
                return isBehind and isClose and isInRange and deliveryBox ~= nil
            end
        }
    })
end

-- Fonction pour lâcher la boîte de livraison
local function DropDeliveryBox()
    if not deliveryBox then return end
    
    local ped = PlayerPedId()
    DetachEntity(deliveryBox, true, true)
    PlaceObjectOnGroundProperly(deliveryBox)
    FreezeEntityPosition(deliveryBox, true)
    ClearPedTasks(ped)
    
    -- Arrêter l'animation
    StopAnimTask(ped, "anim@heists@narcotics@trash", "walk", 1.0)
end

-- Fonction pour créer le waypoint
local function CreateWaypoint(coords)
    ClearGpsPlayerWaypoint()
    SetNewWaypoint(coords.x, coords.y)
end

-- Fonction pour supprimer le waypoint
local function RemoveWaypoint()
    ClearGpsPlayerWaypoint()
end

-- Fonction de nettoyage
local function CleanupActivity()
    
    -- Supprimer le véhicule
    if currentVehicle then
        QBCore.Functions.DeleteVehicle(currentVehicle)
        currentVehicle = nil
    end
    
    -- Supprimer la boîte de livraison
    DeleteDeliveryBox()
    
    -- Supprimer les blips
    for _, blip in pairs(deliveryBlips) do
        RemoveBlip(blip)
    end
    deliveryBlips = {}
    
    -- Supprimer les zones de livraison
    for _, zone in pairs(deliveryZones) do
        exports.ox_target:removeZone(zone)
    end
    deliveryZones = {}
    
    -- Supprimer l'option de fin d'activité
    if endActivityOption then
        exports.ox_target:removeLocalEntity(ped, 'garbage_end_activity')
        endActivityOption = nil
    end
    
    -- Supprimer le waypoint
    RemoveWaypoint()
    
    -- Réinitialiser les variables
    isWorking = false
    deliveredPoints = {}
end

-- Fonction pour sélectionner une run aléatoire
local function SelectRandomRun()
    local totalChance = 0
    for _, run in ipairs(Config.Runs) do
        totalChance = totalChance + run.chance
    end
    
    local random = math.random(1, totalChance)
    local currentChance = 0
    
    for _, run in ipairs(Config.Runs) do
        currentChance = currentChance + run.chance
        if random <= currentChance then
            return run
        end
    end
    
    return Config.Runs[1] -- Fallback sur la première run si quelque chose ne va pas
end

-- Création du ped
CreateThread(function()
    -- Chargement du modèle
    local model = GetHashKey(Config.StartPoint.ped.model)
    
    -- Vérification et chargement du modèle
    if not IsModelInCdimage(model) then
        return
    end
    
    RequestModel(model)
    local timeout = 0
    while not HasModelLoaded(model) and timeout < 50 do
        timeout = timeout + 1
        Wait(100)
    end
    
    if not HasModelLoaded(model) then
        return
    end

    -- Création du ped
    ped = CreatePed(4, model, Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y, Config.StartPoint.ped.coords.z - 1.0, Config.StartPoint.ped.coords.w, false, true)
    
    if not DoesEntityExist(ped) then
        return
    end
    
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    SetPedCanRagdoll(ped, false)
    SetPedCanBeTargetted(ped, false)
    SetPedCanBeDraggedOut(ped, false)
    SetPedCanRagdollFromPlayerImpact(ped, false)
    SetEntityAsMissionEntity(ped, true, true)

    -- Création du blip
    local blip = AddBlipForCoord(Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y, Config.StartPoint.ped.coords.z)
    SetBlipSprite(blip, Config.StartPoint.blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.StartPoint.blip.scale)
    SetBlipColour(blip, Config.StartPoint.blip.color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.StartPoint.blip.label)
    EndTextCommandSetBlipName(blip)

    -- Ajout des options ox_target
    exports.ox_target:addLocalEntity(ped, {
        {
            name = 'garbage_create_group',
            icon = 'fas fa-users',
            label = Lang:t('info.create_group'),
            onSelect = function()
                TriggerServerEvent('pc_delivery:server:createGroup')
            end,
            canInteract = function()
                if currentGroup ~= nil then
                    return false
                end
                
                -- Vérifier la distance (1.2 mètres)
                local playerCoords = GetEntityCoords(PlayerPedId())
                local pedCoords = GetEntityCoords(ped)
                local distance = #(playerCoords - pedCoords)
                return distance <= 1.2
            end
        },
        {
            name = 'garbage_view_group',
            icon = 'fas fa-users',
            label = Lang:t('info.view_group'),
            onSelect = function()
                ShowGroupMenu()
            end,
            canInteract = function()
                if currentGroup == nil then
                    return false
                end
                
                -- Vérifier la distance (1.2 mètres)
                local playerCoords = GetEntityCoords(PlayerPedId())
                local pedCoords = GetEntityCoords(ped)
                local distance = #(playerCoords - pedCoords)
                return distance <= 1.2
            end
        },
        {
            name = 'garbage_start_activity',
            icon = 'fas fa-truck',
            label = Lang:t('info.start_activity'),
            onSelect = function()
                if not currentGroup then
                    lib.notify({
                        title = 'Erreur',
                        description = Lang:t('error.not_in_group'),
                        type = 'error'
                    })
                    return
                end
                TriggerServerEvent('pc_delivery:server:startActivity')
            end,
            canInteract = function()
                if currentGroup == nil or isWorking then
                    return false
                end
                
                -- Vérifier la distance (1.2 mètres)
                local playerCoords = GetEntityCoords(PlayerPedId())
                local pedCoords = GetEntityCoords(ped)
                local distance = #(playerCoords - pedCoords)
                return distance <= 1.2
            end
        }
    })
    
    -- Libération du modèle
    SetModelAsNoLongerNeeded(model)
end)

-- Événements
RegisterNetEvent('pc_delivery:client:groupCreated', function(groupId)
    currentGroup = {
        id = groupId,
        leader = GetPlayerServerId(PlayerId()),
        members = {GetPlayerServerId(PlayerId())}
    }
    lib.notify({
        title = 'Succès',
        description = Lang:t('success.group_created'),
        type = 'success'
    })
end)

RegisterNetEvent('pc_delivery:client:groupUpdated', function(group)
    if not group then
        currentGroup = nil
        return
    end
    
    currentGroup = group
    lib.notify({
        title = 'Information',
        description = Lang:t('info.group_members', {count = #group.members}),
        type = 'info'
    })
end)

RegisterNetEvent('pc_delivery:client:leaveGroup', function()
    if not currentGroup then return end
    TriggerServerEvent('pc_delivery:server:leaveGroup')
end)

RegisterNetEvent('pc_delivery:client:invitePlayer', function(data)
    if not data or not data.targetId then return end
    TriggerServerEvent('pc_delivery:server:invitePlayer', data.targetId)
end)

RegisterNetEvent('pc_delivery:client:groupInvitation', function(groupId)
    local options = {
        {
            title = Lang:t('info.group_invitation'),
            description = '',
            disabled = true
        },
        {
            title = Lang:t('info.accept_invitation'),
            description = '',
            onSelect = function()
                TriggerServerEvent('pc_delivery:server:acceptInvitation', groupId)
            end
        },
        {
            title = Lang:t('info.decline_invitation'),
            description = '',
            onSelect = function()
                lib.notify({
                    title = 'Information',
                    description = Lang:t('info.invitation_declined'),
                    type = 'info'
                })
            end
        }
    }
    
    lib.registerContext({
        id = 'delivery_invitation_menu',
        title = Lang:t('info.group_invitation'),
        options = options
    })
    
    lib.showContext('delivery_invitation_menu')
end)

RegisterNetEvent('pc_delivery:client:updatePlayerNames', function(names)
    playerNames = names
end)

RegisterNetEvent('pc_delivery:client:showInviteMenu', function(players)
    if not currentGroup then 
        return 
    end
        
    local options = {
        {
            title = "Inviter un joueur",
            description = "Sélectionnez un joueur à proximité",
            disabled = true
        }
    }
    
    local hasPlayers = false
    for playerId, playerName in pairs(players) do
        hasPlayers = true
        options[#options + 1] = {
            title = playerName,
            description = "ID: " .. playerId,
            onSelect = function()
                TriggerServerEvent('pc_delivery:server:invitePlayer', playerId)
            end
        }
    end
    
    if not hasPlayers then
        options[#options + 1] = {
            title = "Aucun joueur à proximité",
            description = "Les joueurs doivent être dans un rayon de 10 mètres",
            disabled = true
        }
    end
    
    lib.registerContext({
        id = 'delivery_invite_menu',
        title = "Inviter un joueur",
        options = options
    })
    
    lib.showContext('delivery_invite_menu')
end)

RegisterNetEvent('pc_delivery:client:activityStarted', function(isLeader)
    if isWorking then return end
    isWorking = true
    deliveredPoints = {} -- Réinitialiser les points livrés
    
    -- Sélectionner une run aléatoire
    currentRun = SelectRandomRun()
    
    -- Partager la run sélectionnée avec le serveur
    TriggerServerEvent('pc_delivery:server:setCurrentRun', currentRun.name)
    
    -- Notification du type de run
    lib.notify({
        title = 'Information',
        description = 'Vous avez obtenu une ' .. currentRun.name,
        type = 'info'
    })
    
    -- Spawn du véhicule uniquement si c'est le leader
    if isLeader then
        local vehicleConfig = Config.Vehicles[1]
        QBCore.Functions.SpawnVehicle(vehicleConfig.model, function(vehicle)
            currentVehicle = vehicle
            SetEntityHeading(vehicle, vehicleConfig.coords.w)
            
            -- Gestion du carburant
            local plate = QBCore.Functions.GetPlate(vehicle)
            TriggerServerEvent('qb-fuel:server:SetFuel', plate, 100.0)
            
            -- Donner les clés du véhicule
            TriggerEvent('vehiclekeys:client:SetOwner', plate)
            
            -- Attendre que le véhicule soit complètement chargé
            local attempts = 0
            local function tryShareVehicle()
                if attempts >= 10 then
                    return
                end
                
                local netId = NetworkGetNetworkIdFromEntity(vehicle)
                if netId and netId > 0 then
                    TriggerServerEvent('pc_delivery:server:shareVehicleInfo', netId)
                else
                    attempts = attempts + 1
                    Wait(1000)
                    tryShareVehicle()
                end
            end
            
            tryShareVehicle()
            
            -- Ajouter l'ox_target pour prendre des boîtes pour le leader
            AddBoxTargetToVehicle(currentVehicle)
            
            -- Ajouter l'option pour terminer l'activité
            if not endActivityOption then
                endActivityOption = exports.ox_target:addLocalEntity(ped, {
                    {
                        name = 'garbage_end_activity',
                        icon = 'fas fa-check-circle',
                        label = 'Terminer l\'activité',
                        onSelect = function()
                            if not isWorking then return end
                            
                            local allDelivered = true
                            for i = 1, #currentRun.deliveryPoints do
                                if not deliveredPoints[i] then
                                    allDelivered = false
                                    break
                                end
                            end
                            
                            -- Vérifier la distance au véhicule
                            local isNearVehicle = false
                            if currentVehicle then
                                local playerCoords = GetEntityCoords(PlayerPedId())
                                local vehicleCoords = GetEntityCoords(currentVehicle)
                                local distance = #(playerCoords - vehicleCoords)
                                isNearVehicle = distance <= 50.0
                            end
                            
                            if not allDelivered then
                                -- Demander confirmation si tous les points ne sont pas livrés
                                local alert = lib.alertDialog({
                                    header = 'Attention',
                                    content = 'Vous n\'avez pas livré tous les points. Voulez-vous vraiment terminer l\'activité ? Vous ne recevrez aucune récompense.',
                                    centered = true,
                                    cancel = true
                                })
                                
                                if alert == 'confirm' then
                                    TriggerServerEvent('pc_delivery:server:endActivity', false)
                                    CleanupActivity()
                                end
                            else
                                if not isNearVehicle then
                                    -- Avertir que le véhicule est trop loin
                                    local alert = lib.alertDialog({
                                        header = 'Attention',
                                        content = 'Le véhicule est trop loin. Si vous terminez maintenant, vous ne recevrez aucune récompense. Voulez-vous quand même terminer ?',
                                        centered = true,
                                        cancel = true
                                    })
                                    
                                    if alert == 'confirm' then
                                        TriggerServerEvent('pc_delivery:server:endActivity', false)
                                        CleanupActivity()
                                    end
                                else
                                    -- Donner les récompenses si tous les points sont livrés et le véhicule est proche
                                    TriggerServerEvent('pc_delivery:server:endActivity', true)
                                    CleanupActivity()
                                end
                            end
                        end,
                        canInteract = function()
                            return isWorking
                        end
                    }
                })
            end
        end, vehicleConfig.coords, true)
    end
    
    -- Création des blips pour les points de livraison
    for i, point in ipairs(currentRun.deliveryPoints) do
        -- Création du blip
        local blip = AddBlipForCoord(point.coords.x, point.coords.y, point.coords.z)
        SetBlipSprite(blip, 1)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.7)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(point.label)
        EndTextCommandSetBlipName(blip)
        deliveryBlips[i] = blip
        
        -- Ajout du point de livraison
        local zone = exports.ox_target:addSphereZone({
            coords = point.coords,
            radius = 1.2,
            options = {
                {
                    name = 'deliver_box_' .. i,
                    icon = 'fas fa-box',
                    label = 'Livrer la boîte',
                    onSelect = function()
                        if not isWorking then 
                            return 
                        end
                        
                        if not currentVehicle then 
                            return 
                        end
                        
                        -- Vérifier si le point a déjà été livré
                        if deliveredPoints[i] then
                            lib.notify({
                                title = 'Information',
                                description = 'Ce point a déjà été livré',
                                type = 'info'
                            })
                            return
                        end
                        
                        -- Vérifier si le joueur porte une boîte
                        if not deliveryBox then
                            lib.notify({
                                title = 'Erreur',
                                description = 'Vous devez d\'abord prendre une boîte dans le camion',
                                type = 'error'
                            })
                            return
                        end
                        
                        -- Vérifier si le point est disponible
                        TriggerServerEvent('pc_delivery:server:checkPointAvailable', i)
                    end,
                    canInteract = function()
                        return deliveryBox ~= nil -- Seulement si on porte une boîte
                    end
                }
            }
        })
        deliveryZones[i] = zone
    end
    
    -- Créer le waypoint vers le premier point
    if #currentRun.deliveryPoints > 0 then
        TriggerServerEvent('pc_delivery:server:updateWaypoint', 1)
    end
    
    -- Notification de début
    lib.notify({
        title = 'Information',
        description = Lang:t('success.activity_started'),
        type = 'success'
    })
end)

-- Nouvel événement pour mettre à jour le waypoint
RegisterNetEvent('pc_delivery:client:updateWaypoint', function(pointIndex)
    if not currentRun or not currentRun.deliveryPoints[pointIndex] then return end
    
    local coords = currentRun.deliveryPoints[pointIndex].coords
    SetNewWaypoint(coords.x, coords.y)
end)

RegisterNetEvent('pc_delivery:client:pointDelivered', function(pointIndex)
    deliveredPoints[pointIndex] = true
    
    -- Supprimer le blip et la zone
    if deliveryBlips[pointIndex] then
        RemoveBlip(deliveryBlips[pointIndex])
        deliveryBlips[pointIndex] = nil
    end
    
    if deliveryZones[pointIndex] then
        exports.ox_target:removeZone(deliveryZones[pointIndex])
        deliveryZones[pointIndex] = nil
    end
    
    -- Vérifier s'il reste des points à livrer
    local remainingPoints = 0
    local nextPoint = nil
    for i = 1, #currentRun.deliveryPoints do
        if not deliveredPoints[i] then
            remainingPoints = remainingPoints + 1
            if not nextPoint then
                nextPoint = i
            end
        end
    end
    
    if nextPoint then
        -- Mettre à jour le waypoint vers le prochain point pour tous les joueurs
        TriggerServerEvent('pc_delivery:server:updateWaypoint', nextPoint)
        
        lib.notify({
            title = 'Information',
            description = 'Point livré ! ' .. remainingPoints .. ' point(s) restant(s)',
            type = 'success'
        })
    else
        -- Tous les points ont été livrés
        ClearGpsPlayerWaypoint()
        lib.notify({
            title = 'Félicitations',
            description = 'Tous les points ont été livrés ! Retournez au point de départ.',
            type = 'success'
        })
    end
end)

-- Nouvel événement pour gérer la disponibilité des points
RegisterNetEvent('pc_delivery:client:pointAvailable', function(pointIndex)
    local point = currentRun.deliveryPoints[pointIndex]
    if not point then return end
    
    -- Barre de progression pour la livraison (la boîte reste attachée pendant la progressbar)
    if lib.progressBar({
        duration = 5000,
        label = Lang:t('info.delivering'),
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
            clip = 'machinic_loop_mechandplayer'
        },
    }) then
        -- Supprimer la boîte seulement après la fin de la progressbar
        DeleteDeliveryBox()
        TriggerServerEvent('pc_delivery:server:completeDelivery', pointIndex)
    end
    -- Si la progressbar est annulée, la boîte reste attachée au joueur
end)

RegisterNetEvent('pc_delivery:client:pointNotAvailable', function(reason)
    if reason == 'already_delivered' then
        lib.notify({
            title = 'Information',
            description = 'Ce point a déjà été livré',
            type = 'info'
        })
    elseif reason == 'being_delivered' then
        lib.notify({
            title = 'Information',
            description = 'Ce point est en cours de livraison par un autre joueur',
            type = 'info'
        })
    end
end)

-- Nouvel événement pour recevoir l'information du véhicule
RegisterNetEvent('pc_delivery:client:setVehicle', function(netId)
    
    -- Attendre que le véhicule soit disponible
    local attempts = 0
    local function tryGetVehicle()
        if attempts >= 10 then
            return
        end
        
        local vehicle = NetworkGetEntityFromNetworkId(netId)
        if DoesEntityExist(vehicle) then
            currentVehicle = vehicle
            
            -- Ajouter l'ox_target pour prendre des boîtes
            AddBoxTargetToVehicle(currentVehicle)
        else
            attempts = attempts + 1
            Wait(1000)
            tryGetVehicle()
        end
    end
    
    tryGetVehicle()
end)

-- Événement pour ajouter l'ox_target au véhicule pour tous les membres du groupe
RegisterNetEvent('pc_delivery:client:vehicleReady', function(netId)
    
    -- Attendre que le véhicule soit disponible
    local attempts = 0
    local function tryGetVehicle()
        if attempts >= 10 then
            return
        end
        
        local vehicle = NetworkGetEntityFromNetworkId(netId)
        if DoesEntityExist(vehicle) then
            currentVehicle = vehicle
            
            -- Ajouter l'option pour prendre une boîte directement sur le camion
            exports.ox_target:addLocalEntity(currentVehicle, {
                {
                    name = 'take_box_from_truck',
                    icon = 'fas fa-box',
                    label = 'Prendre une boîte',
                    onSelect = function()
                        if deliveryBox then
                            lib.notify({
                                title = 'Information',
                                description = 'Vous portez déjà une boîte',
                                type = 'info'
                            })
                            return
                        end
                        
                        -- Barre de progression pour prendre la boîte
                        if lib.progressBar({
                            duration = 3000,
                            label = 'Prise de la boîte...',
                            useWhileDead = false,
                            canCancel = true,
                            disable = {
                                car = true,
                                move = true,
                                combat = true
                            },
                            anim = {
                                dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
                                clip = 'machinic_loop_mechandplayer'
                            },
                        }) then
                            -- Créer la boîte et l'attacher au joueur
                            CreateDeliveryBox(GetEntityCoords(PlayerPedId()))
                            
                            lib.notify({
                                title = 'Information',
                                description = 'Boîte prise ! Allez à un point de livraison.',
                                type = 'success'
                            })
                        end
                    end,
                    canInteract = function()
                        if deliveryBox ~= nil or not isWorking then
                            return false
                        end
                        
                        -- Vérifier si le joueur est derrière le véhicule
                        local playerPed = PlayerPedId()
                        local playerCoords = GetEntityCoords(playerPed)
                        local vehicleCoords = GetEntityCoords(currentVehicle)
                        local vehicleHeading = GetEntityHeading(currentVehicle)
                        
                        -- Calculer la position relative du joueur par rapport au véhicule
                        local relativeX = playerCoords.x - vehicleCoords.x
                        local relativeY = playerCoords.y - vehicleCoords.y
                        
                        -- Convertir en coordonnées locales du véhicule
                        local headingRad = math.rad(vehicleHeading)
                        local localX = relativeX * math.cos(-headingRad) - relativeY * math.sin(-headingRad)
                        local localY = relativeX * math.sin(-headingRad) + relativeY * math.cos(-headingRad)
                        
                        -- Vérifier si le joueur est derrière le véhicule (localY négatif) et à une distance raisonnable
                        local isBehind = localY < -2.0 and localY > -8.0 -- Entre 2 et 8 mètres derrière
                        local isClose = math.abs(localX) < 3.0 -- À moins de 3 mètres sur les côtés
                        local distance = #(playerCoords - vehicleCoords)
                        local isInRange = distance <= 1.2 -- Distance maximale de 1.2 mètres
                        
                        return isBehind and isClose and isInRange
                    end
                }
            })
        else
            attempts = attempts + 1
            Wait(1000)
            tryGetVehicle()
        end
    end
    
    tryGetVehicle()
end)

-- Nettoyage
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName ~= GetCurrentResourceName() then return end
    CleanupActivity()
end) 