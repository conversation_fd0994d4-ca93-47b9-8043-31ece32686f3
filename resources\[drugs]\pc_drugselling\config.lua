-- CONFIG À FAIRE --

Config = {}

Config.BurnerPhone = "burner_phone" -- Item name for the burner phone 

Config.Drugs = {
    {
        name = "water",
        label = "Weed",
        minAmount = 5,    -- Quantité minimum demandée
        maxAmount = 15,   -- Quantité maximum demandée
        pricePerUnit = 150, -- Prix par unité
        image = "img/weed.png", -- Ajoutez le chemin de l'image ici
        messages = {
            "Hey, t'as de la bonne beuh?",
            "Je cherche de la weed, t'en as?",
            "T'aurais pas de la bonne herbe?",
            "J'ai besoin de me détendre, t'as du stock?",
            "T'as de la bonne qualité?",
            "Je cherche de la bonne beuh, t'en as?",
            "T'as du stock de weed?",
            "J'ai besoin de me relaxer, t'as de quoi?",
            "T'as de la bonne qualité?",
            "Je cherche de la beuh, t'en as?"
        }
    },
    {
        name = "coke",
        label = "Coke",
        minAmount = 5,    -- Quantité minimum demandée
        maxAmount = 15,   -- Quantité maximum demandée
        pricePerUnit = 150, -- Prix par unité
        image = "img/coke.png", -- Ajoutez le chemin de l'image ici
        messages = {
            "Hey, t'as de la bonne came?",
            "Je cherche de la coke, t'en as?",
            "T'aurais pas de la neige?",
            "J'ai besoin d'un petit coup de boost, t'as du stock?",
            "T'as de la bonne qualité?",
            "Je cherche de la poudre, t'en as?",
            "T'as du stock de coke?",
            "J'ai besoin d'énergie, t'as de quoi?",
            "T'as de la bonne qualité?",
            "Je cherche de la came, t'en as?"
        }
    },
    {
        name = "meth",
        label = "Meth",
        minAmount = 5,    -- Quantité minimum demandée
        maxAmount = 15,   -- Quantité maximum demandée
        pricePerUnit = 150, -- Prix par unité
        image = "img/meth.png", -- Ajoutez le chemin de l'image ici
        messages = {
            "Hey, t'as de la glace?",
            "Je cherche de la meth, t'en as?",
            "T'aurais pas de la glace?",
            "J'ai besoin de rester éveillé, t'as du stock?",
            "T'as de la bonne qualité?",
            "Je cherche de la glace, t'en as?",
            "T'as du stock de meth?",
            "J'ai besoin d'énergie, t'as de quoi?",
            "T'as de la bonne qualité?",
            "Je cherche de la glace, t'en as?"
        }
    },
    {
        name = "heroine",
        label = "Heroine",
        minAmount = 5,    -- Quantité minimum demandée
        maxAmount = 15,   -- Quantité maximum demandée
        pricePerUnit = 150, -- Prix par unité
        image = "img/heroine.png", -- Ajoutez le chemin de l'image ici
        messages = {
            "Hey, t'as de la bonne came?",
            "Je cherche de l'héroïne, t'en as?",
            "T'aurais pas de la came?",
            "J'ai besoin de ma dose, t'as du stock?",
            "T'as de la bonne qualité?",
            "Je cherche de la came, t'en as?",
            "T'as du stock d'héroïne?",
            "J'ai besoin de ma dose, t'as de quoi?",
            "T'as de la bonne qualité?",
            "Je cherche de la came, t'en as?"
        }
    },
    {
        name = "mush",
        label = "Mush",
        minAmount = 5,    -- Quantité minimum demandée
        maxAmount = 15,   -- Quantité maximum demandée
        pricePerUnit = 150, -- Prix par unité
        image = "img/mush.png", -- Ajoutez le chemin de l'image ici
        messages = {
            "Hey, t'as des champis?",
            "Je cherche des champignons, t'en as?",
            "T'aurais pas des shrooms?",
            "J'ai besoin de voyager, t'as du stock?",
            "T'as de la bonne qualité?",
            "Je cherche des champis, t'en as?",
            "T'as du stock de shrooms?",
            "J'ai besoin de planer, t'as de quoi?",
            "T'as de la bonne qualité?",
            "Je cherche des champis, t'en as?"
        }
    },
}

Config.MessageInterval = {
    min = 5000, -- 5 secondes minimum entre les messages
    max = 10000 -- 10 secondes maximum entre les messages
}

Config.DeliveryPed = {
    model = "a_m_y_stwhi_01", -- Modèle du ped
    scenario = "WORLD_HUMAN_SMOKING" -- Animation du ped
}

Config.DeliveryLocations = {
    {
        coords = vec4(291.53, -1078.69, 29.4, 270.78),
        coordsexit = vec4(295.97, -1067.94, 29.35, 347.99),
        label = "test"
    },
    {
        coords = vec4(293.33, -1081.73, 29.4, 354.49),
        coordsexit = vec4(295.97, -1067.94, 29.35, 347.99),
        label = "test"
    },
}

Config.PoliceAlert = {
    chance = 100, -- 35% de chance que la police soit alertée lors de l'acceptation
    transactionChance = 25, -- 25% de chance que la police soit alertée lors de la transaction
    minCops = 1 -- Nombre minimum de policiers requis (mettez le nombre souhaité, par exemple 2)
}

Config.ThankYouMessages = {
    "Merci pour le stock !",
    "Apprécié, merci !",
    "Merci, j'attends la prochaine livraison !",
    "Super, merci beaucoup !",
    "Merci pour la marchandise !"
} 

Config.DealerResponses = {
    "Je ne fais rien de mal, je traîne juste ici.",
    "Pourquoi vous vous intéressez à moi ? Je ne suis qu'un citoyen.",
    "Je n'ai rien à cacher, j'attend des amis.'.",
    "Je suis ici pour acheter des cigarettes, c'est tout.",
    "Vous savez comment c'est, il y a beaucoup de gens qui traînent ici.",
    "Je ne sais pas de quoi vous parlez, je ne suis pas impliqué dans quoi que ce soit.",
    "Je suis juste un livreur, je fais mon travail.",
    "Vous avez des preuves contre moi ? Je ne pense pas.",
    "Je ne suis pas le seul ici, pourquoi ne pas interroger les autres ?",
    "Je suis juste là pour profiter de la vue, rien de plus.",
    "J'ai juste besoin d'un peu d'air, c'est tout...",
    "Je... je me sens pas très bien, je cherche juste un endroit pour me reposer.",
    "Je suis clean, je vous jure ! Je suis en cure depuis 3 mois !",
    "C'est juste de l'herbe, c'est pas comme si c'était de l'héroïne ou quoi...",
    "Je suis en train de me sevrer, j'ai besoin de temps...",
    "Je suis juste un petit consommateur, je ne vends rien !",
    "Je suis en train de chercher un centre de désintoxication...",
    "J'ai des problèmes personnels, laissez-moi tranquille...",
    "Je suis sous traitement, j'ai une ordonnance...",
    "Je suis juste stressé, j'ai besoin de me détendre...",
    "Je ne suis qu'un petit poisson, pourquoi ne pas aller chercher les gros ?",
    "Je suis en train de changer ma vie, donnez-moi une chance...",
    "Je suis juste là pour discuter avec des amis...",
    "Je ne fais de mal à personne, laissez-moi vivre ma vie...",
    "Je suis en train de chercher du travail, je veux m'en sortir..."
}

Config.DrugTheftChance = 10 -- Pourcentage de chance que le ped vole la drogue

Config.PedActions = {
    knifeChance = 30, -- Pourcentage de chance que le ped sorte un couteau
    runAwayChance = 70 -- Pourcentage de chance que le ped s'enfuie
}
Config.CopRefreshTime = 10 -- Temps en minute pour refresh s'il y a des policiers en service
Config.DeliveryTimeout = 1800 -- Temps maximum en secondes avant l'annulation de la livraison (30 minutes)