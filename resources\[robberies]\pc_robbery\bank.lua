local QBCore = exports['qb-core']:GetCoreObject()
local hasThreatenedGuard = false

-- Table pour stocker les peds de banque
local bankPeds = {}

-- Variables pour suivre l'état des actions
local hasInsertedCard = false
local hasUsedThermite = false
local drilledVaults = {}
local isInBankZone = false
local currentBankZone = nil

-- Fonction pour détecter l'entrée en zone de banque
local function detectBankZone()
    CreateThread(function()
        while true do
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local wasInZone = isInBankZone
            local newBankZone = nil

            -- Vérifier si le joueur est dans une zone de banque
            for bankId, bankConfig in pairs(Config.Banks) do
                local distance = #(playerCoords - vector3(bankConfig.pedCoords.x, bankConfig.pedCoords.y, bankConfig.pedCoords.z))
                if distance < 50.0 then -- Zone de 50 mètres autour de la banque
                    newBankZone = bankId
                    break
                end
            end

            -- Si le joueur entre dans une nouvelle zone de banque
            if newBankZone and newBankZone ~= currentBankZone then
                isInBankZone = true
                currentBankZone = newBankZone
                -- Demander la synchronisation des états
                TriggerServerEvent('asc_robbery:requestSync', newBankZone)
            elseif not newBankZone and wasInZone then
                -- Le joueur sort de toutes les zones de banque
                isInBankZone = false
                currentBankZone = nil
            end

            Wait(1000) -- Vérifier toutes les secondes
        end
    end)
end

-- Fonction pour nettoyer les interactions ox_target existantes
local function cleanupBankInteractions()
    for bankId, bankConfig in pairs(Config.Banks) do
        -- Supprimer les interactions thermite
        exports.ox_target:removeZone('thermite_hack_' .. bankId)

        -- Supprimer les interactions de fermeture de porte
        exports.ox_target:removeZone('close_bank_door_' .. bankId)

        -- Supprimer les interactions de système de sécurité
        exports.ox_target:removeZone('security_system_' .. bankId)

        -- Supprimer les interactions des coffres
        if bankConfig.vaultCoords then
            for i = 1, #bankConfig.vaultCoords do
                exports.ox_target:removeZone('drill_vault_' .. bankId .. '_' .. i)
            end
        end
    end
end

-- Fonction pour créer les peds de banque
local function createBankPeds()
    -- Nettoyer les interactions existantes
    cleanupBankInteractions()

    -- Supprimer les peds existants
    for _, ped in pairs(bankPeds) do
        if DoesEntityExist(ped) then
            DeleteEntity(ped)
        end
    end
    bankPeds = {}

    -- Charger le modèle du ped
    local pedModel = GetHashKey('s_m_m_security_01')
    RequestModel(pedModel)
    while not HasModelLoaded(pedModel) do
        Wait(1)
    end

    -- Créer un ped pour chaque banque
    for bankId, bankConfig in pairs(Config.Banks) do
        -- Créer le ped
        local ped = CreatePed(4, pedModel, bankConfig.pedCoords.x, bankConfig.pedCoords.y, bankConfig.pedCoords.z - 1.0, bankConfig.pedCoords.w, false, true)
        
        if not DoesEntityExist(ped) then
            goto continue
        end

        SetEntityHeading(ped, bankConfig.pedCoords.w)
        FreezeEntityPosition(ped, true)
        SetEntityInvincible(ped, true)
        SetBlockingOfNonTemporaryEvents(ped, true)
        SetPedCanRagdoll(ped, false)
        SetPedCanBeTargetted(ped, false)
        SetPedCanBeDraggedOut(ped, false)
        SetPedCanRagdollFromPlayerImpact(ped, false)
        SetEntityAsMissionEntity(ped, true, true)

        -- Stocker le ped
        bankPeds[bankId] = ped

        -- Ajouter l'interaction avec le ped
        exports.ox_target:addLocalEntity(ped, {
            {
                name = 'rob_bank_guard_' .. bankId,
                icon = 'fas fa-user-secret',
                label = 'Menacer le garde',
                onSelect = function()
                    -- Vérifier le cooldown avant de permettre l'interaction
                    TriggerServerEvent('asc_robbery:checkBankCooldown', bankId)
                end
            }
        })

        -- Ajouter l'interaction pour le thermitehack
        exports.ox_target:addBoxZone({
            coords = vec3(bankConfig.thermiteHack.x, bankConfig.thermiteHack.y, bankConfig.thermiteHack.z),
            size = vec3(1, 1, 2),
            rotation = 0,
            debug = false,
            options = {
                {
                    name = 'thermite_hack_' .. bankId,
                    icon = 'fas fa-fire',
                    label = 'Utiliser la thermite',
                    canInteract = function()
                        return hasThreatenedGuard
                    end,
                    onSelect = function()
                        TriggerEvent('asc_robbery:thermitehack', bankId)
                    end
                }
            }
        })

        -- Ajouter l'interaction pour fermer la porte
        exports.ox_target:addBoxZone({
            coords = vec3(bankConfig.policeReset.x, bankConfig.policeReset.y, bankConfig.policeReset.z),
            size = vec3(1, 1, 2),
            rotation = 0,
            debug = false,
            options = {
                {
                    name = 'close_bank_door_' .. bankId,
                    icon = 'fas fa-door-closed',
                    label = 'Fermer la porte',
                    canInteract = function()
                        -- Vérifier si la porte est ouverte en comparant son heading actuel
                        local doorObject = GetClosestObjectOfType(bankConfig.vaultDoorCoords.x, bankConfig.vaultDoorCoords.y, bankConfig.vaultDoorCoords.z, 5.0, GetHashKey(bankConfig.object), false, false, false)
                        if doorObject ~= 0 then
                            local currentHeading = GetEntityHeading(doorObject)
                            local openHeading = bankConfig.heading.open
                            local closedHeading = bankConfig.heading.closed

                            -- Calculer la différence entre le heading actuel et les headings configurés
                            local diffOpen = math.abs(currentHeading - openHeading)
                            local diffClosed = math.abs(currentHeading - closedHeading)

                            -- Gérer le cas où les angles passent par 0/360
                            if diffOpen > 180 then diffOpen = 360 - diffOpen end
                            if diffClosed > 180 then diffClosed = 360 - diffClosed end

                            -- La porte est considérée comme ouverte si elle est plus proche du heading "open"
                            local isOpen = diffOpen < diffClosed

                            return isOpen
                        end
                        return false -- Si pas de porte trouvée, ne pas afficher l'option
                    end,
                    onSelect = function()
                        -- Demander au serveur de fermer la porte pour tous les joueurs
                        TriggerServerEvent('asc_robbery:closeBankDoorForAll', bankId)
                    end
                }
            }
        })

        -- Ajouter l'interaction avec le système de sécurité
        exports.ox_target:addBoxZone({
            coords = bankConfig.securitySystemCoords,
            size = vec3(1, 1, 2),
            rotation = 0,
            debug = false,
            options = {
                {
                    name = 'security_system_' .. bankId,
                    icon = 'fas fa-shield-alt',
                    label = 'Insérer la carte',
                    canInteract = function()
                        return hasThreatenedGuard
                    end,
                    onSelect = function()
                        TriggerEvent('asc_robbery:insertCard', bankId)
                    end
                }
            }
        })

        -- Ajouter les interactions pour les coffres
        if bankConfig.vaultCoords then
            for i = 1, #bankConfig.vaultCoords do
                local vaultCoords = bankConfig.vaultCoords[i]
                exports.ox_target:addBoxZone({
                    coords = vec3(vaultCoords.x, vaultCoords.y, vaultCoords.z),
                    size = vec3(1, 1, 2),
                    rotation = 0,
                    debug = false,
                    options = {
                        {
                            name = 'drill_vault_' .. bankId .. '_' .. i,
                            icon = 'fas fa-drill',
                            label = 'Percer le coffre',
                            canInteract = function()
                                -- Vérifier si le garde a été menacé et si le coffre n'a pas déjà été percé
                                return hasThreatenedGuard and not drilledVaults[i]
                            end,
                            onSelect = function()
                                -- Vérifier côté serveur si le coffre peut être percé
                                TriggerServerEvent('asc_robbery:checkVault', bankId, i)
                            end
                        }
                    }
                })
            end
        else
        end

        ::continue::
    end
end

-- Initialisation
CreateThread(function()
    Wait(2000) -- Attendre que tout soit chargé
    createBankPeds()
    detectBankZone() -- Démarrer la détection de zone

    -- Initialiser Paleto Bank si la fonction existe
    if setupPaletoBankInteractions then
        setupPaletoBankInteractions()
    end
end)

-- Événement pour ouvrir la porte avec le heading configuré
RegisterNetEvent('asc_robbery:openBankDoor', function(doorId, bankId)
    local bankConfig = Config.Banks[bankId]
    if not bankConfig then return end

    local doorCoords = bankConfig.vaultDoorCoords
    local doorHeading = bankConfig.heading.open
    local doorObject = GetClosestObjectOfType(doorCoords.x, doorCoords.y, doorCoords.z, 2.0, GetHashKey(bankConfig.object), false, false, false)
    if doorObject ~= 0 then
        SetEntityHeading(doorObject, doorHeading)
    else
    end
end)

-- Événement pour fermer la porte avec le heading configuré
RegisterNetEvent('asc_robbery:closeBankDoor', function(doorId, bankId)
    local bankConfig = Config.Banks[bankId]
    if not bankConfig then return end

    local doorCoords = bankConfig.vaultDoorCoords
    local doorHeading = bankConfig.heading.closed
    local doorObject = GetClosestObjectOfType(doorCoords.x, doorCoords.y, doorCoords.z, 2.0, GetHashKey(bankConfig.object), false, false, false)
    if doorObject ~= 0 then
        SetEntityHeading(doorObject, doorHeading)
    else
    end
end)

-- Événement pour menacer le garde
RegisterNetEvent('asc_robbery:robBankGuard', function(bankId)
    -- Vérifier si le joueur a une arme
    local ped = PlayerPedId()
    local currentWeapon = GetSelectedPedWeapon(ped)
    
    if currentWeapon == GetHashKey('WEAPON_UNARMED') then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Vous devez avoir une arme en main',
            type = 'error'
        })
        return
    end

    -- Animation du garde
    RequestAnimDict('random@mugging3')
    while not HasAnimDictLoaded('random@mugging3') do
        Wait(1)
    end

    -- Jouer l'animation
    TaskPlayAnim(bankPeds[bankId], 'random@mugging3', 'handsup_standing_base', 8.0, -8.0, -1, 49, 0, false, false, false)

    -- Démarrer le cooldown
    TriggerServerEvent('asc_robbery:startBankCooldown', bankId)

    -- Marquer le garde comme menacé
    hasThreatenedGuard = true

    -- Déclencher l'alerte de police
    exports['ps-dispatch']:FleecaBankRobbery()

    -- Notification
    exports['ox_lib']:notify({
        title = 'Succès',
        description = 'Le garde est maintenant menacé',
        type = 'success'
    })
end)

-- Événement pour insérer la carte
RegisterNetEvent('asc_robbery:insertCard', function(bankId)
    if not bankId then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Erreur de configuration de la banque',
            type = 'error'
        })
        return
    end

    if hasInsertedCard then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Cette action a déjà été effectuée',
            type = 'error'
        })
        return
    end

    -- Positionner le joueur pour l'insertion de carte
    local ped = PlayerPedId()

    -- Utiliser les coordonnées spécifiques pour l'insertion de carte si configurées
    local cardPlayerPos = Config.Banks[bankId].cardPlayerPos
    if cardPlayerPos then
        SetEntityCoords(ped, cardPlayerPos.x, cardPlayerPos.y, cardPlayerPos.z, false, false, false, true)
        SetEntityHeading(ped, cardPlayerPos.w)
        Wait(100) -- Attendre que le positionnement soit effectif
    end

    -- Progress bar pour insérer la carte avec animation de passage de carte
    if lib.progressBar({
        duration = 3000,
        label = 'Insertion de la carte...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = 'anim_heist@hs3f@ig3_cardswipe@male@',
            clip = 'fail_var01',
            flag = 1,
        }
    }) then
        
        -- Lancer le minigame
        local success = exports['glitch-minigames']:StartBruteForce(5)
        Wait(1000) -- Attendre que le minigame se termine
        
        if success then
            hasInsertedCard = true
            -- Obtenir l'ID de la porte la plus proche
            local door = exports.ox_doorlock:getClosestDoor()
            if door then
                -- Débarrer la porte via le serveur
                TriggerServerEvent('asc_robbery:unlockBankDoor', door.id, bankId)
            else
                exports['ox_lib']:notify({
                    title = 'Erreur',
                    description = 'Aucune porte trouvée à proximité',
                    type = 'error'
                })
            end
        else
            exports['ox_lib']:notify({
                title = 'Échec',
                description = 'Vous n\'avez pas réussi à pirater le système',
                type = 'error'
            })
        end
    end
end)

-- Événement pour le thermitehack
RegisterNetEvent('asc_robbery:thermitehack', function(bankId)
    if not bankId then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Erreur de configuration de la banque',
            type = 'error'
        })
        return
    end

    if hasUsedThermite then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Cette action a déjà été effectuée',
            type = 'error'
        })
        return
    end

    
    -- Vérifier si le joueur a la thermite
    local hasItem = exports.ox_inventory:Search('count', Config.General.bank.requiredItemVaultDoor) > 0
    if not hasItem then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Vous avez besoin de thermite',
            type = 'error'
        })
        return
    end
    
    -- Lancer directement le minigame de thermite
        local success = exports['glitch-minigames']:StartDataCrack(3)
        Wait(1000) -- Attendre que le minigame se termine
        
        if success then
            hasUsedThermite = true
            -- Retirer la thermite uniquement si le minigame est réussi
            TriggerServerEvent('asc_robbery:removeThermite')

            -- Animation de placement de thermite avec scène synchronisée
            local ped = PlayerPedId()

            -- Utiliser les coordonnées de thermiteHack depuis la config
            local thermiteCoords = Config.Banks[bankId].thermiteHack

            -- Positionner le joueur aux coordonnées spécifiques si configurées
            local playerPos = Config.Banks[bankId].thermitePlayerPos
            if playerPos then
                SetEntityCoords(ped, playerPos.x, playerPos.y, playerPos.z, false, false, false, true)
                SetEntityHeading(ped, playerPos.w)
                Wait(100) -- Attendre que le positionnement soit effectif
            end

            local pedRotation = GetEntityRotation(ped)

            -- Charger le dictionnaire d'animation
            RequestAnimDict('anim@heists@ornate_bank@thermal_charge')
            while not HasAnimDictLoaded('anim@heists@ornate_bank@thermal_charge') do
                Wait(1)
            end

            -- Créer la scène synchronisée aux coordonnées de thermiteHack
            local thermiteScene = NetworkCreateSynchronisedScene(thermiteCoords.x, thermiteCoords.y, thermiteCoords.z, pedRotation.x, pedRotation.y, pedRotation.z, 2, false, false, **********, 0, 1.3)

            -- Créer l'objet sac de thermite aux coordonnées de thermiteHack
            local thermiteBag = CreateObject(`hei_p_m_bag_var22_arm_s`, thermiteCoords.x, thermiteCoords.y, thermiteCoords.z, true, true, false)
            SetEntityCollision(thermiteBag, false, true)

            -- Ajouter le joueur à la scène
            NetworkAddPedToSynchronisedScene(ped, thermiteScene, 'anim@heists@ornate_bank@thermal_charge', 'thermal_charge', 1.5, -4.0, 1, 16, **********, 0)

            -- Ajouter le sac à la scène
            NetworkAddEntityToSynchronisedScene(thermiteBag, thermiteScene, 'anim@heists@ornate_bank@thermal_charge', 'bag_thermal_charge', 4.0, -8.0, 1)

            -- Démarrer la scène
            NetworkStartSynchronisedScene(thermiteScene)

            -- Attendre un peu puis créer l'objet thermite aux coordonnées de thermiteHack
            Wait(1500)
            local thermalCharge = CreateObject(`hei_prop_heist_thermite`, thermiteCoords.x, thermiteCoords.y, thermiteCoords.z + 0.2, true, true, true)
            SetEntityCollision(thermalCharge, false, true)

            -- Attacher la thermite à la main du joueur
            AttachEntityToEntity(thermalCharge, ped, GetPedBoneIndex(ped, 28422), 0, 0, 0, 0, 0, 200.0, true, true, false, true, 1, true)

            -- Attendre la fin de l'animation puis détacher et placer la thermite
            Wait(2500) -- Attendre la fin de l'animation de placement

            -- Détacher la thermite de la main et la figer en place
            DetachEntity(thermalCharge, true, true)
            FreezeEntityPosition(thermalCharge, true)

            -- Nettoyer la scène et le sac
            Wait(100)
            if DoesEntityExist(thermiteBag) then
                DeleteObject(thermiteBag)
            end
            ClearPedTasks(ped)

            -- Effets visuels de thermite qui brûle
            if DoesEntityExist(thermalCharge) then
                -- Charger les effets de particules pour la thermite
                RequestNamedPtfxAsset('scr_ornate_heist')
                while not HasNamedPtfxAssetLoaded('scr_ornate_heist') do
                    Wait(1)
                end

                -- Charger les effets de particules pour la fumée
                RequestNamedPtfxAsset('core')
                while not HasNamedPtfxAssetLoaded('core') do
                    Wait(1)
                end

                -- Obtenir les coordonnées de la thermite
                local termCoords = GetEntityCoords(thermalCharge)

                -- Démarrer l'effet de combustion (feu)
                local burnEffect = StartParticleFxLoopedAtCoord('scr_heist_ornate_thermal_burn', termCoords.x, termCoords.y + 1.0, termCoords.z, 0, 0, 0, 1.0, false, false, false, false)

                -- Démarrer l'effet de fumée
                local smokeEffect = StartParticleFxLoopedAtCoord('exp_grd_smoke_lrg', termCoords.x, termCoords.y, termCoords.z + 0.5, 0, 0, 0, 0.8, false, false, false, false)

                -- Attendre 3 secondes puis arrêter les effets
                Wait(3000)

                -- Arrêter les effets
                StopParticleFxLooped(burnEffect, false)
                StopParticleFxLooped(smokeEffect, false)

                -- Supprimer l'objet thermite après l'effet
                DeleteObject(thermalCharge)
            end
            
            -- Obtenir l'ID de la porte la plus proche
            local door = exports.ox_doorlock:getClosestDoor()
            if door then
                -- Débarrer la porte du coffre via le serveur (événement spécifique thermite)
                TriggerServerEvent('asc_robbery:unlockThermiteDoor', door.id, bankId)
            else
                exports['ox_lib']:notify({
                    title = 'Erreur',
                    description = 'Aucune porte trouvée à proximité',
                    type = 'error'
                })
            end
        else
            exports['ox_lib']:notify({
                title = 'Échec',
                description = 'Vous n\'avez pas réussi à faire fondre la serrure',
                type = 'error'
            })
        end
end)

-- Événement pour gérer le résultat de la vérification du coffre
RegisterNetEvent('asc_robbery:vaultCheckResult')
AddEventHandler('asc_robbery:vaultCheckResult', function(canDrill, bankId, vaultIndex)
    if canDrill then
        -- Le coffre peut être percé, lancer le processus de perçage
        TriggerEvent('asc_robbery:drillVault', bankId, vaultIndex)
    end
    -- Si canDrill est false, le serveur a déjà envoyé une notification d'erreur
end)

-- Événement pour percer le coffre
RegisterNetEvent('asc_robbery:drillVault', function(bankId, vaultIndex)
    if drilledVaults[vaultIndex] then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Ce coffre a déjà été percé',
            type = 'error'
        })
        return
    end
    
    -- Vérifier si la banque existe
    if not Config.Banks[bankId] then
        return
    end
    
    -- Vérifier si le coffre existe
    local vault = Config.Banks[bankId].vaultCoords[vaultIndex]
    if not vault then
        return
    end

    -- Vérifier si le joueur a la perceuse
    local hasItem = exports.ox_inventory:Search('count', Config.General.bank.requiredItemVault) > 0
    if not hasItem then
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Vous avez besoin d\'une perceuse',
            type = 'error'
        })
        return
    end

    -- Progress bar pour installer la perceuse
    if lib.progressBar({
        duration = 2000,
        label = 'Installation de la perceuse...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        },
    }) then
        
        -- Lancer le minigame de perçage
        local success = exports['glitch-minigames']:StartPlasmaDrilling(8)
        
        if success then
            drilledVaults[vaultIndex] = true
            exports['ox_lib']:notify({
                title = 'Succès',
                description = 'Vous avez réussi à percer le coffre',
                type = 'success'
            })
            -- Déverrouiller le coffre
            TriggerServerEvent('asc_robbery:giveVaultReward', bankId, vaultIndex)
        else
            exports['ox_lib']:notify({
                title = 'Échec',
                description = 'Vous n\'avez pas réussi à percer le coffre',
                type = 'error'
            })
            -- Libérer le verrou du coffre en cas d'échec
            TriggerServerEvent('asc_robbery:unlockVault', bankId, vaultIndex)
        end
    else
        -- Progress bar annulée, libérer le verrou
        TriggerServerEvent('asc_robbery:unlockVault', bankId, vaultIndex)
    end
end)

-- Événement pour recréer le ped lors de la connexion d'un joueur
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    Wait(2000) -- Attendre que tout soit chargé
    createBankPeds()
end)

-- Événement pour recréer le ped lors du spawn d'un joueur
RegisterNetEvent('QBCore:Client:OnPlayerSpawn', function()
    Wait(1000) -- Attendre que tout soit chargé
    createBankPeds()
end)

-- Événement pour gérer le résultat de la vérification du cooldown
RegisterNetEvent('asc_robbery:bankCooldownResult')
AddEventHandler('asc_robbery:bankCooldownResult', function(canRob, bankId)
    if canRob then
        -- Vérifier si le joueur a une arme
        local ped = PlayerPedId()
        local currentWeapon = GetSelectedPedWeapon(ped)
        
        if currentWeapon == GetHashKey('WEAPON_UNARMED') then
            exports['ox_lib']:notify({
                title = 'Erreur',
                description = 'Vous devez avoir une arme en main',
                type = 'error'
            })
            return
        end

        -- Animation du garde
        RequestAnimDict('random@mugging3')
        while not HasAnimDictLoaded('random@mugging3') do
            Wait(1)
        end

        -- Jouer l'animation
        TaskPlayAnim(bankPeds[bankId], 'random@mugging3', 'handsup_standing_base', 8.0, -8.0, -1, 49, 0, false, false, false)

        -- Animation du joueur
        TaskAimGunAtEntity(ped, bankPeds[bankId], -1, true)

        -- Progress bar
        if lib.progressBar({
            duration = 5000,
            label = 'Menace en cours...',
            useWhileDead = false,
            canCancel = true,
            disable = {
                car = true,
                move = true,
                combat = true
            }
        }) then
            -- Démarrer les cooldowns
            TriggerServerEvent('asc_robbery:startBankCooldown', bankId)

            -- Récompense
            TriggerServerEvent('asc_robbery:givePedItem')

            -- Marquer le garde comme menacé
            hasThreatenedGuard = true

            -- Déclencher l'alerte de police
            exports['ps-dispatch']:FleecaBankRobbery()

            -- Arrêter les animations après un délai
            ClearPedTasks(bankPeds[bankId])
            ClearPedTasks(ped)
        else
            -- Arrêter les animations si annulé
            ClearPedTasks(bankPeds[bankId])
            ClearPedTasks(ped)
        end
    end
end)

-- Fonction pour réinitialiser tous les états du client
local function resetAllClientStates()

    -- Réinitialiser tous les flags d'état
    hasThreatenedGuard = false
    hasInsertedCard = false
    hasUsedThermite = false
    drilledVaults = {}

end

-- Événement pour réinitialiser les états du client
RegisterNetEvent('asc_robbery:resetClientStates')
AddEventHandler('asc_robbery:resetClientStates', function()
    resetAllClientStates()
end)

-- Événement pour réinitialiser manuellement (pour les admins)
RegisterNetEvent('asc_robbery:manualReset')
AddEventHandler('asc_robbery:manualReset', function()
    resetAllClientStates()
    TriggerServerEvent('asc_robbery:resetAllStates')
end)

-- Nettoyage lors de l'arrêt de la ressource
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Nettoyer les interactions ox_target
        cleanupBankInteractions()

        -- Nettoyer les interactions de Paleto Bank
        if cleanupPaletoBankInteractions then
            cleanupPaletoBankInteractions()
        end

        -- Supprimer les peds
        for _, ped in pairs(bankPeds) do
            if DoesEntityExist(ped) then
                DeleteEntity(ped)
            end
        end

        -- Réinitialiser les états lors de l'arrêt
        resetAllClientStates()
    end
end)

-- Commande pour réinitialiser côté client (pour les admins)
RegisterCommand('resetrobberyclient', function()
    resetAllClientStates()
    TriggerServerEvent('asc_robbery:resetAllStates')
    exports['ox_lib']:notify({
        title = 'Succès',
        description = 'États de braquage réinitialisés côté client',
        type = 'success'
    })
end, false)

-- Réinitialiser les états lors du démarrage de la ressource
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(1000) -- Attendre que tout soit chargé
        resetAllClientStates()
    end
end)

-- Événement pour recevoir la synchronisation des états
RegisterNetEvent('asc_robbery:receiveSync')
AddEventHandler('asc_robbery:receiveSync', function(bankId, syncData)

    -- Mettre à jour les états locaux
    hasThreatenedGuard = syncData.hasThreatenedGuard
    hasInsertedCard = syncData.hasInsertedCard
    hasUsedThermite = syncData.hasUsedThermite
    drilledVaults = syncData.drilledVaults or {}

    -- Synchroniser l'état de la porte
    if syncData.doorState then
        -- La porte est ouverte, s'assurer qu'elle l'est visuellement
        local bankConfig = Config.Banks[bankId]
        if bankConfig then
            local doorCoords = bankConfig.vaultDoorCoords
            local doorHeading = bankConfig.heading.open
            local doorObject = GetClosestObjectOfType(doorCoords.x, doorCoords.y, doorCoords.z, 2.0, GetHashKey(bankConfig.object), false, false, false)
            if doorObject ~= 0 then
                SetEntityHeading(doorObject, doorHeading)
            end
        end
    end

end)

-- Événements pour synchroniser les états individuels
RegisterNetEvent('asc_robbery:syncThreatenedGuard')
AddEventHandler('asc_robbery:syncThreatenedGuard', function(bankId, state)
    if currentBankZone == bankId then
        hasThreatenedGuard = state
    end
end)

RegisterNetEvent('asc_robbery:syncInsertedCard')
AddEventHandler('asc_robbery:syncInsertedCard', function(bankId, state)
    if currentBankZone == bankId then
        hasInsertedCard = state
    end
end)

RegisterNetEvent('asc_robbery:syncUsedThermite')
AddEventHandler('asc_robbery:syncUsedThermite', function(bankId, state)
    if currentBankZone == bankId then
        hasUsedThermite = state
    end
end)

RegisterNetEvent('asc_robbery:syncDrilledVault')
AddEventHandler('asc_robbery:syncDrilledVault', function(bankId, vaultIndex, state)
    if currentBankZone == bankId then
        drilledVaults[vaultIndex] = state
    end
end)

RegisterNetEvent('asc_robbery:syncDoorState')
AddEventHandler('asc_robbery:syncDoorState', function(bankId, isOpen)
    -- Synchroniser l'état de la porte pour tous les joueurs
    local bankConfig = Config.Banks[bankId]
    if bankConfig then
        local doorCoords = bankConfig.vaultDoorCoords
        local doorHeading = isOpen and bankConfig.heading.open or bankConfig.heading.closed
        local doorObject = GetClosestObjectOfType(doorCoords.x, doorCoords.y, doorCoords.z, 2.0, GetHashKey(bankConfig.object), false, false, false)
        if doorObject ~= 0 then
            SetEntityHeading(doorObject, doorHeading)
        end
    end
end)

-- Spawn de l'employé Pacific Bank au chargement du script
CreateThread(function()
    local pedModel = 's_m_m_bankclerk_01'
    local coords = Config.Pacific.pedCoords
    RequestModel(pedModel)
    while not HasModelLoaded(pedModel) do Wait(10) end
    local ped = CreatePed(0, pedModel, coords.x, coords.y, coords.z - 1.0, coords.w, false, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    SetEntityAsMissionEntity(ped, true, true)
    FreezeEntityPosition(ped, true)
end)

