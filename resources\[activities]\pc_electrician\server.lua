local QBCore = exports['qb-core']:GetCoreObject()

-- Variables locales
local activeGroups = {}
local activePlayers = {}
local repairedPoints = {} -- Table pour suivre les points réparés par groupe
local repairingPoints = {} -- Nouvelle table pour suivre les points en cours de réparation

-- Fonctions utilitaires
local function IsPlayerInGroup(source)
    for _, group in pairs(activeGroups) do
        for _, playerId in ipairs(group.members) do
            if playerId == source then
                return true
            end
        end
    end
    return false
end

local function GetPlayerGroup(source)
    for groupId, group in pairs(activeGroups) do
        for _, playerId in ipairs(group.members) do
            if playerId == source then
                return groupId, group
            end
        end
    end
    return nil, nil
end

local function NotifyGroup(group, message, type)
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('QBCore:Notify', memberId, message, type)
    end
end

local function GetPlayerFullName(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if Player then
        return Player.PlayerData.charinfo.firstname .. " " .. Player.PlayerData.charinfo.lastname
    end
    return "Inconnu"
end

-- Événements
RegisterNetEvent('pc_electrician:server:createGroup', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player then return end
    
    if IsPlayerInGroup(src) then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.already_in_group'), 'error')
        return
    end
    
    local groupId = #activeGroups + 1
    activeGroups[groupId] = {
        leader = src,
        members = {src},
        active = false
    }
    
    -- Envoyer les noms des joueurs au client
    local playerNames = {}
    for _, memberId in ipairs(activeGroups[groupId].members) do
        playerNames[memberId] = GetPlayerFullName(memberId)
    end
    TriggerClientEvent('pc_electrician:client:updatePlayerNames', src, playerNames)
    
    TriggerClientEvent('QBCore:Notify', src, Lang:t('success.group_created'), 'success')
    TriggerClientEvent('pc_electrician:client:groupCreated', src, groupId)
end)

RegisterNetEvent('pc_electrician:server:leaveGroup', function()
    local src = source
    local groupId, group = GetPlayerGroup(src)
    
    if not group then return end
    
    -- Si c'est le leader qui part, le groupe est dissous
    if group.leader == src then
        for _, memberId in ipairs(group.members) do
            if memberId ~= src then
                TriggerClientEvent('QBCore:Notify', memberId, Lang:t('info.group_disbanded'), 'error')
                TriggerClientEvent('pc_electrician:client:groupUpdated', memberId, nil)
            end
        end
        activeGroups[groupId] = nil
    else
        -- Sinon, on retire juste le joueur
        for i, memberId in ipairs(group.members) do
            if memberId == src then
                table.remove(group.members, i)
                break
            end
        end
        NotifyGroup(group, Lang:t('info.player_left_group'), 'primary')
    end
    
    TriggerClientEvent('QBCore:Notify', src, Lang:t('success.left_group'), 'success')
    TriggerClientEvent('pc_electrician:client:groupUpdated', src, nil)
end)

RegisterNetEvent('pc_electrician:server:invitePlayer', function(targetId)
    local src = source
    
    local groupId, group = GetPlayerGroup(src)
    
    if not group or group.leader ~= src then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.not_group_leader'), 'error')
        return
    end
    
    local targetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not targetPlayer then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.player_not_found'), 'error')
        return
    end
    
    if IsPlayerInGroup(targetId) then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.player_already_in_group'), 'error')
        return
    end
    
    if #group.members >= Config.MaximumPlayers then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.group_full'), 'error')
        return
    end
        -- Envoyer l'invitation au joueur
    TriggerClientEvent('QBCore:Notify', targetId, Lang:t('info.group_invitation'), 'primary')
    TriggerClientEvent('pc_electrician:client:groupInvitation', targetId, groupId)
end)

RegisterNetEvent('pc_electrician:server:acceptInvitation', function(groupId)
    local src = source
    
    local group = activeGroups[groupId]
    
    if not group then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.group_not_found'), 'error')
        return
    end
    
    if #group.members >= Config.MaximumPlayers then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.group_full'), 'error')
        return
    end
    
    table.insert(group.members, src)
    
    -- Mettre à jour les noms des joueurs pour tous les membres du groupe
    local playerNames = {}
    for _, memberId in ipairs(group.members) do
        playerNames[memberId] = GetPlayerFullName(memberId)
    end
    
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('pc_electrician:client:updatePlayerNames', memberId, playerNames)
    end
    
    NotifyGroup(group, Lang:t('success.player_joined_group'), 'success')
    
    -- Mettre à jour tous les membres du groupe
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('pc_electrician:client:groupUpdated', memberId, group)
    end
end)

RegisterNetEvent('pc_electrician:server:getPlayersList', function()
    local src = source
    
    local players = {}
    local allPlayers = GetPlayers()
    
    -- Récupérer la position du joueur qui demande la liste
    local playerPed = GetPlayerPed(src)
    local playerCoords = GetEntityCoords(playerPed)
    
    for _, playerId in ipairs(allPlayers) do
        playerId = tonumber(playerId)
        
        if playerId and playerId ~= src then            
            local targetPed = GetPlayerPed(playerId)
            if DoesEntityExist(targetPed) then
                
                local targetCoords = GetEntityCoords(targetPed)
                local distance = #(vector3(playerCoords.x, playerCoords.y, playerCoords.z) - vector3(targetCoords.x, targetCoords.y, targetCoords.z))
                                
                -- Ne garder que les joueurs dans un rayon de 10 mètres
                if distance <= 10.0 then
                    local playerName = GetPlayerFullName(playerId)
                    if playerName then
                        players[playerId] = playerName
                    else
                    end
                else
                end
            else
            end
        else
        end
    end
    
    TriggerClientEvent('pc_electrician:client:showInviteMenu', src, players)
end)

RegisterNetEvent('pc_electrician:server:startActivity', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player then return end
    
    local groupId, group = GetPlayerGroup(src)
    if not group then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.not_in_group'), 'error')
        return
    end
    
    if group.leader ~= src then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.not_group_leader'), 'error')
        return
    end
    
    if group.active then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.activity_already_started'), 'error')
        return
    end
    
    -- Initialiser les points réparés pour ce groupe
    repairedPoints[groupId] = {}
    repairingPoints[groupId] = {}
    
    -- Marquer le groupe comme actif
    group.active = true
    
    -- Ajouter tous les membres du groupe à la liste des joueurs actifs
    for _, memberId in ipairs(group.members) do
        activePlayers[memberId] = groupId
    end
    
    -- Démarrer l'activité pour tous les membres du groupe
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('pc_electrician:client:activityStarted', memberId, memberId == src)
    end
end)

-- Nouvel événement pour partager l'information du véhicule
RegisterNetEvent('pc_electrician:server:shareVehicleInfo', function(netId)
    local src = source    
    local groupId = activePlayers[src]
    if not groupId then 
        return 
    end
    
    local group = activeGroups[groupId]
    if not group then 
        return 
    end
    
    -- Envoyer l'information du véhicule à tous les membres du groupe
    for _, memberId in ipairs(group.members) do
        if memberId ~= src then -- Ne pas envoyer au leader qui a déjà l'information
            TriggerClientEvent('pc_electrician:client:setVehicle', memberId, netId)
        end
    end
    
    -- Donner les clés du véhicule à tous les membres du groupe
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if DoesEntityExist(vehicle) then
        local plate = QBCore.Functions.GetPlate(vehicle)
        for _, memberId in ipairs(group.members) do
            TriggerClientEvent('vehiclekeys:client:SetOwner', memberId, plate)
        end
    end
end)

-- Événement pour informer tous les membres du groupe que le véhicule est prêt
RegisterNetEvent('pc_electrician:server:vehicleReady', function(netId)
    local src = source
    
    local groupId = activePlayers[src]
    if not groupId then 
        return 
    end
    
    local group = activeGroups[groupId]
    if not group then 
        return 
    end
    
    -- Informer tous les membres du groupe que le véhicule est prêt
    for _, memberId in ipairs(group.members) do
        if memberId ~= src then -- Ne pas envoyer au leader qui a déjà l'information
            TriggerClientEvent('pc_electrician:client:vehicleReady', memberId, netId)
        end
    end
end)

-- Événement pour vérifier la disponibilité d'un point de livraison
RegisterNetEvent('pc_electrician:server:checkPointAvailable', function(pointIndex)
    local src = source
    local groupId, group = GetPlayerGroup(src)
    
    if not group then return end
    
    -- Vérifier si le point a déjà été réparé
    if repairedPoints[groupId] and repairedPoints[groupId][pointIndex] then
        TriggerClientEvent('pc_electrician:client:pointNotAvailable', src, 'already_repaired')
        return
    end
    
    -- Vérifier si le point est en cours de réparation
    if repairingPoints[groupId] and repairingPoints[groupId][pointIndex] then
        TriggerClientEvent('pc_electrician:client:pointNotAvailable', src, 'being_repaired')
        return
    end
    
    -- Récupérer la run complète à partir du nom
    local currentRun = nil
    if group.currentRun then
        for _, run in ipairs(Config.Runs) do
            if run.name == group.currentRun then
                currentRun = run
                break
            end
        end
    end
    
    -- Vérifier si l'index est valide
    if not pointIndex or not currentRun or not currentRun.repairPoints[pointIndex] then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.invalid_repair_point'), 'error')
        return
    end
    
    -- Marquer le point comme en cours de réparation
    if not repairingPoints[groupId] then
        repairingPoints[groupId] = {}
    end
    repairingPoints[groupId][pointIndex] = src
    
    -- Informer le client que le point est disponible
    TriggerClientEvent('pc_electrician:client:pointAvailable', src, pointIndex)
end)

RegisterNetEvent('pc_electrician:server:completeRepair', function(pointIndex)
    local src = source
    
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then 
        return 
    end
    
    local groupId = activePlayers[src]
    if not groupId then 
        return 
    end
    
    local group = activeGroups[groupId]
    if not group then 
        return 
    end
    
    -- Vérifier si le point est en cours de réparation par ce joueur
    if not repairingPoints[groupId] or not repairingPoints[groupId][pointIndex] or repairingPoints[groupId][pointIndex] ~= src then
        return
    end
    
    -- Vérifier si le point a déjà été réparé
    if repairedPoints[groupId] and repairedPoints[groupId][pointIndex] then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.point_already_repaired'), 'error')
        return
    end
    
    -- Récupérer la run complète à partir du nom
    local currentRun = nil
    if group.currentRun then
        for _, run in ipairs(Config.Runs) do
            if run.name == group.currentRun then
                currentRun = run
                break
            end
        end
    end
    
    -- Vérifier si l'index est valide
    if not pointIndex or not currentRun or not currentRun.repairPoints[pointIndex] then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.invalid_repair_point'), 'error')
        return
    end
    
    -- Marquer le point comme réparé
    if not repairedPoints[groupId] then
        repairedPoints[groupId] = {}
    end
    repairedPoints[groupId][pointIndex] = true
    
    -- Libérer le point de la liste des points en cours de réparation
    if repairingPoints[groupId] then
        repairingPoints[groupId][pointIndex] = nil
    end
    
    -- Informer tous les membres du groupe que le point a été réparé
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('pc_electrician:client:pointRepaired', memberId, pointIndex)
    end
end)

-- Événement pour terminer l'activité
RegisterNetEvent('pc_electrician:server:endActivity', function(completed)
    local src = source
    local groupId, group = GetPlayerGroup(src)
    
    if not group then return end
    
    -- Si l'activité est terminée avec succès
    if completed then
        -- Vérifier si tous les points sont réparés
        local allPointsRepaired = true
        
        -- Récupérer la run complète à partir du nom
        local currentRun = nil
        if group.currentRun then
            for _, run in ipairs(Config.Runs) do
                if run.name == group.currentRun then
                    currentRun = run
                    break
                end
            end
        end
        
        if currentRun and currentRun.repairPoints then
            if not repairedPoints[groupId] then
                allPointsRepaired = false
            else
                for i = 1, #currentRun.repairPoints do
                    if not repairedPoints[groupId][i] then
                        allPointsRepaired = false
                        break
                    else
                    end
                end
            end
        else
            allPointsRepaired = false
        end
        
        if allPointsRepaired then
            -- Calculer la récompense en fonction du nombre de membres
            local baseReward = currentRun and currentRun.rewards and currentRun.rewards.complete and currentRun.rewards.complete.money or 1000
            local multiplier = Config.GroupMultipliers[#group.members] or 1.0
            local finalReward = math.floor(baseReward * multiplier)
            
            
            -- Donner la récompense à tous les membres du groupe
            for _, memberId in ipairs(group.members) do
                local Player = QBCore.Functions.GetPlayer(memberId)
                if Player then
                    Player.Functions.AddMoney('bank', finalReward, 'electrician-job-payment')
                    TriggerClientEvent('QBCore:Notify', memberId, Lang:t('success.activity_completed', {money = finalReward, multiplier = math.floor((multiplier - 1) * 100)}), 'success')
                end
            end
        else
            -- Notifier que l'activité n'est pas complète
            for _, memberId in ipairs(group.members) do
                TriggerClientEvent('QBCore:Notify', memberId, Lang:t('info.activity_incomplete'), 'error')
            end
        end
    else
        -- Notifier que l'activité a été annulée
        for _, memberId in ipairs(group.members) do
            TriggerClientEvent('QBCore:Notify', memberId, Lang:t('info.activity_incomplete'), 'error')
        end
    end
    
    -- Nettoyer le groupe
    activeGroups[groupId] = nil
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('pc_electrician:client:groupUpdated', memberId, nil)
    end
end)

RegisterNetEvent('pc_electrician:server:kickMember', function(targetId)
    local src = source
    local groupId, group = GetPlayerGroup(src)
    
    if not group then return end
    
    -- Vérifier si c'est bien le leader qui fait la demande
    if group.leader ~= src then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.not_group_leader'), 'error')
        return
    end
    
    -- Vérifier si le joueur cible est dans le groupe
    local isInGroup = false
    for _, memberId in ipairs(group.members) do
        if memberId == targetId then
            isInGroup = true
            break
        end
    end
    
    if not isInGroup then
        TriggerClientEvent('QBCore:Notify', src, Lang:t('error.player_not_in_group'), 'error')
        return
    end
    
    -- Retirer le joueur du groupe
    for i, memberId in ipairs(group.members) do
        if memberId == targetId then
            table.remove(group.members, i)
            break
        end
    end
    
    -- Informer le joueur qu'il a été kick
    TriggerClientEvent('QBCore:Notify', targetId, Lang:t('info.kicked_from_group'), 'error')
    TriggerClientEvent('pc_electrician:client:groupUpdated', targetId, nil)
    
    -- Informer le reste du groupe
    NotifyGroup(group, Lang:t('info.player_kicked'), 'primary')
    
    -- Mettre à jour le groupe pour tous les membres
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('pc_electrician:client:groupUpdated', memberId, group)
    end
end)

RegisterNetEvent('pc_electrician:server:setCurrentRun', function(runName)
    local src = source
    local groupId, group = GetPlayerGroup(src)
    
    if not group then return end
    
    -- Stocker la run dans le groupe
    group.currentRun = runName
end)

RegisterNetEvent('pc_electrician:server:updateWaypoint', function(pointIndex)
    local src = source
    local groupId = activePlayers[src]
    if not groupId then return end
    
    local group = activeGroups[groupId]
    if not group then return end
    
    -- Envoyer la mise à jour du waypoint à tous les membres du groupe
    for _, memberId in ipairs(group.members) do
        TriggerClientEvent('pc_electrician:client:updateWaypoint', memberId, pointIndex)
    end
end)

-- Nettoyage des groupes inactifs
CreateThread(function()
    while true do
        Wait(300000) -- Vérifier toutes les 5 minutes
        for groupId, group in pairs(activeGroups) do
            local allOffline = true
            for _, memberId in ipairs(group.members) do
                if QBCore.Functions.GetPlayer(memberId) then
                    allOffline = false
                    break
                end
            end
            if allOffline then
                activeGroups[groupId] = nil
            end
        end
    end
end) 