let messageInterval;
let config = {
    drugs: [],
    messageInterval: {
        min: 30000,
        max: 120000
    }
};

// Modifier la déclaration initiale des conversations pour charger depuis le localStorage
let conversations = JSON.parse(localStorage.getItem('drugConversations')) || {
    '<PERSON>': [],
    '<PERSON>': [],
    '<PERSON>': [],
    '<PERSON>\'<PERSON>': [],
    '<PERSON>': [],
    '<PERSON>': [],
    '<PERSON>': [],
    '<PERSON>': [],
    '<PERSON>': []
};

let isDeliveryInProgress = false;

// Ajouter une variable pour suivre si on peut envoyer des messages
let canSendMessages = false;

let phonePoweredOn = false;

// Modifier le gestionnaire de message pour l'ouverture du téléphone
window.addEventListener('message', function(event) {
    if (event.data.action === "openPhone") {
        const phone = document.getElementById('phone');
        phone.classList.remove('hidden');
        // Forcer le reflow pour que l'animation fonctionne même si déjà visible
        void phone.offsetWidth;
        phone.classList.add('show');
        updateTime();
        
        phonePoweredOn = true;
        // Synchroniser l'état avec Lua dès l'ouverture
        const selectedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]:checked')).map(cb => cb.value);
        fetch(`https://${GetParentResourceName()}/powerButton`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ power: true, selectedDrugs })
        });
        
        // Stocker les configurations reçues
        if (event.data.config) {
            config = event.data.config;
            // Mettre à jour canSendMessages selon le nombre de policiers
            canSendMessages = event.data.config.hasEnoughCops;
            
            // Vérifier les drogues cochées
            const selectedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]:checked')).map(checkbox => checkbox.value);
            
            // Démarrer les messages automatiques si on a assez de policiers et des drogues cochées
            if (canSendMessages && selectedDrugs.length > 0) {
                startAutomaticMessages();
            } else if (!canSendMessages) {
            } else if (selectedDrugs.length === 0) {
            }
        }
        // Ajout : rafraîchir les indicateurs à l'ouverture du téléphone
        updateMessageIndicators();
    }
});

// Modifier la fonction startAutomaticMessages
function startAutomaticMessages() {
    // Si on ne peut pas envoyer de messages, ne rien faire
    if (!canSendMessages) {
        return;
    }

    // Arrêter l'intervalle précédent s'il existe
    if (messageInterval) {
        clearInterval(messageInterval);
    }

    // Calculer le délai aléatoire pour le premier message
    const firstMessageDelay = Math.random() * (config.messageInterval.max - config.messageInterval.min) + config.messageInterval.min;
    
    // Configurer le premier message avec le délai
    setTimeout(() => {
        if (!isDeliveryInProgress && canSendMessages) {
            sendRandomMessage();
            
            // Configurer l'intervalle pour les messages suivants après l'envoi du premier message
            messageInterval = setInterval(() => {
                if (isDeliveryInProgress || !canSendMessages) {
                    stopAutomaticMessages();
                    return;
                }
                sendRandomMessage();
            }, Math.random() * (config.messageInterval.max - config.messageInterval.min) + config.messageInterval.min);
        }
    }, firstMessageDelay);
}

// Ajouter une fonction pour vérifier si un contact a une demande en attente
function hasUnprocessedRequest(contact) {
    return conversations[contact]?.some(msg => 
        msg.type === 'received' && 
        msg.drugData && 
        !msg.drugData.isProcessed
    );
}

// Ajouter une fonction pour mettre à jour les indicateurs de nouveaux messages
function updateMessageIndicators() {
    const contacts = document.querySelectorAll('.message-contact');
    contacts.forEach(contact => {
        const contactName = contact.dataset.contact;
        const hasUnreadMessage = conversations[contactName]?.some(msg => 
            msg.type === 'received' && 
            msg.drugData && 
            !msg.drugData.isProcessed
        );
        // Supprimer les anciens indicateurs
        const existingIndicator = contact.querySelector('.new-message-indicator');
        const existingCount = contact.querySelector('.unread-count');
        if (existingIndicator) existingIndicator.remove();
        if (existingCount) existingCount.remove();
        if (hasUnreadMessage) {
            // Ajouter l'indicateur de nouveau message
            const indicator = document.createElement('div');
            indicator.className = 'new-message-indicator';
            // Compter les messages non lus
            const unreadCount = conversations[contactName].filter(msg => 
                msg.type === 'received' && 
                msg.drugData && 
                !msg.drugData.isProcessed
            ).length;
            // Ajouter le compteur
            const count = document.createElement('span');
            count.className = 'unread-count';
            count.textContent = unreadCount;
            contact.appendChild(indicator);
            contact.appendChild(count);
        }
    });
}

// Modifier la fonction sendRandomMessage
async function sendRandomMessage() {
    
    // Vérifier d'abord le nombre de policiers
    try {
        const response = await fetch(`https://${GetParentResourceName()}/checkPoliceCount`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        
        // Si pas assez de policiers, arrêter les messages
        if (!data.enoughCops) {
            stopAutomaticMessages();
            return;
        }

        // Vérifier si une livraison est en cours
        if (isDeliveryInProgress) {
            return; // Ne pas envoyer de message si une livraison est en cours
        }

        // Récupérer les drogues cochées
        const selectedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]:checked')).map(checkbox => checkbox.value);
        
        // Si aucune drogue n'est cochée, ne rien faire
        if (selectedDrugs.length === 0) {
            return;
        }

        // Filtrer les contacts qui n'ont pas de demande en attente
        const contacts = [
            'Marcus Rodriguez',
            'Sarah Chen',
            'Antoine Dubois',
            'Liam O\'Connor',
            'Isabella Santos',
            'Viktor Petrov',
            'Jasmine Williams',
            'Mohammed Al-Sayed',
            'Elena Kovač'
        ].filter(contact => !hasUnprocessedRequest(contact));

        // Si aucun contact n'est disponible, ne rien faire
        if (contacts.length === 0) {
            return;
        }
        
        const randomContact = contacts[Math.floor(Math.random() * contacts.length)];
        
        // Sélectionner une drogue aléatoire parmi celles cochées
        const randomDrugName = selectedDrugs[Math.floor(Math.random() * selectedDrugs.length)];
        const randomDrug = config.drugs.find(drug => drug.name === randomDrugName);
        
        const amount = Math.floor(Math.random() * (randomDrug.maxAmount - randomDrug.minAmount + 1)) + randomDrug.minAmount;
        const totalPrice = amount * randomDrug.pricePerUnit;
        
        // Construire le message avec la quantité et le prix
        const baseMessage = randomDrug.messages[Math.floor(Math.random() * randomDrug.messages.length)];
        const fullMessage = `${baseMessage} Il me faut ${amount} ${randomDrug.label} pour ${totalPrice}$`;
        
        // Ajouter le message à la conversation
        if (!conversations[randomContact]) {
            conversations[randomContact] = [];
        }
        
        conversations[randomContact].push({
            type: 'received',
            message: fullMessage,
            drugData: {
                name: randomDrug.name,
                amount: amount,
                price: totalPrice,
                isProcessed: false
            }
        });
        
        saveConversations();
        updateMessageIndicators(); // Mettre à jour les indicateurs
        
        // Envoyer l'événement au client
        fetch(`https://${GetParentResourceName()}/receivedMessage`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contact: randomContact,
                message: fullMessage,
                drugData: {
                    name: randomDrug.name,
                    amount: amount,
                    price: totalPrice
                }
            })
        });
        
        // Mettre à jour l'affichage si nécessaire
        if (document.getElementById('currentContact').textContent === randomContact) {
            displayConversation(randomContact);
        }
    } catch (error) {
        console.error('Erreur lors de la vérification du nombre de policiers:', error);
        stopAutomaticMessages();
    }
}

// Déplacer également la fonction stopAutomaticMessages
function stopAutomaticMessages() {
    if (messageInterval) {
        clearInterval(messageInterval);
    }
}

// Modifier la fonction displayConversation pour ajouter le bouton GPS
function displayConversation(contact) {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = '';
    
    if (conversations[contact]) {
        conversations[contact].forEach(msg => {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', msg.type);
            
            // Si c'est un message de localisation, ajouter le bouton GPS
            if (msg.isLocation) {
                const messageContent = document.createElement('div');
                messageContent.textContent = msg.message;
                
                const gpsButton = document.createElement('button');
                gpsButton.classList.add('gps-button');
                gpsButton.textContent = '📍 GPS';
                gpsButton.onclick = () => {
                    fetch(`https://${GetParentResourceName()}/setGPS`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            coords: msg.locationData
                        })
                    });
                };
                
                messageElement.appendChild(messageContent);
                messageElement.appendChild(gpsButton);
            } else {
                messageElement.textContent = msg.message;
            }
            
            chatMessages.appendChild(messageElement);
        });
    }
    
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

document.addEventListener('keydown', function(event) {
    if (event.key === "Escape") {
        closePhone();
    }
});

window.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        try {
            const selectedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]:checked')).map(cb => cb.value);
            fetch(`https://${GetParentResourceName()}/powerButton`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ power: false, selectedDrugs })
            });
        } catch (e) {
            console.error('[Drugselling][Init] Erreur lors de la synchro initiale avec Lua:', e);
        }
    }, 100);

    const phone = document.getElementById('phone');
    const powerScreen = document.getElementById('powerScreen');
    const mainScreen = document.getElementById('mainScreen');
    const contactsScreen = document.getElementById('contactsScreen');
    const powerButton = document.getElementById('powerButton');
    const contactButton = document.getElementById('contactButton');
    const backButton = document.getElementById('backButton');
    const messagesScreen = document.getElementById('messagesScreen');
    const chatScreen = document.getElementById('chatScreen');
    const messageButton = document.getElementById('messageButton');
    const backFromMessages = document.getElementById('backFromMessages');
    const backToMessages = document.getElementById('backToMessages');
    const messageContacts = document.querySelectorAll('.message-contact');
    const currentContact = document.getElementById('currentContact');
    const responseYes = document.getElementById('responseYes');
    const responseNo = document.getElementById('responseNo');
    const chatMessages = document.getElementById('chatMessages');
    const closePhoneButton = document.getElementById('closePhoneButton');

    // Fonction pour cacher tous les écrans
    function hideAllScreens() {
        powerScreen.classList.add('hidden');
        mainScreen.classList.add('hidden');
        contactsScreen.classList.add('hidden');
        messagesScreen.classList.add('hidden');
        chatScreen.classList.add('hidden');
    }

    // Fonction pour afficher un écran spécifique
    function showScreen(screen) {
        hideAllScreens();
        screen.classList.remove('hidden');
    }

    // Fonction pour recevoir un message (accessible globalement)
    window.receiveMessage = function(contact, message, drugData = null) {
        // Ajouter le message à la conversation du contact
        if (!conversations[contact]) conversations[contact] = [];
        conversations[contact].push({
            type: 'received',
            message: message,
            drugData: drugData // Toujours présent, même null
        });
        // Mettre à jour les pastilles et compteurs
        updateMessageIndicators();
        // Si on est dans la conversation avec ce contact, afficher le message
        const currentContactName = document.getElementById('currentContact').textContent;
        if (currentContactName === contact) {
            displayConversation(contact);
        }
    };

    // Modifier le gestionnaire du bouton d'allumage
    powerButton.addEventListener('click', function() {
        // Vérifier d'abord le nombre de policiers
        fetch(`https://${GetParentResourceName()}/checkPoliceCount`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => response.json())
        .then(data => {
            canSendMessages = data.enoughCops;
            
            // Afficher la liste des drogues
            displayDrugList();
            // Restaurer les drogues cochées
            restoreCheckedDrugs();
            
            // Vérifier les drogues cochées
            const selectedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]:checked')).map(checkbox => checkbox.value);

            // Synchroniser l'état avec Lua (téléphone allumé)
            phonePoweredOn = true;
            fetch(`https://${GetParentResourceName()}/powerButton`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ power: true, selectedDrugs })
            });
            
            // Réinitialiser les conversations
            conversations = {
                'Marcus Rodriguez': [],
                'Sarah Chen': [],
                'Antoine Dubois': [],
                'Liam O\'Connor': [],
                'Isabella Santos': [],
                'Viktor Petrov': [],
                'Jasmine Williams': [],
                'Mohammed Al-Sayed': [],
                'Elena Kovač': []
            };
            
            // Effacer le localStorage
            localStorage.removeItem('drugConversations');
            
            // Réinitialiser les notifications de nouveaux messages
            const messageIndicators = document.querySelectorAll('.new-message-indicator');
            messageIndicators.forEach(indicator => indicator.remove());
            
            const unreadCounts = document.querySelectorAll('.unread-count');
            unreadCounts.forEach(count => count.remove());
            
            // Afficher l'écran principal
            showScreen(mainScreen);
            
            // Ne démarrer les messages que s'il y a assez de policiers et des drogues cochées
            if (canSendMessages && selectedDrugs.length > 0) {
                startAutomaticMessages();
            } else if (!canSendMessages) {
                fetch(`https://${GetParentResourceName()}/showNotification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: 'Information',
                        message: 'Pas assez de policiers en service pour le moment',
                        type: 'error'
                    })
                });
            } else if (selectedDrugs.length === 0) {
                fetch(`https://${GetParentResourceName()}/showNotification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: 'Information',
                        message: 'Veuillez sélectionner au moins une drogue dans la page produit',
                        type: 'error'
                    })
                });
            }
        });
    });

    // Gestion du bouton contacts
    contactButton.addEventListener('click', function() {
        showScreen(contactsScreen);
    });

    // Gestion du bouton retour
    backButton.addEventListener('click', function() {
        showScreen(mainScreen);
    });

    // Gestionnaire pour le bouton Messages
    messageButton.addEventListener('click', function() {
        showScreen(messagesScreen);
        // Ajout : rafraîchir les indicateurs à l'ouverture de l'écran messages
        updateMessageIndicators();
    });

    // Retour depuis l'écran des messages
    backFromMessages.addEventListener('click', function() {
        showScreen(mainScreen);
    });

    // Retour vers la liste des messages
    backToMessages.addEventListener('click', function() {
        showScreen(messagesScreen);
    });

    // Gestion des clics sur les contacts pour la messagerie
    messageContacts.forEach(contact => {
        contact.addEventListener('click', function() {
            const contactName = this.dataset.contact;
            currentContact.textContent = contactName;
            showScreen(chatScreen);
            displayConversation(contactName); // Afficher la conversation du contact sélectionné
        });
    });

    // Envoi de message
    responseYes.addEventListener('click', function() {
        sendResponse('Oui');
    });

    responseNo.addEventListener('click', function() {
        sendResponse('Non');
    });

    function sendResponse(response) {
        const contact = document.getElementById('currentContact').textContent;

        // Vérifier si une livraison est en cours
        if (isDeliveryInProgress) {
            // Informer l'utilisateur qu'il ne peut pas répondre
            fetch(`https://${GetParentResourceName()}/showNotification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: 'Erreur',
                    message: 'Vous ne pouvez pas répondre à une autre demande pendant une livraison en cours.',
                    type: 'error'
                })
            });
            return; // Ne pas envoyer de réponse
        }

        // Trouver le dernier message non traité pour ce contact
        const lastMessage = conversations[contact]?.find(msg => 
            msg.type === 'received' && 
            msg.drugData && 
            !msg.drugData.isProcessed
        );

        if (!lastMessage) {
            // Si pas de message non traité, informer l'utilisateur
            fetch(`https://${GetParentResourceName()}/showNotification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: 'Erreur',
                    message: 'Cette demande a déjà été traitée',
                    type: 'error'
                })
            });
            return;
        }

        // Marquer le message comme traité
        lastMessage.drugData.isProcessed = true;

        // Ajouter la réponse à la conversation
        if (!conversations[contact]) {
            conversations[contact] = [];
        }

        conversations[contact].push({
            type: 'sent',
            message: response
        });

        saveConversations(); // Sauvegarder après l'envoi d'une réponse

        // Si la réponse est "Oui", on met la livraison en cours
        if (response === 'Oui') {
            isDeliveryInProgress = true;
            stopAutomaticMessages(); // Arrêter les messages pendant la livraison

            // Simuler un délai avant l'envoi de la localisation (1.5 secondes)
            setTimeout(() => {
                // Envoyer une demande au client pour obtenir une localisation
                fetch(`https://${GetParentResourceName()}/requestDeliveryLocation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contact: contact
                    })
                });
            }, 1500);
        }

        displayConversation(contact);
        updateMessageIndicators(); // Mettre à jour les indicateurs

        // Envoyer la réponse au client avec les données de la drogue
        fetch(`https://${GetParentResourceName()}/drugResponse`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contact: contact,
                response: response,
                drugData: lastMessage?.drugData || null // Ajouter les données de la drogue
            })
        });
    }

    // Modifier le gestionnaire d'événements pour le bouton de fermeture
    closePhoneButton.addEventListener('click', function() {
        phonePoweredOn = false;
        fetch(`https://${GetParentResourceName()}/powerButton`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ power: false, selectedDrugs: [] })
        });
        showScreen(powerScreen);
        // Arrêter les messages automatiques
        stopAutomaticMessages();
    });

    updateMessageIndicators();
});

// Fonction pour fermer le téléphone
function closePhone() {
    const phone = document.getElementById('phone');
    phone.classList.remove('show');
    // Attendre la fin de l'animation avant de cacher
    setTimeout(() => {
        phone.classList.add('hidden');
    }, 500); // Durée identique à la transition CSS
    fetch(`https://${GetParentResourceName()}/closePhone`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    });
}

// Événement pour fermer le téléphone lorsque l'item est réutilisé
window.addEventListener('message', function(event) {
    if (event.data.action === "closePhone") {
        closePhone();
    }
});

function updateTime() {
    const now = new Date();
    const time = now.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    document.querySelector('.time').textContent = time;
}

// Mise à jour de l'heure toutes les minutes
setInterval(updateTime, 60000); 

// Modifier le gestionnaire de message pour la livraison complétée
window.addEventListener('message', function(event) {
    if (event.data.action === "deliveryComplete") {
        isDeliveryInProgress = false;
        
        // Attendre un délai aléatoire avant de redémarrer les messages (entre 10 et 30 secondes)
        setTimeout(() => {
            if (!isDeliveryInProgress) { // Vérifier à nouveau avant de redémarrer
                startAutomaticMessages();
            }
        }, Math.random() * (30000 - 10000) + 10000);
    }
});

// Modifier la partie qui gère la réception de la localisation
window.addEventListener('message', function(event) {
    if (event.data.action === "sendLocation") {
        const contact = event.data.contact;
        const location = event.data.location;
        
        // Créer un message avec un bouton GPS
        conversations[contact].push({
            type: 'received',
            message: `📍 Point de livraison: ${location.label}`,
            isLocation: true,
            locationData: location.coords // Sauvegarder les coordonnées
        });
        
        // Mettre à jour l'affichage si nous sommes dans cette conversation
        if (document.getElementById('currentContact').textContent === contact) {
            displayConversation(contact);
        }
    }
});

// Ajouter une fonction pour sauvegarder les conversations
function saveConversations() {
    localStorage.setItem('drugConversations', JSON.stringify(conversations));
}

// Fonction pour afficher la liste des drogues
function displayDrugList() {
    const drugListContainer = document.getElementById('drugList');
    drugListContainer.innerHTML = ''; // Réinitialiser le contenu

    // Vérifier si config.drugs est défini
    if (config.drugs && config.drugs.length > 0) {
        config.drugs.forEach(drug => {
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = drug.name; // Utiliser le nom de la drogue comme ID
            checkbox.value = drug.name; // Valeur de la case à cocher

            const label = document.createElement('label');
            label.htmlFor = drug.name; // Lier le label à la case à cocher
            label.textContent = `${drug.label} - ${drug.pricePerUnit}$`; // Afficher le nom et le prix

            const image = document.createElement('img');
            image.src = drug.image; // Utiliser le chemin de l'image
            image.alt = drug.label; // Texte alternatif pour l'image
            image.className = 'drug-image'; // Ajouter la classe pour le style

            const div = document.createElement('div');
            div.appendChild(checkbox);
            div.appendChild(label);
            div.appendChild(image); // Ajouter l'image au conteneur
            drugListContainer.appendChild(div); // Ajouter la case à cocher, le label et l'image au conteneur
        });
        // Ajouter les listeners après avoir généré la liste
        addDrugCheckboxListeners();
    } else {
        drugListContainer.textContent = 'Aucune drogue disponible.';
    }
}

// Fonction pour sauvegarder l'état des drogues cochées
function saveCheckedDrugs() {
    const checkedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]'))
        .filter(checkbox => checkbox.checked)
        .map(checkbox => checkbox.value);
    localStorage.setItem('checkedDrugs', JSON.stringify(checkedDrugs));
}

// Fonction pour restaurer l'état des drogues cochées
function restoreCheckedDrugs() {
    const checkedDrugs = JSON.parse(localStorage.getItem('checkedDrugs')) || [];
    checkedDrugs.forEach(drugName => {
        const checkbox = document.getElementById(drugName);
        if (checkbox) {
            checkbox.checked = true; // Cocher la case si elle est dans la liste
        }
    });
}

// Appeler cette fonction lorsque la page produit est affichée
function showProduct() {
    document.getElementById('productScreen').style.display = 'block';
    document.getElementById('mainScreen').style.display = 'none';
    displayDrugList(); // Afficher la liste des drogues
    restoreCheckedDrugs(); // Restaurer l'état des cases à cocher
}

// Appeler la fonction pour sauvegarder l'état des cases à cocher lorsque vous quittez la page produit
function hideProduct() {
    saveCheckedDrugs(); // Sauvegarder l'état des cases à cocher
    document.getElementById('productScreen').style.display = 'none';
    document.getElementById('mainScreen').style.display = 'block';
}

// Synchroniser l'état avec Lua à chaque (dé)cochage de drogue
function addDrugCheckboxListeners() {
    const checkboxes = document.querySelectorAll('#drugList input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (!phonePoweredOn) return;
            const selectedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]:checked')).map(cb => cb.value);
            fetch(`https://${GetParentResourceName()}/powerButton`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ power: true, selectedDrugs })
            });
        });
    });
}

// Relancer les messages automatiques si la police redevient suffisante
window.addEventListener('message', function(event) {
    if (event.data.action === "restartAutomaticMessages") {
        // Vérifier les conditions : téléphone allumé, drogues sélectionnées, pas de livraison
        const selectedDrugs = Array.from(document.querySelectorAll('#drugList input[type="checkbox"]:checked')).map(cb => cb.value);
        if (phonePoweredOn && selectedDrugs.length > 0 && !isDeliveryInProgress) {
            startAutomaticMessages();
        }
    }
});

// Ajout : gestion de la réception des messages d'acheteur depuis Lua
window.addEventListener('message', function(event) {
    if (event.data.action === 'receiveMessage') {
        // Ajoute le message à la conversation du contact
        receiveMessage(event.data.contact, event.data.message, event.data.drugData ?? null);
        // Optionnel : afficher une notification ou mettre à jour l'UI
        updateMessageIndicators();
    }
});

window.addEventListener('message', function(event) {
    if (event.data.action === 'updateMessageIndicators') {
        updateMessageIndicators();
    }
});

// Ajout : gestion du callback NUI pour la vérification d'une demande en cours
window.addEventListener('message', function(event) {
    if (event.data.action === 'hasUnprocessedRequest') {
        const contact = event.data.contact;
        const hasUnprocessed = hasUnprocessedRequest(contact);
        if (window.InvokeNative) {
            // Pour FiveM NUI
            window.postMessage({
                action: 'hasUnprocessedRequestResult',
                hasUnprocessed: hasUnprocessed
            }, '*');
        } else if (typeof fetch === 'function') {
            // Pour callback NUI classique
            fetch('https://' + GetParentResourceName() + '/hasUnprocessedRequestResult', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ hasUnprocessed: hasUnprocessed })
            });
        }
    }
});