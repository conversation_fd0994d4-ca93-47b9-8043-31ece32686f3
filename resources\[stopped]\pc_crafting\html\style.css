* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;
}

.hidden {
    display: none !important;
}

#crafting-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1400px;
    max-height: 70vh;
    background-color: rgba(13, 25, 43, 0.95);
    border-radius: 10px;
    padding: 15px;
    color: white;
    box-shadow: 0 0 20px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(0, 100, 255, 0.3);
}

.crafting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(0, 50, 100, 0.3);
    border-radius: 10px;
    border-bottom: 1px solid rgba(0, 100, 255, 0.3);
    margin-bottom: 15px;
}

#craftingTitle {
    color: #fff;
    margin: 0;
    font-size: 28px;
    font-weight: bold;
}

.close-button {
    background: #0066cc;
    border: none;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s;
}

.close-button:hover {
    background: #0052a3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 100, 255, 0.3);
}

.crafting-content {
    display: flex;
    gap: 15px;
    height: calc(65vh - 100px);
}

.crafting-main {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.crafting-categories {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    padding: 10px;
    background: rgba(0, 50, 100, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(0, 100, 255, 0.2);
}

.category-button {
    background-color: rgba(0, 50, 100, 0.5);
    border: 1px solid rgba(0, 100, 255, 0.3);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.category-button:hover {
    background-color: rgba(0, 100, 255, 0.3);
    transform: translateY(-2px);
}

.category-button.active {
    background-color: #0066cc;
    box-shadow: 0 0 10px rgba(0, 100, 255, 0.5);
}

.crafting-recipes {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    padding: 15px;
    max-height: calc(100vh - 250px);
    overflow-y: auto;
    background: rgba(0, 50, 100, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(0, 100, 255, 0.2);
}

.recipe-card {
    background: rgba(0, 50, 100, 0.3);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    border: 1px solid rgba(0, 100, 255, 0.2);
    transition: all 0.3s;
    cursor: pointer;
}

.recipe-card:hover {
    background: rgba(0, 100, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 100, 255, 0.3);
}

.recipe-card.selected {
    background: rgba(0, 100, 255, 0.4);
    border-color: #0066cc;
    box-shadow: 0 0 15px rgba(0, 100, 255, 0.5);
}

.recipe-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recipe-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.recipe-result-image {
    width: 32px;
    height: 32px;
    object-fit: contain;
    border-radius: 4px;
    background: rgba(0, 100, 255, 0.1);
    padding: 2px;
}

.recipe-name {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
}

.recipe-category {
    background: rgba(0, 100, 255, 0.3);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #fff;
}

.recipe-description {
    color: #ccc;
    font-size: 14px;
    line-height: 1.4;
    min-height: 22px;
    /* Garde l'espace même si vide pour alignement parfait */
    display: block;
}

.recipe-ingredients-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.ingredient-tag {
    background: rgba(255, 100, 100, 0.3);
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 11px;
    color: #ff6b6b;
    border: 1px solid rgba(255, 100, 100, 0.5);
}

.ingredient-tag.available {
    background: rgba(100, 255, 100, 0.3);
    color: #6bff6b;
    border-color: rgba(100, 255, 100, 0.5);
}

.crafting-info {
    width: 400px;
    min-width: 400px;
    background-color: rgba(0, 50, 100, 0.3);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    border: 1px solid rgba(0, 100, 255, 0.3);
    overflow: hidden;
}

.crafting-info h2 {
    font-size: 20px;
    color: #fff;
    margin-bottom: 10px;
    text-align: center;
}

.info-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
    max-height: calc(65vh - 150px);
    padding-right: 5px;
}

.selected-recipe-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.selected-recipe-info h3 {
    color: #0066cc;
    font-size: 18px;
    margin-bottom: 5px;
}

.selected-recipe-info p {
    color: #ccc;
    line-height: 1.4;
}

.recipe-ingredients h4,
.recipe-result h4,
.crafting-time h4 {
    color: #fff;
    font-size: 14px;
    margin-bottom: 8px;
}

#ingredientsList {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 5px;
}

.ingredient-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: rgba(0, 50, 100, 0.2);
    border-radius: 4px;
    border: 1px solid rgba(0, 100, 255, 0.2);
}

.ingredient-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ingredient-image {
    width: 24px;
    height: 24px;
    object-fit: contain;
    border-radius: 3px;
    background: rgba(0, 100, 255, 0.1);
    padding: 1px;
}

.ingredient-name {
    color: #fff;
    font-size: 14px;
}

.ingredient-amount {
    color: #00a3ff;
    font-weight: bold;
}

.ingredient-amount.insufficient {
    color: #ff6b6b;
}

#recipeResult {
    color: #00a3ff;
    font-weight: bold;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-item-image {
    width: 28px;
    height: 28px;
    object-fit: contain;
    border-radius: 4px;
    background: rgba(0, 100, 255, 0.1);
    padding: 2px;
}

#craftingTime {
    color: #ffa500;
    font-weight: bold;
    font-size: 16px;
}

.craft-button {
    background-color: #0066cc;
    border: none;
    color: white;
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
    width: 100%;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.craft-button:hover {
    background-color: #0052a3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 100, 255, 0.3);
}

.craft-button:disabled {
    background-color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.no-recipe-selected {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #ccc;
    font-size: 16px;
    text-align: center;
}

.crafting-quantity {
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    background: rgba(0, 50, 100, 0.10);
    border-radius: 8px;
    padding: 8px 10px 4px 10px;
    width: 100%;
    box-sizing: border-box;
}
.crafting-quantity h4 {
    margin: 0 0 4px 0;
    font-size: 13px;
    color: #ffd700;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}
/* Masquer les flèches natives de l'input number (tous navigateurs) */
#craftQuantity::-webkit-outer-spin-button,
#craftQuantity::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
#craftQuantity[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
}

.crafting-quantity-controls {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    flex-wrap: nowrap;
}

.quantity-btn {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: none;
    background: #0066cc;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 4px rgba(0,100,255,0.10);
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
    padding: 0;
}
.quantity-btn:hover {
    background: #3388ff;
    box-shadow: 0 0 8px #ffd70055;
}

#craftQuantity {
    width: 50px;
    min-width: 50px;
    padding: 5px 4px;
    border-radius: 6px;
    border: 1.5px solid #0066cc;
    background: rgba(13, 25, 43, 0.85);
    color: #fff;
    font-size: 16px;
    outline: none;
    transition: border 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 4px rgba(0,100,255,0.08);
    text-align: center;
    box-sizing: border-box;
}

#quantityMaxInfo {
    display: block;
    font-size: 11px;
    color: #ffd700;
    margin-top: 4px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 50, 100, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 100, 255, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 100, 255, 0.7);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.recipe-card {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive */
@media (max-width: 1200px) {
    .crafting-content {
        flex-direction: column;
    }
    
    .crafting-info {
        width: 100%;
        max-height: 200px;
    }
} 