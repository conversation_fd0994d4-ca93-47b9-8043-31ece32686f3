-- Script pour créer automatiquement les portes ox_doorlock pour les banques
-- Ce script peut être exécuté une seule fois pour configurer les portes

local function createBankDoors()
    print('^3[ASC_ROBBERY]^7 === CRÉATION DES PORTES OX_DOORLOCK POUR LES BANQUES ===')
    
    for bankId, bankConfig in pairs(Config.Banks) do
        local doorCoords = bankConfig.vaultDoorCoords
        local doorName = bankConfig.doorName or ('fleeca_vault_' .. bankId)
        
        -- Données de la porte pour ox_doorlock
        local doorData = {
            name = doorName,
            coords = {
                x = doorCoords.x,
                y = doorCoords.y,
                z = doorCoords.z
            },
            model = GetHashKey(bankConfig.object), -- Hash du modèle de porte
            heading = bankConfig.heading.closed,
            maxDistance = 2.0,
            state = 1, -- 1 = barrée par défaut
            groups = {
                police = 0 -- Seule la police peut débarrer (optionnel)
            },
            hideUi = false
        }
        
        print('^3[ASC_ROBBERY]^7 Création de la porte pour la banque ' .. bankId .. ':')
        print('^3[ASC_ROBBERY]^7   - Nom: ' .. doorName)
        print('^3[ASC_ROBBERY]^7   - Coordonnées: ' .. doorCoords.x .. ', ' .. doorCoords.y .. ', ' .. doorCoords.z)
        print('^3[ASC_ROBBERY]^7   - Modèle: ' .. bankConfig.object .. ' (Hash: ' .. GetHashKey(bankConfig.object) .. ')')
        
        -- Créer la porte via ox_doorlock
        -- Note: Cette fonction peut ne pas exister selon la version d'ox_doorlock
        -- Dans ce cas, vous devrez créer manuellement les portes dans la base de données
        
        -- Exemple de requête SQL à exécuter manuellement :
        local sqlQuery = string.format([[
INSERT INTO `ox_doorlock` (`name`, `data`) VALUES 
('%s', '{"coords":{"x":%f,"y":%f,"z":%f},"model":%d,"heading":%f,"maxDistance":2.0,"state":1,"groups":{"police":0},"hideUi":false}');
        ]], doorName, doorCoords.x, doorCoords.y, doorCoords.z, GetHashKey(bankConfig.object), bankConfig.heading.closed)
        
        print('^2[ASC_ROBBERY]^7 Requête SQL pour la banque ' .. bankId .. ':')
        print('^2[ASC_ROBBERY]^7 ' .. sqlQuery)
        
        -- Si vous voulez exécuter automatiquement (décommentez les lignes suivantes)
        -- MySQL.insert('INSERT INTO `ox_doorlock` (`name`, `data`) VALUES (?, ?)', {
        --     doorName,
        --     json.encode(doorData)
        -- }, function(insertId)
        --     if insertId then
        --         print('^2[ASC_ROBBERY]^7 Porte créée avec succès pour la banque ' .. bankId .. ' (ID: ' .. insertId .. ')')
        --         -- Mettre à jour la configuration avec l'ID de la porte
        --         Config.Banks[bankId].doorId = insertId
        --     else
        --         print('^1[ASC_ROBBERY]^7 Échec de la création de la porte pour la banque ' .. bankId)
        --     end
        -- end)
    end
    
    print('^3[ASC_ROBBERY]^7 === FIN DE LA CRÉATION DES PORTES ===')
    print('^3[ASC_ROBBERY]^7 Instructions:')
    print('^3[ASC_ROBBERY]^7 1. Copiez les requêtes SQL ci-dessus')
    print('^3[ASC_ROBBERY]^7 2. Exécutez-les dans votre base de données')
    print('^3[ASC_ROBBERY]^7 3. Notez les IDs des portes créées')
    print('^3[ASC_ROBBERY]^7 4. Mettez à jour config.lua avec les doorId correspondants')
    print('^3[ASC_ROBBERY]^7 5. Redémarrez ox_doorlock et asc_robbery')
end

-- Commande pour créer les portes (admin uniquement)
RegisterCommand('createBankDoors', function(source, args, rawCommand)
    local src = source
    
    -- Vérifier si c'est un admin
    if src == 0 or IsPlayerAceAllowed(src, 'command.resetrobbery') then
        createBankDoors()
        
        if src ~= 0 then
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Info',
                description = 'Requêtes SQL générées dans la console serveur',
                type = 'info'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Erreur',
            description = 'Vous n\'avez pas la permission d\'utiliser cette commande',
            type = 'error'
        })
    end
end, false)

-- Auto-exécution au démarrage (optionnel, décommentez si nécessaire)
-- CreateThread(function()
--     Wait(5000) -- Attendre que tout soit chargé
--     createBankDoors()
-- end)
