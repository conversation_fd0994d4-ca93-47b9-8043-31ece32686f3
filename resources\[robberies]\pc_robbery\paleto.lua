-- Variables globales pour tracker l'état du braquage
local paletoC4DoorExists = true -- Par dé<PERSON><PERSON>, la porte existe
local paletoWiresCut = false -- Par dé<PERSON><PERSON>, les fils ne sont pas coupés
local paletoMainDoorOpen = false -- Par défaut, la porte principale n'est pas ouverte
local paletoThermiteUsed = false -- Par défaut, la thermite n'a pas été utilisée
local paletoC4Used = false -- Par défa<PERSON>, le C4 n'a pas été utilisé
local paletoDrilledVaults = {} -- Tracker les coffres déjà percés
local playerInPaletoZone = false -- Tracker si le joueur est dans la zone

-- Fonction pour ouvrir la porte principale de Paleto Bank
function openPaletoMainDoor()
    local doorObject = GetClosestObjectOfType(
        Config.Paletobank.mainDoorCoords.x,
        Config.Paletobank.mainDoorCoords.y,
        Config.Paletobank.mainDoorCoords.z,
        5.0,
        GetHashKey(Config.Paletobank.mainDoorObject),
        false, false, false
    )

    if doorObject ~= 0 then
        SetEntityHeading(doorObject, Config.Paletobank.heading.open)
    else
    end
end

-- Fonction pour fermer la porte principale de Paleto Bank
function closePaletoMainDoor()
    local doorObject = GetClosestObjectOfType(
        Config.Paletobank.mainDoorCoords.x,
        Config.Paletobank.mainDoorCoords.y,
        Config.Paletobank.mainDoorCoords.z,
        5.0,
        GetHashKey(Config.Paletobank.mainDoorObject),
        false, false, false
    )

    if doorObject ~= 0 then
        SetEntityHeading(doorObject, Config.Paletobank.heading.closed)
    else
    end
end



-- Demander la synchronisation automatique au démarrage
CreateThread(function()
    Wait(3000) -- Attendre que tout soit chargé
    TriggerServerEvent('asc_robbery:requestFullSync')
end)

-- Créer les ox_target pour Paleto Bank
CreateThread(function()
    Wait(2000) -- Attendre que tout soit chargé

    -- Vérifier si Config existe
    if not Config or not Config.Paletobank then
        return
    end

    -- 1. Ox_target pour couper les fils
    if Config.Paletobank.cutWireCoords then
        local coords = Config.Paletobank.cutWireCoords

        local success = pcall(function()
            exports.ox_target:addBoxZone({
                coords = coords,
                size = vec3(2, 2, 2),
                rotation = 0,
                debug = false, -- Activé pour voir la zone
                options = {
                    {
                        name = 'paleto_cut_wires',
                        icon = 'fas fa-cut',
                        label = 'Couper les fils',
                        onSelect = function()
                            TriggerServerEvent('asc_robbery:attemptPaletoCutWires')
                        end
                    }
                }
            })
        end)

        if success then
        else
        end
    end

    -- 2. Ox_target pour hacker la porte principale (thermite)
    if Config.Paletobank.mainDoorHack then
        local thermiteCoords = Config.Paletobank.mainDoorHack
        local success = pcall(function()
            exports.ox_target:addBoxZone({
                coords = thermiteCoords,
                size = vec3(1.5, 1.5, 2),
                rotation = 0,
                debug = false, -- Activé pour voir la zone
                options = {
                    {
                        name = 'paleto_thermite_door',
                        icon = 'fas fa-fire',
                        label = 'Pirater le système',
                        canInteract = function()
                            -- Permettre l'interaction seulement si les fils sont coupés ET que la thermite n'a pas déjà été utilisée
                            return paletoWiresCut and not paletoThermiteUsed
                        end,
                        onSelect = function()
                            -- Vérifier côté serveur avant de continuer
                            TriggerServerEvent('asc_robbery:attemptPaletoMainDoorHack')
                        end
                    }
                }
            })
        end)

        if success then
        else
        end
    end

    -- 3. Ox_target pour faire exploser la porte C4
    if Config.Paletobank.c4DoorCoords then
        local c4Coords = Config.Paletobank.c4DoorCoords

        local success = pcall(function()
            exports.ox_target:addBoxZone({
                coords = c4Coords,
                size = vec3(1.5, 1.5, 2),
                rotation = 0,
                debug = false, -- Activé pour voir la zone
                options = {
                    {
                        name = 'paleto_c4_door',
                        icon = 'fas fa-bomb',
                        label = 'Placer le C4',
                        canInteract = function()
                            -- Permettre l'interaction seulement si les fils sont coupés ET que le C4 n'a pas déjà été utilisé
                            return paletoWiresCut and not paletoC4Used
                        end,
                        onSelect = function()
                            -- Vérifier côté serveur avant de continuer
                            TriggerServerEvent('asc_robbery:attemptPaletoC4Door')
                        end
                    }
                }
            })
        end)

        if success then
        else
        end
    end

    -- 4. Ox_target pour percer les coffres du vault
    if Config.Paletobank.vaultRewardCoords then

        for i, vaultCoords in ipairs(Config.Paletobank.vaultRewardCoords) do

            local success = pcall(function()
                exports.ox_target:addBoxZone({
                    coords = vaultCoords,
                    size = vec3(1.0, 1.0, 1.5),
                    rotation = 0,
                    debug = false, -- Activé pour voir la zone
                    options = {
                        {
                            name = 'paleto_drill_vault_' .. i,
                            icon = 'fas fa-tools',
                            label = 'Percer le coffre',
                            canInteract = function()
                                -- Permettre l'interaction si les fils sont coupés et le coffre pas encore percé
                                return paletoWiresCut and not paletoDrilledVaults[i]
                            end,
                            onSelect = function()
                                -- Vérifier côté serveur avant de continuer
                                TriggerServerEvent('asc_robbery:attemptPaletoDrillVault', i, vaultCoords)
                            end
                        }
                    }
                })
            end)

            if success then
            else
            end
        end
    end
end)

-- Commande de debug pour aller aux coordonnées
RegisterCommand('gotopaleto', function()
    if Config and Config.Paletobank and Config.Paletobank.cutWireCoords then
        local coords = Config.Paletobank.cutWireCoords
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)

        exports['ox_lib']:notify({
            title = 'Debug Paleto',
            description = 'Téléporté aux coordonnées de Paleto Bank',
            type = 'success'
        })
    else
    end
end, false)

-- Commande de debug pour aller à la porte principale
RegisterCommand('gotopaletooor', function()
    if Config and Config.Paletobank and Config.Paletobank.mainDoorCoords then
        local coords = Config.Paletobank.mainDoorCoords
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)

        exports['ox_lib']:notify({
            title = 'Debug Paleto Door',
            description = 'Téléporté à la porte principale de Paleto Bank',
            type = 'success'
        })
    else
    end
end, false)

-- Commande de debug pour aller à la porte C4
RegisterCommand('gotopaletoC4', function()
    if Config and Config.Paletobank and Config.Paletobank.c4DoorCoords then
        local coords = Config.Paletobank.c4DoorCoords
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)

        exports['ox_lib']:notify({
            title = 'Debug Paleto C4',
            description = 'Téléporté à la porte C4 de Paleto Bank',
            type = 'success'
        })

        -- Déclencher automatiquement la recherche d'objets
        Wait(1000)
        TriggerEvent('asc_robbery:checkAndReplaceC4Door')
    else
    end
end, false)

-- Commande pour rechercher manuellement la porte C4
RegisterCommand('findpaletoc4', function()
    if Config and Config.Paletobank and Config.Paletobank.c4DoorCoords then
        TriggerEvent('asc_robbery:checkAndReplaceC4Door')
    else
    end
end, false)

-- Commande pour tester la création d'objets de porte
RegisterCommand('testc4objects', function()
    if not Config or not Config.Paletobank or not Config.Paletobank.c4DoorCoords then
        return
    end

    local coords = Config.Paletobank.c4DoorCoords
    local testObjects = {
        'prop_fnclink_03gate5',
        'prop_gate_prison_01',
        'prop_gate_airport_01',
        'prop_barrier_work05',
        'prop_barrier_work01a',
        'v_ilev_gb_vauldr',
        'prop_sec_barrier_01a',
        'prop_sec_barrier_02a'
    }


    for i, objectName in ipairs(testObjects) do
        local objectHash = GetHashKey(objectName)
        local testObject = CreateObject(
            objectHash,
            coords.x + (i * 2), -- Décaler chaque objet
            coords.y,
            coords.z,
            true, true, false
        )

        if testObject ~= 0 then
            SetEntityHeading(testObject, 45.0)
            FreezeEntityPosition(testObject, true)
            SetEntityAsMissionEntity(testObject, true, true)

            -- Supprimer l'objet après 5 secondes
            CreateThread(function()
                Wait(5000)
                DeleteObject(testObject)
                DeleteEntity(testObject)
            end)
        else
        end

        Wait(100) -- Petit délai entre les créations
    end

    exports['ox_lib']:notify({
        title = 'Test C4',
        description = 'Test de création d\'objets terminé (objets supprimés dans 5s)',
        type = 'info'
    })
end, false)

-- Commande pour tester la recherche de porte manuellement
RegisterCommand('findpaletodoor', function()
    if not Config or not Config.Paletobank then
        return
    end

    local coords = Config.Paletobank.mainDoorCoords
    local objectName = Config.Paletobank.mainDoorObject
    local objectHash = GetHashKey(objectName)

    local doorObject = GetClosestObjectOfType(
        coords.x, coords.y, coords.z,
        10.0,
        objectHash,
        false, false, false
    )

    if doorObject ~= 0 then
        local doorCoords = GetEntityCoords(doorObject)
        local heading = GetEntityHeading(doorObject)
    else
    end
end, false)

-- Commande pour supprimer manuellement l'entité v_ilev_cbankvaulgate01
RegisterCommand('deletepalegate', function()
    if not Config or not Config.Paletobank then
        return
    end

    local coords = Config.Paletobank.mainDoorCoords
    local gateObjectHash = GetHashKey('v_ilev_cbankvaulgate01')


    local gateObject = GetClosestObjectOfType(
        coords.x, coords.y, coords.z,
        20.0,
        gateObjectHash,
        false, false, false
    )

    if gateObject ~= 0 then
        local gateCoords = GetEntityCoords(gateObject)
        -- Méthode plus efficace pour supprimer l'objet
        SetEntityAsMissionEntity(gateObject, true, true)
        DeleteObject(gateObject)
        DeleteEntity(gateObject)

        -- Vérifier si l'objet existe encore
        Wait(100)
        if DoesEntityExist(gateObject) then
            SetEntityVisible(gateObject, false, false)
            SetEntityCollision(gateObject, false, false)
            FreezeEntityPosition(gateObject, true)
            SetEntityAlpha(gateObject, 0, false)

            exports['ox_lib']:notify({
                title = 'Debug Paleto',
                description = 'Entité masquée (suppression impossible)',
                type = 'warning'
            })
        else

            exports['ox_lib']:notify({
                title = 'Debug Paleto',
                description = 'Entité v_ilev_cbankvaulgate01 supprimée',
                type = 'success'
            })
        end
    else

        exports['ox_lib']:notify({
            title = 'Debug Paleto',
            description = 'Entité v_ilev_cbankvaulgate01 non trouvée',
            type = 'error'
        })
    end
end, false)

-- Commande pour replacer manuellement la porte C4
RegisterCommand('replacepaletoc4', function()
    TriggerServerEvent('asc_robbery:requestReplaceC4Door')

    exports['ox_lib']:notify({
        title = 'Debug Paleto',
        description = 'Demande de remplacement de la porte C4 envoyée',
        type = 'info'
    })
end, false)

-- Commande pour tester le déplacement de la porte C4
RegisterCommand('testmovec4', function()
    if not Config or not Config.Paletobank then
        return
    end

    local c4ObjectHash = GetHashKey(Config.Paletobank.c4DoorObject)

    -- Chercher la porte à sa position originale
    local c4Object = GetClosestObjectOfType(
        Config.Paletobank.c4DoorOriginalCoords.x,
        Config.Paletobank.c4DoorOriginalCoords.y,
        Config.Paletobank.c4DoorOriginalCoords.z,
        10.0,
        c4ObjectHash,
        false, false, false
    )

    if c4Object ~= 0 then
        local currentCoords = GetEntityCoords(c4Object)

        -- Déplacer vers la position cachée
        SetEntityCoords(
            c4Object,
            Config.Paletobank.c4DoorHiddenCoords.x,
            Config.Paletobank.c4DoorHiddenCoords.y,
            Config.Paletobank.c4DoorHiddenCoords.z,
            false, false, false, true
        )


        exports['ox_lib']:notify({
            title = 'Test C4',
            description = 'Porte déplacée vers la position cachée',
            type = 'success'
        })

        -- Remettre en place après 5 secondes
        CreateThread(function()
            Wait(5000)
            SetEntityCoords(
                c4Object,
                Config.Paletobank.c4DoorOriginalCoords.x,
                Config.Paletobank.c4DoorOriginalCoords.y,
                Config.Paletobank.c4DoorOriginalCoords.z,
                false, false, false, true
            )

            exports['ox_lib']:notify({
                title = 'Test C4',
                description = 'Porte remise à sa position originale',
                type = 'info'
            })
        end)
    else
        exports['ox_lib']:notify({
            title = 'Test C4',
            description = 'Porte C4 non trouvée',
            type = 'error'
        })
    end
end, false)

-- Commande pour trouver la vraie position de la porte C4
RegisterCommand('findrealc4', function()
    if not Config or not Config.Paletobank then
        return
    end

    local c4ObjectHash = GetHashKey(Config.Paletobank.c4DoorObject)

    -- Rechercher dans un rayon très large
    local searchCoords = vector3(-105, 6475, 31) -- Coordonnées approximatives
    local c4Object = GetClosestObjectOfType(
        searchCoords.x, searchCoords.y, searchCoords.z,
        20.0, -- Rayon très large
        c4ObjectHash,
        false, false, false
    )

    if c4Object ~= 0 then
        local realCoords = GetEntityCoords(c4Object)
        local realHeading = GetEntityHeading(c4Object)

        exports['ox_lib']:notify({
            title = 'Porte C4 trouvée',
            description = 'Coordonnées: ' .. string.format("%.2f, %.2f, %.2f", realCoords.x, realCoords.y, realCoords.z),
            type = 'success'
        })

        -- Téléporter le joueur à la porte
        SetEntityCoords(PlayerPedId(), realCoords.x, realCoords.y, realCoords.z, false, false, false, true)

    else

        -- Lister tous les objets proches pour debug
        local nearbyObjects = {}

        for i = 0, 255 do
            local object = GetClosestObjectOfType(searchCoords.x, searchCoords.y, searchCoords.z, 15.0, i, false, false, false)
            if object ~= 0 then
                local objectModel = GetEntityModel(object)
                local objectCoords = GetEntityCoords(object)
                local distance = #(searchCoords - objectCoords)

                table.insert(nearbyObjects, {
                    handle = object,
                    model = objectModel,
                    coords = objectCoords,
                    distance = distance
                })
            end
        end

        for i, obj in ipairs(nearbyObjects) do
            if i <= 5 then -- Limiter à 5 objets
            end
        end

        exports['ox_lib']:notify({
            title = 'Porte C4 non trouvée',
            description = 'Vérifiez la console pour plus de détails',
            type = 'error'
        })
    end
end, false)

-- Commande pour forcer le déplacement de la porte C4 vers sa position originale
RegisterCommand('forcec4reset', function()
    if not Config or not Config.Paletobank then
        return
    end

    local c4ObjectHash = GetHashKey(Config.Paletobank.c4DoorObject)

    -- Chercher la porte dans un rayon très large
    local c4Object = GetClosestObjectOfType(
        -105.0, 6475.0, 31.0, -- Coordonnées approximatives
        30.0, -- Rayon très large
        c4ObjectHash,
        false, false, false
    )

    if c4Object ~= 0 then
        local currentCoords = GetEntityCoords(c4Object)

        SetEntityCoords(
            c4Object,
            Config.Paletobank.c4DoorOriginalCoords.x,
            Config.Paletobank.c4DoorOriginalCoords.y,
            Config.Paletobank.c4DoorOriginalCoords.z,
            false, false, false, true
        )

        -- S'assurer qu'elle est visible et a des collisions
        SetEntityVisible(c4Object, true, false)
        SetEntityCollision(c4Object, true, false)
        SetEntityHeading(c4Object, Config.Paletobank.c4DoorOriginalCoords.w) -- Utiliser le heading du vec4

        -- Vérifier la nouvelle position
        Wait(100)
        local newCoords = GetEntityCoords(c4Object)

        paletoC4DoorExists = true

        exports['ox_lib']:notify({
            title = 'Reset C4 forcé',
            description = 'Porte déplacée vers la position originale',
            type = 'success'
        })

    else
        exports['ox_lib']:notify({
            title = 'Reset C4 forcé',
            description = 'Porte C4 non trouvée',
            type = 'error'
        })
    end
end, false)

-- Commande de debug pour obtenir l'item requis pour couper les fils
RegisterCommand('getpaletoitem', function()
    if Config and Config.Paletobank and Config.Paletobank.requiredItemsCutWire then
        local requiredItem = Config.Paletobank.requiredItemsCutWire
        TriggerServerEvent('asc_robbery:givePaletoItem', requiredItem)
    else
    end
end, false)

-- Commande de debug pour obtenir la thermite
RegisterCommand('getpalethermite', function()
    if Config and Config.Paletobank and Config.Paletobank.requiredItemMainDoor then
        local requiredItem = Config.Paletobank.requiredItemMainDoor
        TriggerServerEvent('asc_robbery:givePaletoItem', requiredItem)
    else
    end
end, false)

-- Commande de debug pour obtenir le C4
RegisterCommand('getpaletoc4', function()
    if Config and Config.Paletobank and Config.Paletobank.requiredItemC4Door then
        local requiredItem = Config.Paletobank.requiredItemC4Door
        TriggerServerEvent('asc_robbery:givePaletoItem', requiredItem)
    else
    end
end, false)

-- Commande de debug pour obtenir la drill
RegisterCommand('getpaletodrill', function()
    if Config and Config.Paletobank and Config.Paletobank.vaultRewardRequiredItem then
        local requiredItem = Config.Paletobank.vaultRewardRequiredItem
        TriggerServerEvent('asc_robbery:givePaletoItem', requiredItem)
    else
    end
end, false)

-- Événement pour recevoir le nombre de policiers du serveur
RegisterNetEvent('asc_robbery:receivePoliceCountPaleto')
AddEventHandler('asc_robbery:receivePoliceCountPaleto', function(policeCount, requiredPolice)

    if policeCount >= requiredPolice then
        exports['ox_lib']:notify({
            title = 'Police',
            description = 'Nombre de policiers suffisant (' .. policeCount .. '/' .. requiredPolice .. ')',
            type = 'success'
        })
    else
        exports['ox_lib']:notify({
            title = 'Police',
            description = 'Pas assez de policiers (' .. policeCount .. '/' .. requiredPolice .. ')',
            type = 'error'
        })
    end
end)

-- Événement pour démarrer le minigame CircuitBreaker
RegisterNetEvent('asc_robbery:startPaletoMinigame')
AddEventHandler('asc_robbery:startPaletoMinigame', function()

    exports['ox_lib']:notify({
        title = 'Minigame',
        description = 'Coupez les fils du système de sécurité...',
        type = 'info'
    })

    -- Démarrer le minigame CircuitBreaker

    local success = exports['glitch-minigames']:StartCircuitBreaker(4, 0)

        if success then
            -- Minigame réussi
            TriggerServerEvent('asc_robbery:paletoMinigameSuccess')
        else
            -- Minigame échoué
            exports['ox_lib']:notify({
                title = 'Échec',
                description = 'Vous avez échoué à couper les fils !',
                type = 'error'
            })
        end
    end)

-- Événement pour réinitialiser la position de la porte au démarrage du script
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(3000) -- Attendre plus longtemps que tout soit chargé

        -- Supprimer l'entité v_ilev_cbankvaulgate01
        local gateObjectHash = GetHashKey('v_ilev_cbankvaulgate01')
        local gateObject = GetClosestObjectOfType(
            Config.Paletobank.mainDoorCoords.x,
            Config.Paletobank.mainDoorCoords.y,
            Config.Paletobank.mainDoorCoords.z,
            20.0, -- Rayon de recherche plus large
            gateObjectHash,
            false, false, false
        )

        if gateObject ~= 0 then
            local gateCoords = GetEntityCoords(gateObject)

            -- Méthode plus efficace pour supprimer l'objet
            SetEntityAsMissionEntity(gateObject, true, true)
            DeleteObject(gateObject)
            DeleteEntity(gateObject)

            -- Vérifier si l'objet existe encore
            Wait(100)
            if DoesEntityExist(gateObject) then
                SetEntityVisible(gateObject, false, false)
                SetEntityCollision(gateObject, false, false)
                FreezeEntityPosition(gateObject, true)
                SetEntityAlpha(gateObject, 0, false)
            else
            end
        else
        end

        local coords = Config.Paletobank.mainDoorCoords
        local objectName = Config.Paletobank.mainDoorObject
        local objectHash = GetHashKey(objectName)

        -- Essayer avec différents rayons de recherche
        local doorObject = 0
        for radius = 2.0, 20.0, 2.0 do
            doorObject = GetClosestObjectOfType(
                coords.x, coords.y, coords.z,
                radius,
                objectHash,
                false, false, false
            )

            if doorObject ~= 0 then
                break
            else
            end
        end

        -- Si toujours pas trouvée, essayer de lister tous les objets proches
        if doorObject == 0 then

            -- Créer une liste des objets proches
            local nearbyObjects = {}
            for i = 0, 255 do
                local object = GetClosestObjectOfType(coords.x, coords.y, coords.z, 10.0, i, false, false, false)
                if object ~= 0 then
                    local objectModel = GetEntityModel(object)
                    local objectCoords = GetEntityCoords(object)
                    local distance = #(vector3(coords.x, coords.y, coords.z) - objectCoords)
                    table.insert(nearbyObjects, {
                        handle = object,
                        model = objectModel,
                        coords = objectCoords,
                        distance = distance
                    })
                end
            end

            for i, obj in ipairs(nearbyObjects) do
                if i <= 5 then -- Limiter à 5 objets pour éviter le spam
                end
            end
        end

        if doorObject ~= 0 then
            local initialHeading = GetEntityHeading(doorObject)
            local doorCoords = GetEntityCoords(doorObject)

            -- Appliquer le nouveau heading
            SetEntityHeading(doorObject, Config.Paletobank.heading.closed)

            -- Vérifier le heading après modification
            Wait(100) -- Petit délai pour s'assurer que le changement est appliqué
            local finalHeading = GetEntityHeading(doorObject)
        else
        end
    end
end)

-- Thread pour s'assurer que l'entité v_ilev_cbankvaulgate01 reste supprimée et que la porte C4 existe
CreateThread(function()
    while true do
        Wait(5000) -- Vérifier toutes les 5 secondes

        if Config and Config.Paletobank then
            -- 1. Supprimer v_ilev_cbankvaulgate01 si elle réapparaît
            if Config.Paletobank.mainDoorCoords then
                local gateObjectHash = GetHashKey('v_ilev_cbankvaulgate01')
                local gateObject = GetClosestObjectOfType(
                    Config.Paletobank.mainDoorCoords.x,
                    Config.Paletobank.mainDoorCoords.y,
                    Config.Paletobank.mainDoorCoords.z,
                    20.0,
                    gateObjectHash,
                    false, false, false
                )

                if gateObject ~= 0 then
                    SetEntityAsMissionEntity(gateObject, true, true)
                    DeleteObject(gateObject)
                    DeleteEntity(gateObject)

                    -- Si la suppression échoue, masquer l'objet
                    Wait(100)
                    if DoesEntityExist(gateObject) then
                        SetEntityVisible(gateObject, false, false)
                        SetEntityCollision(gateObject, false, false)
                        FreezeEntityPosition(gateObject, true)
                        SetEntityAlpha(gateObject, 0, false)
                    end
                end
            end

            -- 2. S'assurer que la porte C4 existe (vérification simple, la création sera gérée par le serveur si nécessaire)
            if Config.Paletobank.c4DoorCoords and Config.Paletobank.c4DoorObject then
                local c4ObjectHash = GetHashKey(Config.Paletobank.c4DoorObject)
                local c4Object = GetClosestObjectOfType(
                    Config.Paletobank.c4DoorCoords.x,
                    Config.Paletobank.c4DoorCoords.y,
                    Config.Paletobank.c4DoorCoords.z,
                    5.0,
                    c4ObjectHash,
                    false, false, false
                )

                if c4Object == 0 then
                    TriggerServerEvent('asc_robbery:requestReplaceC4Door')
                end
            end
        end
    end
end)

-- Événement pour démarrer le minigame thermite de la porte principale
RegisterNetEvent('asc_robbery:startPaletoThermiteMinigame')
AddEventHandler('asc_robbery:startPaletoThermiteMinigame', function()

    exports['ox_lib']:notify({
        title = 'Thermite',
        description = 'Placez la thermite sur la serrure...',
        type = 'info'
    })

    -- Démarrer le minigame thermite (vous pouvez ajuster la difficulté)
    local success = exports['glitch-minigames']:StartVarHack(5, 30)

    if success then
        -- Minigame réussi
        TriggerServerEvent('asc_robbery:paletoThermiteSuccess')
    else
        -- Minigame échoué
        exports['ox_lib']:notify({
            title = 'Échec',
            description = 'La thermite n\'a pas fonctionné !',
            type = 'error'
        })
    end
end)

-- Événement pour démarrer le minigame C4 de la porte secondaire
RegisterNetEvent('asc_robbery:startPaletoC4Minigame')
AddEventHandler('asc_robbery:startPaletoC4Minigame', function()

    exports['ox_lib']:notify({
        title = 'C4',
        description = 'Placez le C4 sur la porte...',
        type = 'info'
    })

    -- Démarrer le minigame C4/thermite (vous pouvez ajuster la difficulté)
    local success = exports['glitch-minigames']:StartVarHack(5, 30)

    if success then
        -- Minigame réussi
        TriggerServerEvent('asc_robbery:paletoC4Success')
    else
        -- Minigame échoué
        exports['ox_lib']:notify({
            title = 'Échec',
            description = 'Le C4 n\'a pas explosé !',
            type = 'error'
        })
    end
end)

-- Événement pour faire exploser la porte C4 de Paleto Bank
RegisterNetEvent('asc_robbery:explodePaletoC4Door', function()

    local c4DoorObject = GetClosestObjectOfType(
        Config.Paletobank.c4DoorCoords.x,
        Config.Paletobank.c4DoorCoords.y,
        Config.Paletobank.c4DoorCoords.z,
        10.0, -- Rayon plus large pour trouver la porte
        GetHashKey(Config.Paletobank.c4DoorObject),
        false, false, false
    )

    if c4DoorObject ~= 0 then
        local doorCoords = GetEntityCoords(c4DoorObject)

        -- Créer l'explosion
        AddExplosion(doorCoords.x, doorCoords.y, doorCoords.z, 2, 10.0, true, false, 1.0) -- Type 2 = explosion normale

        -- Attendre un peu pour l'effet visuel
        Wait(500)

        -- Faire tomber la porte (appliquer une force physique)
        ApplyForceToEntity(c4DoorObject, 1, 0.0, 5.0, -2.0, 0.0, 0.0, 0.0, 0, true, true, true, false, true)

        -- Déplacer la porte vers la position cachée après quelques secondes
        CreateThread(function()
            Wait(100) -- Attendre 3 secondes pour l'effet visuel

            -- Déplacer la porte vers les coordonnées cachées
            SetEntityCoords(
                c4DoorObject,
                Config.Paletobank.c4DoorHiddenCoords.x,
                Config.Paletobank.c4DoorHiddenCoords.y,
                Config.Paletobank.c4DoorHiddenCoords.z,
                false, false, false, true
            )

            -- Optionnel : rendre la porte invisible et sans collision
            SetEntityVisible(c4DoorObject, false, false)
            SetEntityCollision(c4DoorObject, false, false)

            -- Marquer que la porte n'existe plus à sa position originale
            paletoC4DoorExists = false

        end)


        exports['ox_lib']:notify({
            title = 'Explosion !',
            description = 'La porte a explosé !',
            type = 'success'
        })
    else
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Impossible de trouver la porte',
            type = 'error'
        })
    end
end)

-- Événement pour vérifier et replacer la porte C4 (appelé par le serveur)
RegisterNetEvent('asc_robbery:checkAndReplaceC4Door')
AddEventHandler('asc_robbery:checkAndReplaceC4Door', function()
    if not Config or not Config.Paletobank or not Config.Paletobank.c4DoorCoords then
        return
    end


    local c4ObjectHash = GetHashKey(Config.Paletobank.c4DoorObject)

    -- Chercher la porte à sa position originale (rayon plus large)
    local c4ObjectOriginal = GetClosestObjectOfType(
        Config.Paletobank.c4DoorOriginalCoords.x,
        Config.Paletobank.c4DoorOriginalCoords.y,
        Config.Paletobank.c4DoorOriginalCoords.z,
        10.0, -- Rayon plus large
        c4ObjectHash,
        false, false, false
    )

    -- Chercher la porte à la position cachée (rayon plus large)
    local c4ObjectHidden = GetClosestObjectOfType(
        Config.Paletobank.c4DoorHiddenCoords.x,
        Config.Paletobank.c4DoorHiddenCoords.y,
        Config.Paletobank.c4DoorHiddenCoords.z,
        10.0, -- Rayon plus large
        c4ObjectHash,
        false, false, false
    )

    -- Si pas trouvée, chercher dans un rayon très large autour de la zone générale
    local c4ObjectAnywhere = nil
    if c4ObjectOriginal == 0 and c4ObjectHidden == 0 then
        c4ObjectAnywhere = GetClosestObjectOfType(
            -105.0, 6475.0, 31.0, -- Coordonnées approximatives
            25.0, -- Rayon très large
            c4ObjectHash,
            false, false, false
        )

        if c4ObjectAnywhere ~= 0 then
            local anywhereCoords = GetEntityCoords(c4ObjectAnywhere)
        end
    end

    if c4ObjectOriginal ~= 0 then
        -- Vérifier si la porte est vraiment à sa position originale
        local coords = GetEntityCoords(c4ObjectOriginal)
        local originalCoords = vector3(Config.Paletobank.c4DoorOriginalCoords.x, Config.Paletobank.c4DoorOriginalCoords.y, Config.Paletobank.c4DoorOriginalCoords.z)
        local distanceFromOriginal = #(vector3(coords.x, coords.y, coords.z) - originalCoords)


        if distanceFromOriginal < 2.0 then
            -- La porte est vraiment à sa position originale = elle n'a pas explosé

            -- S'assurer qu'elle est visible et a des collisions
            SetEntityVisible(c4ObjectOriginal, true, false)
            SetEntityCollision(c4ObjectOriginal, true, false)
            -- S'assurer que le heading est correct
            SetEntityHeading(c4ObjectOriginal, Config.Paletobank.c4DoorOriginalCoords.w)
            paletoC4DoorExists = true
        else
            -- La porte n'est pas à sa vraie position originale, la remettre

            SetEntityCoords(
                c4ObjectOriginal,
                Config.Paletobank.c4DoorOriginalCoords.x,
                Config.Paletobank.c4DoorOriginalCoords.y,
                Config.Paletobank.c4DoorOriginalCoords.z,
                false, false, false, true
            )

            SetEntityVisible(c4ObjectOriginal, true, false)
            SetEntityCollision(c4ObjectOriginal, true, false)
            SetEntityHeading(c4ObjectOriginal, Config.Paletobank.c4DoorOriginalCoords.w) -- Utiliser le heading du vec4
            paletoC4DoorExists = true

        end

    elseif c4ObjectHidden ~= 0 then
        -- La porte est cachée = elle a explosé, ne pas la remettre automatiquement
        paletoC4DoorExists = false

    elseif c4ObjectAnywhere ~= 0 then
        -- La porte est trouvée ailleurs, la remettre à sa position originale
        local anywhereCoords = GetEntityCoords(c4ObjectAnywhere)
        -- Remettre la porte à sa position originale
        SetEntityCoords(
            c4ObjectAnywhere,
            Config.Paletobank.c4DoorOriginalCoords.x,
            Config.Paletobank.c4DoorOriginalCoords.y,
            Config.Paletobank.c4DoorOriginalCoords.z,
            false, false, false, true
        )

        -- Remettre la porte visible et avec collisions
        SetEntityVisible(c4ObjectAnywhere, true, false)
        SetEntityCollision(c4ObjectAnywhere, true, false)

        -- Remettre le bon heading depuis le vec4
        SetEntityHeading(c4ObjectAnywhere, Config.Paletobank.c4DoorOriginalCoords.w)

        paletoC4DoorExists = true


    else
        -- Aucune porte trouvée
        paletoC4DoorExists = true -- Assumer qu'elle existe pour permettre l'interaction
    end

    -- Debug final de l'état de la porte C4
end)

-- Événement pour marquer les fils comme coupés
RegisterNetEvent('asc_robbery:paletoWiresCut')
AddEventHandler('asc_robbery:paletoWiresCut', function()
    exports['ps-dispatch']:PaletoBankRobbery()
    paletoWiresCut = true
    
end)

-- Événement pour synchroniser l'état de la porte C4
RegisterNetEvent('asc_robbery:paletoC4DoorState')
AddEventHandler('asc_robbery:paletoC4DoorState', function(exists)
    paletoC4DoorExists = exists
end)

-- Événement pour recevoir l'état synchronisé du serveur
RegisterNetEvent('asc_robbery:syncPaletoState')
AddEventHandler('asc_robbery:syncPaletoState', function(serverState)

    paletoWiresCut = serverState.wiresCut
    paletoC4DoorExists = serverState.c4DoorExists
    paletoMainDoorOpen = serverState.mainDoorOpen
    paletoThermiteUsed = serverState.thermiteUsed
    paletoC4Used = serverState.c4Used

    -- Synchroniser l'état des coffres percés depuis le nouvel état
    if serverState.vaultsDrilled then
        paletoDrilledVaults = {}
        for vaultIndex, isDrilled in pairs(serverState.vaultsDrilled) do
            if isDrilled then
                paletoDrilledVaults[vaultIndex] = true
            end
        end
    end

    local drilledList = {}
    for vaultIndex, isDrilled in pairs(paletoDrilledVaults) do
        if isDrilled then
            table.insert(drilledList, tostring(vaultIndex))
        end
    end

    -- Synchroniser l'état des portes
    if serverState.mainDoorOpen then
        -- Si la porte principale a été piratée avec la thermite, l'ouvrir
        openPaletoMainDoor()
    else
        -- Si la porte principale n'a pas été piratée, la fermer
        closePaletoMainDoor()
    end

    -- Synchroniser l'état physique de la porte C4
    if not serverState.c4DoorExists then
        -- La porte a été explosée, la déplacer vers la position cachée

        local c4DoorObject = GetClosestObjectOfType(
            Config.Paletobank.c4DoorCoords.x,
            Config.Paletobank.c4DoorCoords.y,
            Config.Paletobank.c4DoorCoords.z,
            10.0,
            GetHashKey(Config.Paletobank.c4DoorObject),
            false, false, false
        )

        if c4DoorObject ~= 0 then
            -- Déplacer la porte vers la position cachée
            SetEntityCoords(
                c4DoorObject,
                Config.Paletobank.c4DoorHiddenCoords.x,
                Config.Paletobank.c4DoorHiddenCoords.y,
                Config.Paletobank.c4DoorHiddenCoords.z,
                false, false, false, true
            )

            -- Rendre la porte invisible et sans collision
            SetEntityVisible(c4DoorObject, false, false)
            SetEntityCollision(c4DoorObject, false, false)

        else
        end
    else
        -- La porte existe, s'assurer qu'elle est à sa position originale

        local c4DoorObject = GetClosestObjectOfType(
            Config.Paletobank.c4DoorHiddenCoords.x,
            Config.Paletobank.c4DoorHiddenCoords.y,
            Config.Paletobank.c4DoorHiddenCoords.z,
            10.0,
            GetHashKey(Config.Paletobank.c4DoorObject),
            false, false, false
        )

        if c4DoorObject ~= 0 then
            -- Remettre la porte à sa position originale
            SetEntityCoords(
                c4DoorObject,
                Config.Paletobank.c4DoorOriginalCoords.x,
                Config.Paletobank.c4DoorOriginalCoords.y,
                Config.Paletobank.c4DoorOriginalCoords.z,
                false, false, false, true
            )

            -- Rendre la porte visible et avec collision
            SetEntityVisible(c4DoorObject, true, false)
            SetEntityCollision(c4DoorObject, true, false)
            SetEntityHeading(c4DoorObject, Config.Paletobank.c4DoorOriginalCoords.w)
        end
    end

    -- Les ox_target sont maintenant gérés par canInteract, pas besoin de mise à jour

end)

-- Événement pour marquer un coffre comme percé
RegisterNetEvent('asc_robbery:paletoVaultDrilled')
AddEventHandler('asc_robbery:paletoVaultDrilled', function(vaultIndex)
    paletoDrilledVaults[vaultIndex] = true
end)

-- Événement pour démarrer le minigame de perçage des coffres
RegisterNetEvent('asc_robbery:startPaletoDrillMinigame')
AddEventHandler('asc_robbery:startPaletoDrillMinigame', function(vaultIndex, vaultCoords)

    -- Démarrer le minigame de perçage (circuit breaker ou autre)
    local success = exports['glitch-minigames']:StartPlasmaDrilling(8) -- 6 circuits, difficulté 2

    if success then
        -- Minigame réussi
        TriggerServerEvent('asc_robbery:paletoDrillSuccess', vaultIndex)
    else
        -- Minigame échoué
        TriggerServerEvent('asc_robbery:paletoDrillFailed', vaultIndex)
        exports['ox_lib']:notify({
            title = 'Échec',
            description = 'Le perçage a échoué !',
            type = 'error'
        })
    end
end)

-- Événement pour ouvrir la porte principale de Paleto Bank
RegisterNetEvent('asc_robbery:openPaletoMainDoor', function()

    -- Mettre à jour l'état local
    paletoMainDoorOpen = true
    paletoThermiteUsed = true

    local doorObject = GetClosestObjectOfType(
        Config.Paletobank.mainDoorCoords.x,
        Config.Paletobank.mainDoorCoords.y,
        Config.Paletobank.mainDoorCoords.z,
        5.0,
        GetHashKey(Config.Paletobank.mainDoorObject),
        false, false, false
    )

    if doorObject ~= 0 then
        local initialHeading = GetEntityHeading(doorObject)

        -- Appliquer le heading d'ouverture
        SetEntityHeading(doorObject, Config.Paletobank.heading.open)

        -- Vérifier le heading après modification
        Wait(100)
        local finalHeading = GetEntityHeading(doorObject)

        exports['ox_lib']:notify({
            title = 'Succès',
            description = 'La porte de la banque Paleto est maintenant ouverte',
            type = 'success'
        })
    else
        exports['ox_lib']:notify({
            title = 'Erreur',
            description = 'Impossible de trouver la porte',
            type = 'error'
        })
    end

    -- Les ox_target sont maintenant gérés par canInteract, pas besoin de mise à jour
end)

-- Commande pour aller au premier coffre du vault
RegisterCommand('gotopaletoault', function()
    if Config and Config.Paletobank and Config.Paletobank.vaultRewardCoords and #Config.Paletobank.vaultRewardCoords > 0 then
        local coords = Config.Paletobank.vaultRewardCoords[1] -- Premier coffre
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)

        exports['ox_lib']:notify({
            title = 'Debug Paleto Vault',
            description = 'Téléporté au premier coffre du vault',
            type = 'success'
        })
    else
    end
end, false)



-- Commande pour vérifier l'état des fils et de la porte C4
RegisterCommand('paletostate', function()

    exports['ox_lib']:notify({
        title = 'État Paleto Bank',
        description = 'Fils: ' .. (paletoWiresCut and 'Coupés' or 'Intacts') .. ' | Porte C4: ' .. (paletoC4DoorExists and 'Existe' or 'Explosée'),
        type = 'info'
    })
end, false)

-- Commande pour forcer l'état des fils (debug)
RegisterCommand('forcewires', function()
    paletoWiresCut = true

    -- Les ox_target sont maintenant gérés par canInteract, pas besoin de mise à jour

    exports['ox_lib']:notify({
        title = 'Debug Paleto',
        description = 'Fils forcés comme coupés + ox_target mis à jour',
        type = 'success'
    })
end, false)

-- Commande pour tester l'événement paletoWiresCut
RegisterCommand('testwiresevent', function()
    -- Déclencher manuellement l'événement
    TriggerEvent('asc_robbery:paletoWiresCut')

    exports['ox_lib']:notify({
        title = 'Test Événement',
        description = 'Événement paletoWiresCut déclenché manuellement',
        type = 'info'
    })
end, false)

-- Événement pour démarrer la progress bar de coupure des fils
RegisterNetEvent('asc_robbery:startWireCuttingProgress')
AddEventHandler('asc_robbery:startWireCuttingProgress', function()

    -- Obtenir la position du joueur
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    -- Animation de coupure des fils
    local animDict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@"
    local animName = "machinic_loop_mechandplayer"

    -- Charger l'animation
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(100)
    end

    -- Démarrer l'animation
    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, -1, 1, 0, false, false, false)

    -- Progress bar avec ox_lib
    local success = exports['ox_lib']:progressBar({
        duration = 8000, -- 8 secondes
        label = 'Coupure des fils électriques...',
        useWhileDead = false,
        canCancel = false,
        disable = {
            car = true,
            move = true,
            combat = true,
            mouse = false
        },
        anim = {
            dict = animDict,
            clip = animName,
            flag = 1
        }
    })

    -- Arrêter l'animation
    ClearPedTasks(playerPed)
    RemoveAnimDict(animDict)

    if success then

        -- Finaliser la coupure des fils côté serveur
        TriggerServerEvent('asc_robbery:finalizePaletoWireCut')

        -- Effet sonore de coupure
        PlaySoundFrontend(-1, "PICK_UP", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)

        -- Effet visuel d'étincelles (optionnel)
        CreateThread(function()
            local sparkEffect = "scr_reconstructionaccident"
            local sparkName = "scr_sparking_generator"

            RequestNamedPtfxAsset(sparkEffect)
            while not HasNamedPtfxAssetLoaded(sparkEffect) do
                Wait(100)
            end

            -- Créer l'effet d'étincelles
            UseParticleFxAssetNextCall(sparkEffect)
            local ptfx = StartParticleFxLoopedAtCoord(sparkName, playerCoords.x, playerCoords.y, playerCoords.z + 1.0, 0.0, 0.0, 0.0, 0.5, false, false, false, false)

            -- Arrêter l'effet après 3 secondes
            Wait(3000)
            StopParticleFxLooped(ptfx, false)
            RemoveNamedPtfxAsset(sparkEffect)
        end)

    else

        exports['ox_lib']:notify({
            title = 'Annulé',
            description = 'Coupure des fils interrompue',
            type = 'error'
        })
    end
end)

-- Événement pour démarrer la progress bar de piratage système
RegisterNetEvent('asc_robbery:startSystemHackingProgress')
AddEventHandler('asc_robbery:startSystemHackingProgress', function()

    -- Obtenir le joueur
    local playerPed = PlayerPedId()

    -- Animation de typing/piratage système
    local typingDict = "anim@scripted@player@mission@tunf_bunk_ig3_nas_upload@"
    local typingName = "normal_typing"

    -- Charger l'animation de typing
    RequestAnimDict(typingDict)
    while not HasAnimDictLoaded(typingDict) do
        Wait(100)
    end

    -- Démarrer l'animation
    TaskPlayAnim(playerPed, typingDict, typingName, 8.0, -8.0, -1, 1, 0, false, false, false)

    -- Progress bar avec ox_lib
    local success = exports['ox_lib']:progressBar({
        duration = 12000, -- 12 secondes pour le piratage
        label = 'Piratage du système de sécurité...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
            mouse = false
        },
        anim = {
            dict = typingDict,
            clip = typingName,
            flag = 1
        }
    })

    -- Arrêter l'animation
    ClearPedTasks(playerPed)
    RemoveAnimDict(typingDict)

    if success then
        -- Finaliser l'ouverture de la porte côté serveur
        TriggerServerEvent('asc_robbery:finalizePaletoMainDoorHack')

        -- Effet sonore de succès
        PlaySoundFrontend(-1, "HACKING_SUCCESS", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)

        -- Effet visuel de piratage réussi (écran qui clignote)
        CreateThread(function()
            -- Effet de flash vert pour indiquer le succès
            for _ = 1, 3 do
                SetFlash(0, 0, 500, 7000, 500)
                Wait(200)
            end
        end)

    else

        -- Effet sonore d'échec
        PlaySoundFrontend(-1, "HACKING_FAILURE", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)

        exports['ox_lib']:notify({
            title = 'Annulé',
            description = 'Piratage du système interrompu',
            type = 'error'
        })
    end
end)

-- Variable pour tracker l'objet C4 placé
local placedC4Object = nil

-- Événement pour démarrer la progress bar de placement C4
RegisterNetEvent('asc_robbery:startC4PlacementProgress')
AddEventHandler('asc_robbery:startC4PlacementProgress', function()

    -- Obtenir le joueur et les coordonnées de la porte C4
    local playerPed = PlayerPedId()
    local c4DoorCoords = Config.Paletobank.c4DoorCoords

    -- Animation de placement d'objet (style installation/réparation)
    local animDict = "mini@repair"
    local animName = "fixing_a_ped"

    -- Charger l'animation
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(100)
    end

    -- Charger le modèle de C4
    local c4Model = GetHashKey("prop_c4_final")
    RequestModel(c4Model)
    while not HasModelLoaded(c4Model) do
        Wait(100)
    end

    -- Créer l'objet C4 et l'attacher à la main du joueur
    placedC4Object = CreateObject(c4Model, c4DoorCoords.x, c4DoorCoords.y, c4DoorCoords.z, true, true, true)
    local handBone = GetPedBoneIndex(playerPed, 28422) -- Main droite
    AttachEntityToEntity(placedC4Object, playerPed, handBone, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, true, true, false, true, 1, true)

    -- Démarrer l'animation de manipulation/installation
    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, -1, 1, 0, false, false, false)

    -- Progress bar avec ox_lib
    local success = exports['ox_lib']:progressBar({
        duration = 10000, -- 10 secondes pour placer le C4
        label = 'Placement du C4 sur la porte...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
            mouse = false
        },
        anim = {
            dict = animDict,
            clip = animName,
            flag = 1
        }
    })

    -- Arrêter l'animation
    ClearPedTasks(playerPed)
    RemoveAnimDict(animDict)

    if success then

        -- Détacher le C4 de la main et le placer sur la porte
        DetachEntity(placedC4Object, true, false)
        SetEntityCoords(placedC4Object, c4DoorCoords.x, c4DoorCoords.y, c4DoorCoords.z, false, false, false, true)
        SetEntityHeading(placedC4Object, c4DoorCoords.w or 0.0)

        -- Rendre le C4 statique et visible
        SetEntityCollision(placedC4Object, false, false)
        FreezeEntityPosition(placedC4Object, true)

        -- Finaliser le placement côté serveur
        TriggerServerEvent('asc_robbery:finalizeC4Explosion')

        -- Effet sonore de placement
        PlaySoundFrontend(-1, "PICK_UP", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)

    else

        -- Supprimer le C4 si annulé
        if placedC4Object then
            DeleteObject(placedC4Object)
            placedC4Object = nil
        end

        exports['ox_lib']:notify({
            title = 'Annulé',
            description = 'Placement du C4 interrompu',
            type = 'error'
        })
    end

    -- Nettoyer le modèle
    SetModelAsNoLongerNeeded(c4Model)
end)

-- Événement pour notification que le C4 a été placé (pour tous les joueurs)
RegisterNetEvent('asc_robbery:c4PlacedNotification')
AddEventHandler('asc_robbery:c4PlacedNotification', function()
    -- Mettre à jour l'état local
    paletoC4Used = true

    exports['ox_lib']:notify({
        title = 'C4 Détecté',
        description = 'Un C4 a été placé sur la porte !',
        type = 'warning'
    })
end)

-- Modifier l'événement d'explosion pour supprimer le C4 placé
RegisterNetEvent('asc_robbery:explodePaletoC4Door')
AddEventHandler('asc_robbery:explodePaletoC4Door', function()

    -- Supprimer l'objet C4 placé lors de l'explosion
    if placedC4Object then
        DeleteObject(placedC4Object)
        placedC4Object = nil
    end

    -- Déplacer la porte vers la position cachée
    local c4DoorObject = GetClosestObjectOfType(
        Config.Paletobank.c4DoorCoords.x,
        Config.Paletobank.c4DoorCoords.y,
        Config.Paletobank.c4DoorCoords.z,
        10.0,
        GetHashKey(Config.Paletobank.c4DoorObject),
        false, false, false
    )

    if c4DoorObject ~= 0 then

        -- Déplacer la porte vers la position cachée
        SetEntityCoords(
            c4DoorObject,
            Config.Paletobank.c4DoorHiddenCoords.x,
            Config.Paletobank.c4DoorHiddenCoords.y,
            Config.Paletobank.c4DoorHiddenCoords.z,
            false, false, false, true
        )

        -- Rendre la porte invisible et sans collision
        SetEntityVisible(c4DoorObject, false, false)
        SetEntityCollision(c4DoorObject, false, false)

        -- Mettre à jour l'état local
        paletoC4DoorExists = false
    else
    end

    -- Effets d'explosion
    local explosionCoords = Config.Paletobank.c4DoorCoords
    AddExplosion(explosionCoords.x, explosionCoords.y, explosionCoords.z, 2, 10.0, true, false, 1.0)

    -- Notification d'explosion
    exports['ox_lib']:notify({
        title = 'Explosion !',
        description = 'La porte a explosé !',
        type = 'warning'
    })

end)

-- Commandes de test supprimées pour éviter les doublons d'ox_target

-- Commande pour tester la synchronisation complète
RegisterCommand('testsync', function()
    -- Demander la synchronisation au serveur
    TriggerServerEvent('asc_robbery:requestPaletoSync')

    exports['ox_lib']:notify({
        title = 'Test Synchronisation',
        description = 'Synchronisation demandée - Vérifiez la console',
        type = 'info'
    })
end, false)

-- Commande updatetargets supprimée pour éviter les doublons

-- Zone de détection pour Paleto Bank (pour synchronisation automatique)
CreateThread(function()
    local paletoZoneCoords = vector3(-104.71, 6472.03, 31.63) -- Coordonnées de Paleto Bank
    local paletoZoneRadius = 50.0 -- Rayon de détection

    while true do
        Wait(2000) -- Vérifier toutes les 2 secondes

        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local distance = #(playerCoords - paletoZoneCoords)

        local isInZone = distance <= paletoZoneRadius

        -- Si le joueur vient d'entrer dans la zone
        if isInZone and not playerInPaletoZone then
            playerInPaletoZone = true

            -- Demander la synchronisation au serveur
            TriggerServerEvent('asc_robbery:requestPaletoSync')

        -- Si le joueur vient de sortir de la zone
        elseif not isInZone and playerInPaletoZone then
            playerInPaletoZone = false
        end
    end
end)

-- Commande pour tester directement l'événement de synchronisation
RegisterCommand('testsyncevent', function()

    -- Créer un état de test
    local testState = {
        wiresCut = true,
        c4DoorExists = true,
        mainDoorOpen = false,
        vaultsDrilled = {}
    }

    -- Déclencher manuellement l'événement de synchronisation
    TriggerEvent('asc_robbery:syncPaletoState', testState)

    exports['ox_lib']:notify({
        title = 'Test Sync Event',
        description = 'Événement de synchronisation déclenché manuellement',
        type = 'info'
    })
end, false)

-- Commandes de test supprimées pour éviter les doublons d'ox_target
