<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crafting UI</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="crafting-container" class="hidden">
        <div class="crafting-header">
            <h1 id="craftingTitle">Crafting</h1>
            <button id="closeButton" class="close-button">Fermer</button>
        </div>
        
        <div class="crafting-content">
            <div class="crafting-main">
                <div class="crafting-categories">
                    <!-- Les catégories seront ajoutées ici dynamiquement -->
                </div>

                <div class="crafting-recipes">
                    <!-- Les recettes seront ajoutées ici dynamiquement -->
                </div>
            </div>

            <div class="crafting-info">
                <h2>Informations <i class="fas fa-info-circle"></i></h2>
                <div class="info-content">
                    <div class="selected-recipe-info hidden">
                        <h3 id="selectedRecipeName">Recette sélectionnée</h3>
                        <p id="selectedRecipeDescription">Description de la recette</p>
                        <div class="recipe-ingredients">
                            <h4>Ingrédients requis:</h4>
                            <div id="ingredientsList">
                                <!-- Les ingrédients seront ajoutés ici dynamiquement -->
                            </div>
                        </div>
                        <div class="recipe-result">
                            <h4>Résultat:</h4>
                            <p id="recipeResult">Item crafté</p>
                        </div>
                        <div class="crafting-time">
                            <h4>Temps de craft:</h4>
                            <p id="craftingTime">0 secondes</p>
                        </div>
                        <div class="crafting-quantity">
                            <h4>Quantité à crafter:</h4>
                            <div class="crafting-quantity-controls">
                                <button id="quantityMinus" type="button" class="quantity-btn">-</button>
                                <input type="number" id="craftQuantity" min="1" value="1" style="width: 70px;">
                                <button id="quantityPlus" type="button" class="quantity-btn">+</button>
                            </div>
                            <span id="quantityMaxInfo"></span>
                        </div>
                        <button id="craftButton" class="craft-button">
                            <i class="fas fa-hammer"></i> Crafter
                        </button>
                    </div>
                    <div class="no-recipe-selected">
                        <p>Sélectionnez une recette pour voir les détails</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 