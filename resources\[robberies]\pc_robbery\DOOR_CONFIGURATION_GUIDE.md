# Guide de Configuration des Portes - ASC Robbery

## 🎯 Objectif
Configurer correctement les portes ox_doorlock pour que les portes de banque se barrent automatiquement lors des resets.

## 📋 Étapes de Configuration

### **Étape 1 : Diagnostic Initial**
```bash
# Console serveur ou en jeu (admin)
/testBankDoors
```

**Résultats possibles :**
- ✅ **Porte trouvée** : La configuration fonctionne déjà
- ❌ **Aucune porte trouvée** : Vous devez créer les portes ox_doorlock

### **Étape 2 : Génération des Portes**
```bash
# Console serveur ou en jeu (admin)
/createBankDoors
```

Cette commande génère les requêtes SQL nécessaires.

### **Étape 3 : Création en Base de Données**

#### **Option A : Exécution Manuelle (Recommandée)**
1. Copiez les requêtes SQL générées
2. Exécutez-les dans votre base de données MySQL
3. Notez les IDs des portes créées

#### **Option B : Exécution Automatique**
Décommentez les lignes dans `create_bank_doors.lua` :
```lua
-- MySQL.insert('INSERT INTO `ox_doorlock` (`name`, `data`) VALUES (?, ?)', {
--     doorName,
--     json.encode(doorData)
-- }, function(insertId)
--     if insertId then
--         print('^2[ASC_ROBBERY]^7 Porte créée avec succès pour la banque ' .. bankId .. ' (ID: ' .. insertId .. ')')
--         Config.Banks[bankId].doorId = insertId
--     end
-- end)
```

### **Étape 4 : Configuration des IDs**

Mettez à jour votre `config.lua` avec les IDs des portes créées :

```lua
Config.Banks = {
    [1] = {
        -- ... autres configurations ...
        doorId = 10, -- ID réel de la porte créée en base
        doorName = "fleeca_vault_1", -- Nom de la porte
        -- ... reste de la configuration ...
    }
}
```

### **Étape 5 : Test Final**
```bash
# Redémarrez les scripts
restart ox_doorlock
restart asc_robbery

# Testez la configuration
/testBankDoors

# Testez la réinitialisation
/resetrobbery
```

## 🔧 Méthodes de Recherche des Portes

Le système utilise 3 méthodes dans cet ordre :

### **1. Par ID (Priorité 1)**
```lua
doorId = 10 -- ID spécifique de la porte ox_doorlock
```

### **2. Par Nom (Priorité 2)**
```lua
doorName = "fleeca_vault_1" -- Nom de la porte ox_doorlock
```

### **3. Par Coordonnées (Fallback)**
```lua
vaultDoorCoords = vector3(148.026, -1044.364, 29.507)
-- Recherche automatique dans un rayon de 5 mètres
```

## 🛠️ Dépannage

### **Problème : Aucune porte trouvée**
**Solutions :**
1. Vérifiez que ox_doorlock est démarré
2. Créez les portes avec `/createBankDoors`
3. Vérifiez les coordonnées dans la configuration

### **Problème : Porte trouvée mais ne se barre pas**
**Solutions :**
1. Vérifiez les permissions ox_doorlock
2. Testez manuellement : `exports.ox_doorlock:setDoorState(ID, 1)`
3. Vérifiez les logs pour les erreurs

### **Problème : Mauvaise porte trouvée**
**Solutions :**
1. Utilisez `doorId` pour spécifier l'ID exact
2. Utilisez `doorName` pour spécifier le nom exact
3. Ajustez les `vaultDoorCoords` pour être plus précis

## 📊 Exemple de Configuration Complète

```lua
Config.Banks = {
    [1] = {  
        pedCoords = vec4(146.76, -1043.75, 29.37, 176.14),
        thermiteHack = vector3(148.***********, -1046.**********, 29.************),
        vaultDoorCoords = vector3(148.***********, -1044.**********, 29.************),
        doorId = 10, -- ID de la porte ox_doorlock (après création)
        doorName = "fleeca_vault_1", -- Nom de la porte ox_doorlock
        minPolice = 1,
        cooldown = 30,
        securitySystemCoords = vector3(147.***********, -1046.**********, 29.********),
        policeReset = vector3(147.***********, -1046.**********, 29.********),
        object = 'v_ilev_gb_vauldr',
        heading = {
            closed = 250.0,
            open = 160.0
        },
        vaultCoords = {
            vector3(149.***********, -1044.**********, 29.************),
            vector3(151.***********, -1046.**********, 29.************),
            -- ... autres coffres ...
        },
    },
    -- Ajoutez d'autres banques avec des IDs et noms uniques
}
```

## 🚀 Commandes Utiles

```bash
# Diagnostic des portes
/testBankDoors

# Génération des requêtes SQL
/createBankDoors

# Test de réinitialisation
/resetrobbery

# Statut des cooldowns
/robberyStatus
```

## ✅ Vérification Finale

Après configuration, vous devriez voir :
- ✅ `/testBankDoors` trouve les portes pour chaque banque
- ✅ `/resetrobbery` barre les portes automatiquement
- ✅ Les portes se ferment à la fin des cooldowns
- ✅ Aucune erreur dans les logs

## 📝 Notes Importantes

1. **Sauvegardez** votre base de données avant de créer les portes
2. **Testez** sur un serveur de développement d'abord
3. **Documentez** les IDs des portes créées
4. **Redémarrez** ox_doorlock après création des portes
5. **Vérifiez** les permissions pour les commandes admin
