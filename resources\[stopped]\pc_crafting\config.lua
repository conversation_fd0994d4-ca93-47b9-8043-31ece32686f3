
Config = {}

-- Configuration générale
Config.Debug = false

-- Points de crafting
Config.CraftingStations = {
    -- Exemple de station avec prop
    ["burger_station"] = {
        label = "Table Burger",
        coords = vector3(123.4, 456.7, 78.9),
        propModel = 'prop_tool_bench02', -- modèle du prop (optionnel)
        propCoords = vector4(123.2, 456.6, 78.9, 180.0), -- position et heading du prop (optionnel)
    },
    -- Autres stations...
    -- ['workbench'] = {
    --     label = 'Établi de travail',
    --     coords = vec3(255.31, 217.16, 101.68), -- À modifier selon vos besoins
    --     blip = {
    --         enabled = true,
    --         sprite = 566,
    --         color = 5,
    --         scale = 0.8,
    --         label = 'Établi de travail'
    --     },
    --     categories = {
    --         'weapons',
    --         'tools',
    --         'food',
    --         'misc'
    --     }
    -- },
    ['kitchen'] = {
        label = 'Cuisine',
        coords = vec3(255.31, 217.16, 101.68),
        propModel = 'prop_tool_bench02',
        propCoords = vector4(255.31, 217.16, 100.68, 0.0),
        blip = {
            enabled = true,
            sprite = 567,
            color = 2,
            scale = 0.8,
            label = 'Cuisine'
        },
        categories = {
            'food',
            'drinks',
            'medicine',
            'autre'
        }
    }
}

-- Recettes de crafting
Config.Recipes = {
    ['burger'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },
    ['burger1'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger2'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger3'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger4'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger5'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger6'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger7'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger8'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger9'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger11'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger12'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },

    ['burger13'] = {
        category = 'food',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['water'] = 2,
            ['burger'] = 1
        },
        result = {
            item = 'burger',
            amount = 1
        },
        requiredStation = 'kitchen'
    },
    
    -- Armes
    ['weapon_pistol'] = {
        category = 'weapons',
        time = 5, -- Temps de craft en secondes
        ingredients = {
            ['steel'] = 5,
            ['wood'] = 2,
            ['screw'] = 10
        },
        result = {
            item = 'weapon_pistol',
            amount = 1
        },
        requiredStation = 'workbench'
    },
    
    -- Outils
    ['lockpick'] = {
        category = 'tools',
        time = 5,
        ingredients = {
            ['steel'] = 2,
            ['screw'] = 3
        },
        result = {
            item = 'lockpick',
            amount = 1
        },
        requiredStation = 'workbench'
    },
    
    -- Nourriture
    ['sandwich'] = {
        category = 'food',
        time = 5,
        ingredients = {
            ['bread'] = 2,
            ['ham'] = 1,
            ['lettuce'] = 1
        },
        result = {
            item = 'sandwich',
            amount = 1
        },
        requiredStation = 'kitchen'
    },
    
    -- Boissons
    ['coffee'] = {
        category = 'drinks',
        time = 8,
        ingredients = {
            ['coffee_bean'] = 1,
            ['water'] = 1
        },
        result = {
            item = 'coffee',
            amount = 1
        },
        requiredStation = 'kitchen'
    }
}

-- Messages
Config.Messages = {
    ['crafting_started'] = 'Crafting en cours...',
    ['crafting_success'] = 'Crafting terminé avec succès!',
    ['crafting_failed'] = 'Crafting échoué - ingrédients manquants',
    ['not_enough_ingredients'] = 'Vous n\'avez pas assez d\'ingrédients',
    ['wrong_station'] = 'Vous devez être à la bonne station pour cette recette',
    ['already_crafting'] = 'Vous êtes déjà en train de crafter'
} 
