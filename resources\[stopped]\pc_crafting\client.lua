
local QBCore = exports['qb-core']:GetCoreObject()
local isUIOpen = false
local currentStation = nil
local isCrafting = false
local craftingBlips = {}
local spawnedClientProps = {}
local propZoneRadius = 30.0
local propLoadRadius = 100.0 -- Rayon de chargement des props
local stationsConfig = {} -- Configuration des stations reçue du serveur
local playerPosition = vector3(0, 0, 0)
local lastPositionCheck = 0

-- Importer ox_lib
local lib = exports.ox_lib

-- Fonction pour vérifier la distance entre deux points
local function GetDistance(pos1, pos2)
    return #(pos1 - pos2)
end

-- Fonction pour créer un prop spécifique
local function CreateStationProp(station, data)
    if not data.propModel or not data.propCoords then
        return false
    end

    local model = data.propModel
    local coords = data.propCoords

    print('^3[PC_CRAFTING CLIENT]^7 Création du prop: ' .. model .. ' pour la station: ' .. station)

    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(10)
    end

    local obj = CreateObject(model, coords.x, coords.y, coords.z, false, true, true)

    if obj ~= 0 then
        SetEntityHeading(obj, coords.w or 0.0)
        FreezeEntityPosition(obj, true)
        SetEntityAsMissionEntity(obj, true, true)
        spawnedClientProps[station] = obj
        print('^2[PC_CRAFTING CLIENT]^7 ✓ Prop créé avec succès pour la station: ' .. station)
        return true
    else
        print('^1[PC_CRAFTING CLIENT]^7 ✗ Échec de création du prop pour la station: ' .. station)
        return false
    end
end

-- Fonction pour supprimer un prop spécifique
local function DeleteStationProp(station)
    if spawnedClientProps[station] then
        local obj = spawnedClientProps[station]
        if DoesEntityExist(obj) then
            DeleteObject(obj)
            DeleteEntity(obj)
        end
        spawnedClientProps[station] = nil
        print('^3[PC_CRAFTING CLIENT]^7 Prop supprimé pour la station: ' .. station)
    end
end

-- Fonction pour vérifier la proximité et gérer les props
local function CheckProximityAndManageProps()
    local currentTime = GetGameTimer()

    -- Vérifier seulement toutes les 2 secondes pour optimiser
    if currentTime - lastPositionCheck < 2000 then
        return
    end

    lastPositionCheck = currentTime
    playerPosition = GetEntityCoords(PlayerPedId())

    for station, data in pairs(stationsConfig) do
        if data.propModel and data.propCoords then
            local propCoords = vector3(data.propCoords.x, data.propCoords.y, data.propCoords.z)
            local distance = GetDistance(playerPosition, propCoords)

            -- Si le joueur est proche et le prop n'existe pas, le créer
            if distance <= propLoadRadius and not spawnedClientProps[station] then
                CreateStationProp(station, data)

            -- Si le joueur est loin et le prop existe, le supprimer
            elseif distance > propLoadRadius and spawnedClientProps[station] then
                DeleteStationProp(station)
            end
        end
    end
end

-- Fonction pour créer les blips
local function CreateCraftingBlips()
    for stationName, stationData in pairs(Config.CraftingStations) do
        if stationData.blip and stationData.blip.enabled then
            local blip = AddBlipForCoord(stationData.coords.x, stationData.coords.y, stationData.coords.z)
            SetBlipSprite(blip, stationData.blip.sprite)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, stationData.blip.scale)
            SetBlipColour(blip, stationData.blip.color)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(stationData.blip.label)
            EndTextCommandSetBlipName(blip)
            
            craftingBlips[stationName] = blip
        end
    end
end

-- Fonction pour supprimer les blips
local function DeleteCraftingBlips()
    for _, blip in pairs(craftingBlips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    craftingBlips = {}
end

-- Fonction pour créer les points d'interaction
local function CreateCraftingInteractions()
    for stationName, stationData in pairs(Config.CraftingStations) do
        exports.ox_target:addBoxZone({
            coords = stationData.coords,
            size = vec3(1.0, 1.0, 2.0),
            rotation = 0,
            debug = Config.Debug,
            options = {
                {
                    name = 'crafting_station_' .. stationName,
                    icon = 'fas fa-hammer',
                    label = 'Accéder à ' .. stationData.label,
                    distance = 2.5,
                    onSelect = function()
                        TriggerEvent('pc_crafting:client:openCrafting', stationName)
                    end
                }
            }
        })
    end
end

-- Fonction pour ouvrir l'interface de crafting
RegisterNetEvent('pc_crafting:client:openCrafting', function(stationName)
    if isUIOpen then return end
    
    local stationData = Config.CraftingStations[stationName]
    if not stationData then return end
    
    -- Vérifier la distance
    local playerCoords = GetEntityCoords(PlayerPedId())
    local distance = #(playerCoords - stationData.coords)
    if distance > 3.0 then
        QBCore.Functions.Notify('Vous devez être plus proche de la station', 'error')
        return
    end
    
    currentStation = stationName
    isUIOpen = true
    
    -- Préparer les données pour l'UI
    local recipes = {}
    for recipeName, recipeData in pairs(Config.Recipes) do
        if recipeData.requiredStation == stationName then
            -- Vérifier les ingrédients du joueur
            local canCraft = true
            local playerIngredients = {}
            
            for ingredient, amount in pairs(recipeData.ingredients) do
                local playerAmount = exports.ox_inventory:Search('count', ingredient) or 0
                playerIngredients[ingredient] = playerAmount
                
                if playerAmount < amount then
                    canCraft = false
                end
            end
            
            -- Récupérer les informations des ingrédients depuis ox_inventory
            local ingredientInfo = {}
            for ingredient, amount in pairs(recipeData.ingredients) do
                local itemData = exports.ox_inventory:Items(ingredient)
                ingredientInfo[ingredient] = {
                    label = itemData and itemData.label or ingredient,
                    description = itemData and itemData.description or ''
                }
            end
            
            -- Récupérer les informations de l'item résultant depuis ox_inventory
            local resultItem = exports.ox_inventory:Items(recipeData.result.item)
            local resultLabel = resultItem and resultItem.label or recipeData.result.item
            local resultDescription = resultItem and resultItem.description or nil
            
            table.insert(recipes, {
                name = recipeName,
                label = resultLabel,
                description = resultDescription,
                category = recipeData.category,
                time = recipeData.time,
                ingredients = recipeData.ingredients,
                ingredientInfo = ingredientInfo,
                playerIngredients = playerIngredients,
                canCraft = canCraft,
                result = recipeData.result
            })
        end
    end
    
    -- Envoyer les données à l'UI
    SendNUIMessage({
        action = 'openCrafting',
        stationName = stationData.label,
        recipes = recipes
    })
    
    SetNuiFocus(true, true)
end)

-- Fonction pour fermer l'interface
RegisterNUICallback('closeCrafting', function(data, cb)
    isUIOpen = false
    currentStation = nil
    SetNuiFocus(false, false)
    cb('ok')
end)

-- Fonction pour démarrer le crafting
RegisterNUICallback('startCrafting', function(data, cb)
    if isCrafting then
        cb({success = false, message = Config.Messages['already_crafting']})
        return
    end
    
    local recipeName = data.recipeName
    local recipe = Config.Recipes[recipeName]
    local quantity = tonumber(data.quantity) or 1
    if quantity < 1 then quantity = 1 end
    
    if not recipe then
        cb({success = false, message = 'Recette invalide'})
        return
    end
    
    -- Vérifier la station
    if recipe.requiredStation ~= currentStation then
        cb({success = false, message = Config.Messages['wrong_station']})
        return
    end
    
    -- Vérifier les ingrédients pour la quantité totale
    local hasIngredients = true
    for ingredient, amount in pairs(recipe.ingredients) do
        local playerAmount = exports.ox_inventory:Search('count', ingredient) or 0
        if playerAmount < amount * quantity then
            hasIngredients = false
            break
        end
    end
    
    if not hasIngredients then
        cb({success = false, message = Config.Messages['not_enough_ingredients']})
        return
    end
    
    -- Démarrer le crafting
    isCrafting = true
    cb({success = true})
    
    local playerPed = PlayerPedId()
    for i = 1, quantity do
        TaskStartScenarioInPlace(playerPed, "PROP_HUMAN_BUM_BIN", 0, true)
        local duration = tonumber(recipe.time) and tonumber(recipe.time) * 1000 or 3000
        if not duration or duration <= 0 then duration = 3000 end
        local progressLabel = recipeName
        local resultItem = exports.ox_inventory:Items(recipe.result.item)
        if resultItem and resultItem.label and resultItem.label ~= "" then
            progressLabel = resultItem.label
        end
        if not progressLabel or progressLabel == "" then
            progressLabel = recipe.result.item or recipeName or "Crafting"
        end
        print("DEBUG progressBar", duration, progressLabel, "["..i.."/"..quantity.."]")
        local finished = false
        local done = false
        QBCore.Functions.Progressbar("crafting_item", "Crafting " .. tostring(progressLabel) .. " ["..i.."/"..quantity.."]", duration, false, true, {
            disableMovement = true,
            disableCarMovement = true,
            disableMouse = false,
            disableCombat = true,
        }, {}, {}, {}, function() -- Done
            ClearPedTasks(playerPed)
            isCrafting = false
            finished = true
        end, function() -- Cancel
            ClearPedTasks(playerPed)
            isCrafting = false
            finished = false
        end)
        -- Attendre la fin de la progressbar
        while isCrafting do Wait(100) end
        if not finished then
            QBCore.Functions.Notify('Crafting annulé', 'error')
            break
        end
        -- Déclencher l'événement serveur pour le crafting (un par item)
        TriggerServerEvent('pc_crafting:server:craftItem', recipeName)
        Wait(250) -- Petite pause pour éviter les spams
    end
end)

-- Événement pour recevoir le résultat du crafting
RegisterNetEvent('pc_crafting:client:craftingResult', function(success, message)
    if success then
        QBCore.Functions.Notify(message, 'success')
    else
        QBCore.Functions.Notify(message, 'error')
    end
end)

-- Commande pour ouvrir l'interface de crafting (pour les tests)
RegisterCommand('crafting', function()
    if isUIOpen then
        TriggerEvent('pc_crafting:client:closeCrafting')
    else
        -- Ouvrir le premier station disponible pour les tests
        local firstStation = next(Config.CraftingStations)
        if firstStation then
            TriggerEvent('pc_crafting:client:openCrafting', firstStation)
        end
    end
end, false)

-- Initialisation
CreateThread(function()
    Wait(1000)
    CreateCraftingBlips()
    CreateCraftingInteractions()
end)

-- Demande la synchro des props à l'init
CreateThread(function()
    Wait(1000)
    TriggerServerEvent('pc_crafting:server:requestProps')
end)

-- Fonction pour charger un prop de station
local function loadStationProp(station, data)
    if spawnedClientProps[station] then return end
    TriggerServerEvent('pc_crafting:server:requestProps', station)
end

-- Fonction pour supprimer un prop de station
local function unloadStationProp(station)
    if spawnedClientProps[station] and DoesEntityExist(spawnedClientProps[station]) then
        DeleteEntity(spawnedClientProps[station])
    end
    spawnedClientProps[station] = nil
end

-- Handler pour recevoir le prop d'une station
RegisterNetEvent('pc_crafting:client:syncProps', function(props)
    for _, data in ipairs(props) do
        local station = data.station
        if not spawnedClientProps[station] then
            local model = data.model
            local coords = data.coords
            local heading = data.heading
            RequestModel(model)
            while not HasModelLoaded(model) do Wait(10) end
            local obj = CreateObjectNoOffset(model, coords.x, coords.y, coords.z, false, false, false)
            SetEntityHeading(obj, heading)
            FreezeEntityPosition(obj, true)
            SetEntityAsMissionEntity(obj, true, true)
            spawnedClientProps[station] = obj
        end
    end
end)

-- Thread de surveillance des zones de props
CreateThread(function()
    Wait(1000)
    while true do
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        for station, data in pairs(Config.CraftingStations) do
            if data.propModel and data.propCoords then
                local dist = #(playerCoords - vector3(data.propCoords.x, data.propCoords.y, data.propCoords.z))
                if dist < propZoneRadius then
                    if not spawnedClientProps[station] then
                        loadStationProp(station, data)
                    end
                else
                    if spawnedClientProps[station] then
                        unloadStationProp(station)
                    end
                end
            end
        end
        Wait(1000)
    end
end)

-- Nettoyage à la déconnexion ou stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        for _, obj in pairs(spawnedClientProps) do
            if DoesEntityExist(obj) then DeleteEntity(obj) end
        end
        spawnedClientProps = {}
    end
end) 

RegisterNetEvent('pc_crafting:createStationProps')
AddEventHandler('pc_crafting:createStationProps', function(stations)
    print('^2[PC_CRAFTING CLIENT]^7 Réception de la configuration des stations')

    -- Stocker la configuration des stations
    stationsConfig = stations

    -- Vérifier immédiatement la proximité pour charger les props proches
    CheckProximityAndManageProps()

    print('^2[PC_CRAFTING CLIENT]^7 Configuration des stations reçue et vérification de proximité effectuée')
end)

-- Thread pour vérifier la proximité des props en continu
CreateThread(function()
    while true do
        if next(stationsConfig) then -- Seulement si on a reçu la config
            CheckProximityAndManageProps()
        end
        Wait(2000) -- Vérifier toutes les 2 secondes
    end
end)

-- Événement quand un joueur se connecte
AddEventHandler('onClientResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        print('^2[PC_CRAFTING CLIENT]^7 Script démarré - demande de configuration des stations')
        -- Demander la configuration au serveur
        TriggerServerEvent('pc_crafting:requestStationsConfig')
    end
end)
