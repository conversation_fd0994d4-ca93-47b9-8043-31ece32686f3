
local QBCore = exports['qb-core']:GetCoreObject()

-- Fonction pour vérifier si un joueur a les ingrédients nécessaires
local function HasIngredients(source, ingredients)
    for ingredient, amount in pairs(ingredients) do
        local playerAmount = exports.ox_inventory:Search(source, 'count', ingredient) or 0
        if playerAmount < amount then
            return false
        end
    end
    
    return true
end

-- Fonction pour retirer les ingrédients
local function RemoveIngredients(source, ingredients)
    for ingredient, amount in pairs(ingredients) do
        local success = exports.ox_inventory:RemoveItem(source, ingredient, amount)
        if not success then
            return false
        end
    end
    
    return true
end

-- Fonction pour donner l'item crafté
local function GiveCraftedItem(source, result)
    local success = exports.ox_inventory:AddItem(source, result.item, result.amount)
    if success then
        return true
    end
    
    return false
end

-- Événement pour crafter un item
RegisterNetEvent('pc_crafting:server:craftItem', function(recipeName)
    local source = source
    local recipe = Config.Recipes[recipeName]
    local quantity = 1
    if type(recipeName) == 'table' and recipeName.recipeName then
        recipe = Config.Recipes[recipeName.recipeName]
        quantity = tonumber(recipeName.quantity) or 1
    end
    if not recipe then
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, 'Recette invalide')
        return
    end
    if quantity < 1 then quantity = 1 end
    -- Vérifier les ingrédients pour la quantité totale
    local totalIngredients = {}
    for ingredient, amount in pairs(recipe.ingredients) do
        totalIngredients[ingredient] = amount * quantity
    end
    if not HasIngredients(source, totalIngredients) then
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, Config.Messages['not_enough_ingredients'])
        return
    end
    -- Retirer les ingrédients
    if not RemoveIngredients(source, totalIngredients) then
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, 'Erreur lors de la suppression des ingrédients')
        return
    end
    -- Donner la quantité d'items craftés
    local result = recipe.result
    local success = exports.ox_inventory:AddItem(source, result.item, result.amount * quantity)
    if not success then
        -- Remettre les ingrédients si l'ajout échoue
        for ingredient, amount in pairs(totalIngredients) do
            exports.ox_inventory:AddItem(source, ingredient, amount)
        end
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, 'Inventaire plein')
        return
    end
    -- Succès
    TriggerClientEvent('pc_crafting:client:craftingResult', source, true, Config.Messages['crafting_success'])
end)

-- Commande pour donner des ingrédients de test (admin seulement)
QBCore.Commands.Add('givecrafting', 'Donner des ingrédients de test pour le crafting', {{name = 'playerid', help = 'ID du joueur'}}, true, function(source, args)
    local targetId = tonumber(args[1])
    if not targetId then
        TriggerClientEvent('QBCore:Notify', source, 'ID de joueur invalide', 'error')
        return
    end
    
    -- Donner des ingrédients de test
    local testIngredients = {
        'steel',
        'wood',
        'screw',
        'bread',
        'ham',
        'lettuce',
        'coffee_bean',
        'water',
        'burger'
    }
    
    for _, item in ipairs(testIngredients) do
        exports.ox_inventory:AddItem(targetId, item, 10)
    end
    
    TriggerClientEvent('QBCore:Notify', source, 'Ingrédients de test donnés au joueur', 'success')
    TriggerClientEvent('QBCore:Notify', targetId, 'Vous avez reçu des ingrédients de test pour le crafting', 'success')
end, 'admin') 

-- Créer les props au démarrage du script
AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        print('^2[PC_CRAFTING]^7 Démarrage du script - création des props...')
        -- Attendre un peu que les clients soient prêts
        Wait(2000)
        -- Demander aux clients de créer les objets
        TriggerClientEvent('pc_crafting:createStationProps', -1, Config.CraftingStations)
        print('^2[PC_CRAFTING]^7 Événement de création des props envoyé aux clients')
    end
end)
