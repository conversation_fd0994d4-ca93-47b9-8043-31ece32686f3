local QBCore = exports['qb-core']:GetCoreObject()

-- Fonction pour vérifier si un joueur a les ingrédients nécessaires
local function HasIngredients(source, ingredients)
    for ingredient, amount in pairs(ingredients) do
        local playerAmount = exports.ox_inventory:Search(source, 'count', ingredient) or 0
        if playerAmount < amount then
            return false
        end
    end
    
    return true
end

-- Fonction pour retirer les ingrédients
local function RemoveIngredients(source, ingredients)
    for ingredient, amount in pairs(ingredients) do
        local success = exports.ox_inventory:RemoveItem(source, ingredient, amount)
        if not success then
            return false
        end
    end
    
    return true
end

-- Fonction pour donner l'item crafté
local function GiveCraftedItem(source, result)
    local success = exports.ox_inventory:AddItem(source, result.item, result.amount)
    if success then
        return true
    end
    
    return false
end

-- Événement pour crafter un item
RegisterNetEvent('pc_crafting:server:craftItem', function(recipeName)
    local source = source
    local recipe = Config.Recipes[recipeName]
    local quantity = 1
    if type(recipeName) == 'table' and recipeName.recipeName then
        recipe = Config.Recipes[recipeName.recipeName]
        quantity = tonumber(recipeName.quantity) or 1
    end
    if not recipe then
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, 'Recette invalide')
        return
    end
    if quantity < 1 then quantity = 1 end
    -- Vérifier les ingrédients pour la quantité totale
    local totalIngredients = {}
    for ingredient, amount in pairs(recipe.ingredients) do
        totalIngredients[ingredient] = amount * quantity
    end
    if not HasIngredients(source, totalIngredients) then
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, Config.Messages['not_enough_ingredients'])
        return
    end
    -- Retirer les ingrédients
    if not RemoveIngredients(source, totalIngredients) then
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, 'Erreur lors de la suppression des ingrédients')
        return
    end
    -- Donner la quantité d'items craftés
    local result = recipe.result
    local success = exports.ox_inventory:AddItem(source, result.item, result.amount * quantity)
    if not success then
        -- Remettre les ingrédients si l'ajout échoue
        for ingredient, amount in pairs(totalIngredients) do
            exports.ox_inventory:AddItem(source, ingredient, amount)
        end
        TriggerClientEvent('pc_crafting:client:craftingResult', source, false, 'Inventaire plein')
        return
    end
    -- Succès
    TriggerClientEvent('pc_crafting:client:craftingResult', source, true, Config.Messages['crafting_success'])
end)

-- Commande pour donner des ingrédients de test (admin seulement)
QBCore.Commands.Add('givecrafting', 'Donner des ingrédients de test pour le crafting', {{name = 'playerid', help = 'ID du joueur'}}, true, function(source, args)
    local targetId = tonumber(args[1])
    if not targetId then
        TriggerClientEvent('QBCore:Notify', source, 'ID de joueur invalide', 'error')
        return
    end
    
    -- Donner des ingrédients de test
    local testIngredients = {
        'steel',
        'wood',
        'screw',
        'bread',
        'ham',
        'lettuce',
        'coffee_bean',
        'water',
        'burger'
    }
    
    for _, item in ipairs(testIngredients) do
        exports.ox_inventory:AddItem(targetId, item, 10)
    end
    
    TriggerClientEvent('QBCore:Notify', source, 'Ingrédients de test donnés au joueur', 'success')
    TriggerClientEvent('QBCore:Notify', targetId, 'Vous avez reçu des ingrédients de test pour le crafting', 'success')
end, 'admin') 

local spawnedProps = {}

AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        for station, data in pairs(Config.CraftingStations) do
            if data.propModel and data.propCoords then
                local model = data.propModel
                local coords = data.propCoords
                RequestModel(model)
                while not HasModelLoaded(model) do Wait(10) end
                local obj = CreateObject(model, coords.x, coords.y, coords.z, false, true, true)
                SetEntityHeading(obj, coords.w)
                FreezeEntityPosition(obj, true)
                SetEntityAsMissionEntity(obj, true, true)
                spawnedProps[station] = obj
            end
        end
    end
end)

AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    -- Sync props pour le joueur qui se connecte
    deferrals.defer()
    Wait(100)
    local src = source
    local propsToSend = {}
    for station, obj in pairs(spawnedProps) do
        if DoesEntityExist(obj) then
            local coords = GetEntityCoords(obj)
            local heading = GetEntityHeading(obj)
            local model = GetEntityModel(obj)
            propsToSend[#propsToSend+1] = {station = station, model = model, coords = coords, heading = heading}
        end
    end
    TriggerClientEvent('pc_crafting:client:syncProps', src, propsToSend)
    deferrals.done()
end)

RegisterNetEvent('pc_crafting:server:requestProps', function()
    local src = source
    local propsToSend = {}
    for station, obj in pairs(spawnedProps) do
        if DoesEntityExist(obj) then
            local coords = GetEntityCoords(obj)
            local heading = GetEntityHeading(obj)
            local model = GetEntityModel(obj)
            propsToSend[#propsToSend+1] = {station = station, model = model, coords = coords, heading = heading}
        end
    end
    TriggerClientEvent('pc_crafting:client:syncProps', src, propsToSend)
end)

-- Protection : respawn le prop s'il est supprimé
CreateThread(function()
    while true do
        Wait(10000)
        for station, obj in pairs(spawnedProps) do
            if not DoesEntityExist(obj) then
                local data = Config.CraftingStations[station]
                if data and data.propModel and data.propCoords then
                    local model = data.propModel
                    local coords = data.propCoords
                    RequestModel(model)
                    while not HasModelLoaded(model) do Wait(10) end
                    local newObj = CreateObject(model, coords.x, coords.y, coords.z, false, true, true)
                    SetEntityHeading(newObj, coords.w)
                    FreezeEntityPosition(newObj, true)
                    SetEntityAsMissionEntity(newObj, true, true)
                    spawnedProps[station] = newObj
                end
            end
        end
    end
end) 