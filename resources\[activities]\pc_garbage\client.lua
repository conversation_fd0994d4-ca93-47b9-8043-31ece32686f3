local QBCore = exports['qb-core']:GetCoreObject()

-- Initialisation de la langue
local Lang = Lang or Locale:new({
    phrases = Translations,
    warnOnMissing = true
})

local ped = nil
local isWorking = false
local currentGroup = nil
local currentVehicle = nil
local collectionBlips = {}
local playerNames = {}
local collectedPoints = {} -- Table pour suivre les points déjà collectés
local endActivityOption = nil -- Pour stocker l'option de fin d'activité
local collectionZones = {} -- Table pour stocker les zones de collecte
local garbageBag = nil -- Pour stocker le sac de poubelle
local garbageBagCoords = nil -- Pour stocker la position du sac de poubelle
local currentRun = nil -- Pour stocker la run actuelle

-- Fonctions utilitaires
local function GetPlayerName(source)
    return playerNames[source] or "Inconnu"
end

local function ShowGroupMenu()
    if not currentGroup then return end
    
    local options = {
        {
            title = "Menu du groupe",
            description = '',
            disabled = true
        }
    }
    
    -- Ajouter le leader
    options[#options + 1] = {
        title = "Leader: " .. GetPlayerName(currentGroup.leader),
        description = "ID: " .. currentGroup.leader,
        disabled = true
    }
    
    -- Ajouter les membres
    for _, memberId in ipairs(currentGroup.members) do
        if memberId ~= currentGroup.leader then
            options[#options + 1] = {
                title = "Membre: " .. GetPlayerName(memberId),
                description = "ID: " .. memberId,
                onSelect = function()
                    if currentGroup.leader == GetPlayerServerId(PlayerId()) then
                        -- Demander confirmation avant de kick
                        local alert = lib.alertDialog({
                            header = 'Confirmation',
                            content = 'Voulez-vous vraiment exclure ' .. GetPlayerName(memberId) .. ' du groupe ?',
                            centered = true,
                            cancel = true
                        })
                        
                        if alert == 'confirm' then
                            TriggerServerEvent('pc_garbage:server:kickMember', memberId)
                        end
                    end
                end,
                canInteract = function()
                    return currentGroup.leader == GetPlayerServerId(PlayerId())
                end
            }
        end
    end
    
    -- Ajouter l'option pour inviter un joueur (uniquement pour le leader)
    if currentGroup.leader == GetPlayerServerId(PlayerId()) then
        options[#options + 1] = {
            title = "Inviter un joueur",
            description = '',
            onSelect = function()
                TriggerServerEvent('pc_garbage:server:getPlayersList')
            end
        }
    end
    
    -- Ajouter l'option pour quitter le groupe
    options[#options + 1] = {
        title = "Quitter le groupe",
        description = '',
        onSelect = function()
            TriggerEvent('pc_garbage:client:leaveGroup')
        end
    }
    
    lib.registerContext({
        id = 'garbage_group_menu',
        title = "Menu du groupe",
        options = options
    })
    
    lib.showContext('garbage_group_menu')
end

local function ShowInviteMenu()
    if not currentGroup then return end
    
    local myId = GetPlayerServerId(PlayerId())
    if currentGroup.leader ~= myId then
        lib.notify({
            title = 'Erreur',
            description = Lang:t('error.not_group_leader'),
            type = 'error'
        })
        return
    end
    
    -- Demander la liste des joueurs au serveur
    TriggerServerEvent('pc_garbage:server:getPlayersList')
end

-- Fonction pour créer le sac de poubelle
local function CreateGarbageBag(coords)
    if garbageBag then
        DeleteObject(garbageBag)
    end
    
    local model = GetHashKey('prop_rub_binbag_01')
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    
    local ped = PlayerPedId()
    garbageBag = CreateObject(model, coords.x, coords.y, coords.z, true, true, true)
    
    -- Attacher directement le sac au joueur
    AttachEntityToEntity(garbageBag, ped, GetPedBoneIndex(ped, 28422), 0.0, -0.2, -0.4, 0.0, 0.0, 0.0, true, true, false, true, 1, true)
    
    -- Charger et jouer l'animation
    RequestAnimDict("anim@heists@narcotics@trash")
    while not HasAnimDictLoaded("anim@heists@narcotics@trash") do
        Wait(0)
    end
    
    TaskPlayAnim(ped, "anim@heists@narcotics@trash", "walk", 8.0, -8.0, -1, 49, 0, false, false, false)
    
    -- Créer une boucle pour maintenir l'animation
    CreateThread(function()
        while garbageBag and DoesEntityExist(garbageBag) and IsEntityAttachedToEntity(garbageBag, ped) do
            if not IsEntityPlayingAnim(ped, "anim@heists@narcotics@trash", "walk", 3) then
                TaskPlayAnim(ped, "anim@heists@narcotics@trash", "walk", 8.0, -8.0, -1, 49, 0, false, false, false)
            end
            Wait(0)
        end
        ClearPedTasks(ped)
    end)
    
    garbageBagCoords = coords
end

-- Fonction pour supprimer le sac de poubelle
local function DeleteGarbageBag()
    if garbageBag then
        DeleteObject(garbageBag)
        garbageBag = nil
        garbageBagCoords = nil
    end
end

-- Fonction pour porter le sac de poubelle
local function CarryGarbageBag()
    if not garbageBag then return end
    
    local ped = PlayerPedId()
    
    -- Charger l'animation
    RequestAnimDict("anim@heists@narcotics@trash")
    while not HasAnimDictLoaded("anim@heists@narcotics@trash") do
        Wait(0)
    end
    
    -- Attacher le sac au joueur
    AttachEntityToEntity(garbageBag, ped, GetPedBoneIndex(ped, 28422), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, true, true, false, true, 1, true)
    
    -- Jouer l'animation de portage
    TaskPlayAnim(ped, "anim@heists@narcotics@trash", "walk", 8.0, -8.0, -1, 49, 0, false, false, false)
    
    -- Créer une boucle pour maintenir l'animation
    CreateThread(function()
        while garbageBag and DoesEntityExist(garbageBag) and IsEntityAttachedToEntity(garbageBag, ped) do
            if not IsEntityPlayingAnim(ped, "anim@heists@narcotics@trash", "walk", 3) then
                TaskPlayAnim(ped, "anim@heists@narcotics@trash", "walk", 8.0, -8.0, -1, 49, 0, false, false, false)
            end
            Wait(0)
        end
        ClearPedTasks(ped)
    end)
end

-- Fonction pour lâcher le sac de poubelle
local function DropGarbageBag()
    if not garbageBag then return end
    
    local ped = PlayerPedId()
    DetachEntity(garbageBag, true, true)
    PlaceObjectOnGroundProperly(garbageBag)
    FreezeEntityPosition(garbageBag, true)
    ClearPedTasks(ped)
    
    -- Arrêter l'animation
    StopAnimTask(ped, "anim@heists@narcotics@trash", "walk", 1.0)
end

-- Fonction pour créer le waypoint
local function CreateWaypoint(coords)
    ClearGpsPlayerWaypoint()
    SetNewWaypoint(coords.x, coords.y)
end

-- Fonction pour supprimer le waypoint
local function RemoveWaypoint()
    ClearGpsPlayerWaypoint()
end

-- Fonction de nettoyage
local function CleanupActivity()
    
    -- Supprimer le véhicule
    if currentVehicle then
        QBCore.Functions.DeleteVehicle(currentVehicle)
        currentVehicle = nil
    end
    
    -- Supprimer le sac de poubelle
    DeleteGarbageBag()
    
    -- Supprimer les blips
    for _, blip in pairs(collectionBlips) do
        RemoveBlip(blip)
    end
    collectionBlips = {}
    
    -- Supprimer les zones de collecte
    for _, zone in pairs(collectionZones) do
        exports.ox_target:removeZone(zone)
    end
    collectionZones = {}
    
    -- Supprimer l'option de fin d'activité
    if endActivityOption then
        exports.ox_target:removeLocalEntity(ped, 'garbage_end_activity')
        endActivityOption = nil
    end
    
    -- Supprimer le waypoint
    RemoveWaypoint()
    
    -- Réinitialiser les variables
    isWorking = false
    collectedPoints = {}
end

-- Fonction pour sélectionner une run aléatoire
local function SelectRandomRun()
    local totalChance = 0
    for _, run in ipairs(Config.Runs) do
        totalChance = totalChance + run.chance
    end
    
    local random = math.random(1, totalChance)
    local currentChance = 0
    
    for _, run in ipairs(Config.Runs) do
        currentChance = currentChance + run.chance
        if random <= currentChance then
            return run
        end
    end
    
    return Config.Runs[1] -- Fallback sur la première run si quelque chose ne va pas
end

-- Création du ped
CreateThread(function()
    -- Chargement du modèle
    local model = GetHashKey(Config.StartPoint.ped.model)
    
    -- Vérification et chargement du modèle
    if not IsModelInCdimage(model) then
        return
    end
    
    RequestModel(model)
    local timeout = 0
    while not HasModelLoaded(model) and timeout < 50 do
        timeout = timeout + 1
        Wait(100)
    end
    
    if not HasModelLoaded(model) then
        return
    end

    -- Création du ped
    ped = CreatePed(4, model, Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y, Config.StartPoint.ped.coords.z - 1.0, Config.StartPoint.ped.coords.w, false, true)
    
    if not DoesEntityExist(ped) then
        return
    end
    
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    SetPedCanRagdoll(ped, false)
    SetPedCanBeTargetted(ped, false)
    SetPedCanBeDraggedOut(ped, false)
    SetPedCanRagdollFromPlayerImpact(ped, false)
    SetEntityAsMissionEntity(ped, true, true)

    -- Création du blip
    local blip = AddBlipForCoord(Config.StartPoint.ped.coords.x, Config.StartPoint.ped.coords.y, Config.StartPoint.ped.coords.z)
    SetBlipSprite(blip, Config.StartPoint.blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.StartPoint.blip.scale)
    SetBlipColour(blip, Config.StartPoint.blip.color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.StartPoint.blip.label)
    EndTextCommandSetBlipName(blip)

    -- Ajout des options ox_target
    exports.ox_target:addLocalEntity(ped, {
        {
            name = 'garbage_create_group',
            icon = 'fas fa-users',
            label = Lang:t('info.create_group'),
            onSelect = function()
                TriggerServerEvent('pc_garbage:server:createGroup')
            end,
            canInteract = function()
                return currentGroup == nil
            end
        },
        {
            name = 'garbage_view_group',
            icon = 'fas fa-users',
            label = Lang:t('info.view_group'),
            onSelect = function()
                ShowGroupMenu()
            end,
            canInteract = function()
                return currentGroup ~= nil
            end
        },
        {
            name = 'garbage_start_activity',
            icon = 'fas fa-truck',
            label = Lang:t('info.start_activity'),
            onSelect = function()
                if not currentGroup then
                    lib.notify({
                        title = 'Erreur',
                        description = Lang:t('error.not_in_group'),
                        type = 'error'
                    })
                    return
                end
                TriggerServerEvent('pc_garbage:server:startActivity')
            end,
            canInteract = function()
                return currentGroup ~= nil and not isWorking
            end
        }
    })
    
    -- Libération du modèle
    SetModelAsNoLongerNeeded(model)
end)

-- Événements
RegisterNetEvent('pc_garbage:client:groupCreated', function(groupId)
    currentGroup = {
        id = groupId,
        leader = GetPlayerServerId(PlayerId()),
        members = {GetPlayerServerId(PlayerId())}
    }
    lib.notify({
        title = 'Succès',
        description = Lang:t('success.group_created'),
        type = 'success'
    })
end)

RegisterNetEvent('pc_garbage:client:groupUpdated', function(group)
    if not group then
        currentGroup = nil
        return
    end
    
    currentGroup = group
    lib.notify({
        title = 'Information',
        description = Lang:t('info.group_members', {count = #group.members}),
        type = 'info'
    })
end)

RegisterNetEvent('pc_garbage:client:leaveGroup', function()
    if not currentGroup then return end
    TriggerServerEvent('pc_garbage:server:leaveGroup')
end)

RegisterNetEvent('pc_garbage:client:invitePlayer', function(data)
    if not data or not data.targetId then return end
    TriggerServerEvent('pc_garbage:server:invitePlayer', data.targetId)
end)

RegisterNetEvent('pc_garbage:client:groupInvitation', function(groupId)
    local options = {
        {
            title = Lang:t('info.group_invitation'),
            description = '',
            disabled = true
        },
        {
            title = Lang:t('info.accept_invitation'),
            description = '',
            onSelect = function()
                TriggerServerEvent('pc_garbage:server:acceptInvitation', groupId)
            end
        },
        {
            title = Lang:t('info.decline_invitation'),
            description = '',
            onSelect = function()
                lib.notify({
                    title = 'Information',
                    description = Lang:t('info.invitation_declined'),
                    type = 'info'
                })
            end
        }
    }
    
    lib.registerContext({
        id = 'garbage_invitation_menu',
        title = Lang:t('info.group_invitation'),
        options = options
    })
    
    lib.showContext('garbage_invitation_menu')
end)

RegisterNetEvent('pc_garbage:client:updatePlayerNames', function(names)
    playerNames = names
end)

RegisterNetEvent('pc_garbage:client:showInviteMenu', function(players)
    if not currentGroup then 
        return 
    end
    
    
    local options = {
        {
            title = "Inviter un joueur",
            description = "Sélectionnez un joueur à proximité",
            disabled = true
        }
    }
    
    local hasPlayers = false
    for playerId, playerName in pairs(players) do
        hasPlayers = true
        options[#options + 1] = {
            title = playerName,
            description = "ID: " .. playerId,
            onSelect = function()
                TriggerServerEvent('pc_garbage:server:invitePlayer', playerId)
            end
        }
    end
    
    if not hasPlayers then
        options[#options + 1] = {
            title = "Aucun joueur à proximité",
            description = "Les joueurs doivent être dans un rayon de 10 mètres",
            disabled = true
        }
    end
    
    lib.registerContext({
        id = 'garbage_invite_menu',
        title = "Inviter un joueur",
        options = options
    })
    
    lib.showContext('garbage_invite_menu')
end)

RegisterNetEvent('pc_garbage:client:activityStarted', function(isLeader)
    if isWorking then return end
    isWorking = true
    collectedPoints = {} -- Réinitialiser les points collectés
    
    -- Sélectionner une run aléatoire
    currentRun = SelectRandomRun()
    
    -- Partager la run sélectionnée avec le serveur
    TriggerServerEvent('pc_garbage:server:setCurrentRun', currentRun.name)
    
    -- Notification du type de run
    lib.notify({
        title = 'Information',
        description = 'Vous avez obtenu une ' .. currentRun.name,
        type = 'info'
    })
    
    -- Spawn du véhicule uniquement si c'est le leader
    if isLeader then
        local vehicleConfig = Config.Vehicles[1]
        QBCore.Functions.SpawnVehicle(vehicleConfig.model, function(vehicle)
            currentVehicle = vehicle
            SetEntityHeading(vehicle, vehicleConfig.coords.w)
            
            -- Gestion du carburant
            local plate = QBCore.Functions.GetPlate(vehicle)
            TriggerServerEvent('qb-fuel:server:SetFuel', plate, 100.0)
            
            -- Donner les clés du véhicule
            TriggerEvent('vehiclekeys:client:SetOwner', plate)
            
            -- Attendre que le véhicule soit complètement chargé
            local attempts = 0
            local function tryShareVehicle()
                if attempts >= 10 then
                    return
                end
                
                local netId = NetworkGetNetworkIdFromEntity(vehicle)
                if netId and netId > 0 then
                    TriggerServerEvent('pc_garbage:server:shareVehicleInfo', netId)
                else
                    attempts = attempts + 1
                    Wait(1000)
                    tryShareVehicle()
                end
            end
            
            tryShareVehicle()
            
            -- Ajouter l'option pour terminer l'activité
            if not endActivityOption then
                endActivityOption = exports.ox_target:addLocalEntity(ped, {
                    {
                        name = 'garbage_end_activity',
                        icon = 'fas fa-check-circle',
                        label = 'Terminer l\'activité',
                        onSelect = function()
                            if not isWorking then return end
                            
                            local allCollected = true
                            for i = 1, #currentRun.collectionPoints do
                                if not collectedPoints[i] then
                                    allCollected = false
                                    break
                                end
                            end
                            
                            -- Vérifier la distance au camion
                            local isNearTruck = false
                            if currentVehicle then
                                local playerCoords = GetEntityCoords(PlayerPedId())
                                local vehicleCoords = GetEntityCoords(currentVehicle)
                                local distance = #(playerCoords - vehicleCoords)
                                isNearTruck = distance <= 50.0
                            end
                            
                            if not allCollected then
                                -- Demander confirmation si tous les points ne sont pas collectés
                                local alert = lib.alertDialog({
                                    header = 'Attention',
                                    content = 'Vous n\'avez pas collecté tous les points. Voulez-vous vraiment terminer l\'activité ? Vous ne recevrez aucune récompense.',
                                    centered = true,
                                    cancel = true
                                })
                                
                                if alert == 'confirm' then
                                    TriggerServerEvent('pc_garbage:server:endActivity', false)
                                    CleanupActivity()
                                end
                            else
                                if not isNearTruck then
                                    -- Avertir que le camion est trop loin
                                    local alert = lib.alertDialog({
                                        header = 'Attention',
                                        content = 'Le camion est trop loin. Si vous terminez maintenant, vous ne recevrez aucune récompense. Voulez-vous quand même terminer ?',
                                        centered = true,
                                        cancel = true
                                    })
                                    
                                    if alert == 'confirm' then
                                        TriggerServerEvent('pc_garbage:server:endActivity', false)
                                        CleanupActivity()
                                    end
                                else
                                    -- Donner les récompenses si tous les points sont collectés et le camion est proche
                                    TriggerServerEvent('pc_garbage:server:endActivity', true)
                                    CleanupActivity()
                                end
                            end
                        end,
                        canInteract = function()
                            return isWorking
                        end
                    }
                })
            end
        end, vehicleConfig.coords, true)
    end
    
    -- Création des blips pour les points de collecte
    for i, point in ipairs(currentRun.collectionPoints) do
        -- Création du blip
        local blip = AddBlipForCoord(point.coords.x, point.coords.y, point.coords.z)
        SetBlipSprite(blip, 1)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.7)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(point.label)
        EndTextCommandSetBlipName(blip)
        collectionBlips[i] = blip
        
        -- Ajout du point de collecte
        local zone = exports.ox_target:addSphereZone({
            coords = point.coords,
            radius = 2.0,
            options = {
                {
                    name = 'collect_garbage_' .. i,
                    icon = 'fas fa-trash',
                    label = Lang:t('info.collecting'),
                    onSelect = function()
                        if not isWorking then 
                            return 
                        end
                        
                        if not currentVehicle then 
                            return 
                        end
                        
                        -- Vérifier si le point a déjà été collecté
                        if collectedPoints[i] then
                            lib.notify({
                                title = 'Information',
                                description = 'Ce point a déjà été collecté',
                                type = 'info'
                            })
                            return
                        end
                        
                        -- Vérifier si le joueur est à proximité du camion
                        local playerCoords = GetEntityCoords(PlayerPedId())
                        local vehicleCoords = GetEntityCoords(currentVehicle)
                        local distance = #(playerCoords - vehicleCoords)
                        
                        if distance > 50.0 then
                            lib.notify({
                                title = 'Erreur',
                                description = 'Vous devez être à proximité du camion pour collecter',
                                type = 'error'
                            })
                            return
                        end
                        
                        -- Vérifier si le point est disponible
                        TriggerServerEvent('pc_garbage:server:checkPointAvailable', i)
                    end
                }
            }
        })
        collectionZones[i] = zone
    end
    
    -- Créer le waypoint vers le premier point
    if #currentRun.collectionPoints > 0 then
        TriggerServerEvent('pc_garbage:server:updateWaypoint', 1)
    end
    
    -- Notification de début
    lib.notify({
        title = 'Information',
        description = Lang:t('success.activity_started'),
        type = 'success'
    })
end)

-- Nouvel événement pour mettre à jour le waypoint
RegisterNetEvent('pc_garbage:client:updateWaypoint', function(pointIndex)
    if not currentRun or not currentRun.collectionPoints[pointIndex] then return end
    
    local coords = currentRun.collectionPoints[pointIndex].coords
    SetNewWaypoint(coords.x, coords.y)
end)

RegisterNetEvent('pc_garbage:client:pointCollected', function(pointIndex)
    collectedPoints[pointIndex] = true
    
    -- Supprimer le blip et la zone
    if collectionBlips[pointIndex] then
        RemoveBlip(collectionBlips[pointIndex])
        collectionBlips[pointIndex] = nil
    end
    
    if collectionZones[pointIndex] then
        exports.ox_target:removeZone(collectionZones[pointIndex])
        collectionZones[pointIndex] = nil
    end
    
    -- Vérifier s'il reste des points à collecter
    local remainingPoints = 0
    local nextPoint = nil
    for i = 1, #currentRun.collectionPoints do
        if not collectedPoints[i] then
            remainingPoints = remainingPoints + 1
            if not nextPoint then
                nextPoint = i
            end
        end
    end
    
    if nextPoint then
        -- Mettre à jour le waypoint vers le prochain point pour tous les joueurs
        TriggerServerEvent('pc_garbage:server:updateWaypoint', nextPoint)
        
        lib.notify({
            title = 'Information',
            description = 'Point collecté ! ' .. remainingPoints .. ' point(s) restant(s)',
            type = 'success'
        })
    else
        -- Tous les points ont été collectés
        ClearGpsPlayerWaypoint()
        lib.notify({
            title = 'Félicitations',
            description = 'Tous les points ont été collectés ! Retournez au point de départ.',
            type = 'success'
        })
    end
end)

-- Nouvel événement pour gérer la disponibilité des points
RegisterNetEvent('pc_garbage:client:pointAvailable', function(pointIndex)
    -- Créer le sac de poubelle et l'attacher directement au joueur
    local point = currentRun.collectionPoints[pointIndex]
    if not point then return end
    
    CreateGarbageBag(point.coords)
    
    -- Ajouter l'option pour déposer le sac dans le camion
    exports.ox_target:addLocalEntity(currentVehicle, {
        {
            name = 'drop_garbage_bag',
            icon = 'fas fa-truck',
            label = 'Déposer le sac',
            onSelect = function()
                DropGarbageBag()
                DeleteGarbageBag()
                
                -- Barre de progression pour la collecte
                if lib.progressBar({
                    duration = 5000,
                    label = Lang:t('info.collecting'),
                    useWhileDead = false,
                    canCancel = true,
                    disable = {
                        car = true,
                        move = true,
                        combat = true
                    },
                    anim = {
                        dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
                        clip = 'machinic_loop_mechandplayer'
                    },
                }) then
                    TriggerServerEvent('pc_garbage:server:completeCollection', pointIndex)
                end
            end,
            canInteract = function()
                return garbageBag ~= nil
            end
        }
    })
end)

RegisterNetEvent('pc_garbage:client:pointNotAvailable', function(reason)
    if reason == 'already_collected' then
        lib.notify({
            title = 'Information',
            description = 'Ce point a déjà été collecté',
            type = 'info'
        })
    elseif reason == 'being_collected' then
        lib.notify({
            title = 'Information',
            description = 'Ce point est en cours de collecte par un autre joueur',
            type = 'info'
        })
    end
end)

-- Nouvel événement pour recevoir l'information du véhicule
RegisterNetEvent('pc_garbage:client:setVehicle', function(netId)
    
    -- Attendre que le véhicule soit disponible
    local attempts = 0
    local function tryGetVehicle()
        if attempts >= 10 then
            return
        end
        
        local vehicle = NetworkGetEntityFromNetworkId(netId)
        if DoesEntityExist(vehicle) then
            currentVehicle = vehicle
        else
            attempts = attempts + 1
            Wait(1000)
            tryGetVehicle()
        end
    end
    
    tryGetVehicle()
end)

-- Nettoyage
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName ~= GetCurrentResourceName() then return end
    CleanupActivity()
end) 