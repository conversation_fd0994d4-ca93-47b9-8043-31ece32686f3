local QBCore = exports['qb-core']:GetCoreObject()
local cooldowns = {}

-- Fonction pour vérifier si un shop est accessible côté serveur
local function IsShopAccessibleServer(shop, playerJob)
    if not shop.jobs then return true end
    local playerGrade = playerJob.grade.level
    for jobName, requiredGrade in pairs(shop.jobs) do
        if playerJob.name == jobName and playerGrade >= requiredGrade then
            return true
        end
    end
    return false
end

-- Vérification utilitaire pour savoir si le shop est fermé à cause d'un job en service
local function IsShopClosedOnDuty(shop)
    if not shop or not shop.shopClosedOnDuty then return false end
    local players = QBCore.Functions.GetQBPlayers()
    for _, player in pairs(players) do
        local job = player.PlayerData.job
        if job and shop.shopClosedOnDuty[job.name] and job.onduty then
            return true
        end
    end
    return false
end

-- Événement pour vérifier l'argent du joueur
RegisterNetEvent('asc_shops:server:checkMoney', function(amount, paymentType, shopName)
    local src = source

    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then 
        TriggerClientEvent('asc_shops:client:checkMoneyResponse', src, false)
        return 
    end

    -- Vérifier si le type de paiement est autorisé pour ce magasin
    local shop = Config.Shops[shopName]
    if not shop then
        TriggerClientEvent('asc_shops:client:checkMoneyResponse', src, false)
        return
    end

    if not shop.payment then
        TriggerClientEvent('asc_shops:client:checkMoneyResponse', src, false)
        return
    end

    if not shop.payment[paymentType] then
        TriggerClientEvent('asc_shops:client:checkMoneyResponse', src, false)
        return
    end

    -- Pour un magasin de vente, on vérifie toujours que le joueur a assez d'argent
    -- car il recevra de l'argent en échange de ses items
    local hasEnough = false
    if paymentType == 'cash' then
        local cashAmount = exports.ox_inventory:GetItem(src, 'money', nil, true)
        hasEnough = cashAmount and cashAmount >= amount
    elseif paymentType == 'bank' then
        local bankAmount = Player.Functions.GetMoney('bank')
        hasEnough = bankAmount >= amount
    elseif paymentType == 'black_money' then
        local blackMoneyAmount = exports.ox_inventory:GetItem(src, 'black_money', nil, true)
        if blackMoneyAmount then
            hasEnough = blackMoneyAmount >= amount
        else
            hasEnough = false
        end
    end
    TriggerClientEvent('asc_shops:client:checkMoneyResponse', src, hasEnough)
end)

-- Fonction utilitaire pour vérifier la limite d'achat sur 24h
local function CheckAndRegisterItemLimit(citizenid, shopName, itemName, quantity, maxPer24h, cb)
    local now = os.time()
    local since = now - 24*60*60
    exports.oxmysql:fetch('SELECT SUM(quantity) as total FROM pc_shop_item_limits WHERE citizenid = @citizenid AND shop_name = @shop AND item_name = @item AND timestamp > @since', {
        ['@citizenid'] = citizenid,
        ['@shop'] = shopName,
        ['@item'] = itemName,
        ['@since'] = since
    }, function(result)
        local total = tonumber(result[1].total) or 0
        if total + quantity > maxPer24h then
            cb(false, total)
        else
            -- Enregistre l'achat
            exports.oxmysql:execute('INSERT INTO pc_shop_item_limits (citizenid, shop_name, item_name, quantity, timestamp) VALUES (@citizenid, @shop, @item, @quantity, @timestamp)', {
                ['@citizenid'] = citizenid,
                ['@shop'] = shopName,
                ['@item'] = itemName,
                ['@quantity'] = quantity,
                ['@timestamp'] = now
            }, function()
                cb(true, total)
            end)
        end
    end)
end

-- Événement pour acheter un item
RegisterNetEvent('asc_shops:server:buyItem', function(itemName, amount, shopName, paymentType, type)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then 
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Joueur non trouvé')
        return 
    end
    local shop = Config.Shops[shopName]
    if not shop then 
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Magasin non trouvé')
        return 
    end
    if IsShopClosedOnDuty(shop) then
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Le magasin est actuellement fermé (service en cours)')
        return
    end
    if not IsShopAccessibleServer(shop, Player.PlayerData.job) then
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Ce magasin n\'est pas accessible actuellement')
        return
    end
    local item = nil
    for _, shopItem in ipairs(shop.items) do
        if shopItem.name == itemName then
            item = shopItem
            break
        end
    end
    if not item then
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Item non disponible')
        return
    end
    -- Vérification de la limite d'achat sur 24h
    local function processBuy()
        local totalPrice = item.price * amount
        -- Vérifier d'abord si le joueur peut porter l'item
        local canAdd = exports.ox_inventory:CanCarryItem(src, itemName, amount)
        if not canAdd then
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Inventaire plein')
            return
        end
        -- Ensuite vérifier si le joueur a assez d'argent
        local hasEnough = false
        if paymentType == 'cash' then
            local cashAmount = exports.ox_inventory:GetItem(src, 'money', nil, true)
            hasEnough = cashAmount and cashAmount >= totalPrice
        elseif paymentType == 'bank' then
            local bankAmount = Player.Functions.GetMoney('bank')
            hasEnough = bankAmount >= totalPrice
        elseif paymentType == 'black_money' then
            local blackMoney = exports.ox_inventory:GetItem(src, 'black_money', nil, true)
            hasEnough = blackMoney and blackMoney >= totalPrice
        end
        if not hasEnough then
            local errorMessage = paymentType == 'cash' and 'Pas assez d\'argent liquide' or
                                paymentType == 'bank' and 'Pas assez d\'argent en banque' or
                                'Pas assez d\'argent sale'
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, errorMessage)
            return
        end
        -- Retirer l'argent
        local moneyRemoved = false
        if paymentType == 'cash' then
            moneyRemoved = exports.ox_inventory:RemoveItem(src, 'money', totalPrice)
        elseif paymentType == 'bank' then
            Player.Functions.RemoveMoney('bank', totalPrice)
            moneyRemoved = true
        elseif paymentType == 'black_money' then
            moneyRemoved = exports.ox_inventory:RemoveItem(src, 'black_money', totalPrice)
        end
        if not moneyRemoved then
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Erreur lors du paiement')
            return
        end
        -- Ajouter l'item
        local added = exports.ox_inventory:AddItem(src, itemName, amount)
        if added then
            TriggerClientEvent('asc_shops:client:buyResponse', src, true, 'Achat réussi')
        else
            -- Rembourser en cas d'erreur
            if paymentType == 'cash' then
                exports.ox_inventory:AddItem(src, 'money', totalPrice)
            elseif paymentType == 'bank' then
                Player.Functions.AddMoney('bank', totalPrice)
            elseif paymentType == 'black_money' then
                exports.ox_inventory:AddItem(src, 'black_money', totalPrice)
            end
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Erreur lors de l\'achat')
        end
    end
    if item.maxPer24h then
        CheckAndRegisterItemLimit(Player.PlayerData.citizenid, shopName, itemName, amount, item.maxPer24h, function(ok, total)
            if not ok then
                TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Limite d\'achat atteinte pour cet item sur 24h ('..item.maxPer24h..')')
                return
            end
            processBuy()
        end)
    else
        processBuy()
    end
end)

-- Fonction utilitaire pour enregistrer un achat dans la base
local function RegisterItemPurchase(citizenid, shopName, itemName, quantity)
    local now = os.time()
    exports.oxmysql:execute('INSERT INTO pc_shop_item_limits (citizenid, shop_name, item_name, quantity, timestamp) VALUES (@citizenid, @shop, @item, @quantity, @timestamp)', {
        ['@citizenid'] = citizenid,
        ['@shop'] = shopName,
        ['@item'] = itemName,
        ['@quantity'] = quantity,
        ['@timestamp'] = now
    })
end

-- Événement pour acheter plusieurs items
RegisterNetEvent('asc_shops:server:buyItems', function(items, shopName, paymentType, type)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then 
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Joueur non trouvé')
        return 
    end
    local shop = Config.Shops[shopName]
    if not shop then 
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Magasin non trouvé')
        return 
    end
    if IsShopClosedOnDuty(shop) then
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Le magasin est actuellement fermé (service en cours)')
        return
    end
    if not IsShopAccessibleServer(shop, Player.PlayerData.job) then
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Ce magasin n\'est pas accessible actuellement')
        return
    end
    -- Vérification des limites pour chaque item (asynchrone)
    local function checkLimitsAndRegister(index)
        if index > #items then
            processMultiBuy()
            return
        end
        local cartItem = items[index]
        local shopItem = nil
        for _, item in ipairs(shop.items) do
            if item.name == cartItem.name then
                shopItem = item
                break
            end
        end
        if shopItem and shopItem.maxPer24h then
            local now = os.time()
            local since = now - 24*60*60
            exports.oxmysql:fetch('SELECT SUM(quantity) as total FROM pc_shop_item_limits WHERE citizenid = @citizenid AND shop_name = @shop AND item_name = @item AND timestamp > @since', {
                ['@citizenid'] = Player.PlayerData.citizenid,
                ['@shop'] = shopName,
                ['@item'] = cartItem.name,
                ['@since'] = since
            }, function(result)
                local total = tonumber(result[1].total) or 0
                local restant = shopItem.maxPer24h - total
                if restant <= 0 then
                    TriggerClientEvent('ox_lib:notify', src, {
                        title = 'Limite atteinte',
                        description = 'Vous avez déjà atteint la limite maximale d\'achat pour l\'item '..cartItem.name..' sur 24h ('..shopItem.maxPer24h..')',
                        type = 'error',
                        position = 'top-right',
                        duration = 5000
                    })
                    TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Limite d\'achat atteinte pour l\'item '..cartItem.name..' sur 24h ('..shopItem.maxPer24h..')')
                    return
                end
                if total + cartItem.quantity > shopItem.maxPer24h then
                    TriggerClientEvent('ox_lib:notify', src, {
                        title = 'Limite atteinte',
                        description = 'Vous ne pouvez en acheter que '..restant..' (limite sur 24h : '..shopItem.maxPer24h..')',
                        type = 'error',
                        position = 'top-right',
                        duration = 5000
                    })
                    TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Vous ne pouvez en acheter que '..restant..' (limite sur 24h : '..shopItem.maxPer24h..')')
                    return
                end
                -- Enregistre l'achat dans la base
                RegisterItemPurchase(Player.PlayerData.citizenid, shopName, cartItem.name, cartItem.quantity)
                checkLimitsAndRegister(index + 1)
            end)
        else
            -- Enregistre aussi les achats sans limite pour cohérence
            RegisterItemPurchase(Player.PlayerData.citizenid, shopName, cartItem.name, cartItem.quantity)
            checkLimitsAndRegister(index + 1)
        end
    end
    -- Logique d'achat (inchangée)
    function processMultiBuy()
        local totalPrice = 0
        local validItems = {}
        for _, cartItem in ipairs(items) do
            local shopItem = nil
            for _, item in ipairs(shop.items) do
                if item.name == cartItem.name then
                    shopItem = item
                    break
                end
            end
            if not shopItem then
                TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Item non disponible')
                return
            end
            local itemPrice = shopItem.price * cartItem.quantity
            totalPrice = totalPrice + itemPrice
            if not exports.ox_inventory:CanCarryItem(src, cartItem.name, cartItem.quantity) then
                TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Inventaire plein')
                return
            end
            table.insert(validItems, {
                name = cartItem.name,
                quantity = cartItem.quantity,
                price = itemPrice
            })
        end
        local hasEnough = false
        if paymentType == 'cash' then
            local cashAmount = exports.ox_inventory:GetItem(src, 'money', nil, true)
            hasEnough = cashAmount and cashAmount >= totalPrice
        elseif paymentType == 'bank' then
            local bankAmount = Player.Functions.GetMoney('bank')
            hasEnough = bankAmount >= totalPrice
        elseif paymentType == 'black_money' then
            local blackMoney = exports.ox_inventory:GetItem(src, 'black_money', nil, true)
            hasEnough = blackMoney and blackMoney >= totalPrice
        end
        if not hasEnough then
            local errorMessage = paymentType == 'cash' and 'Pas assez d\'argent liquide' or
                                paymentType == 'bank' and 'Pas assez d\'argent en banque' or
                                'Pas assez d\'argent sale'
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, errorMessage)
            return
        end
        local moneyRemoved = false
        if paymentType == 'cash' then
            moneyRemoved = exports.ox_inventory:RemoveItem(src, 'money', totalPrice)
        elseif paymentType == 'bank' then
            Player.Functions.RemoveMoney('bank', totalPrice)
            moneyRemoved = true
        elseif paymentType == 'black_money' then
            moneyRemoved = exports.ox_inventory:RemoveItem(src, 'black_money', totalPrice)
        end
        if not moneyRemoved then
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Erreur lors du paiement')
            return
        end
        local allItemsAdded = true
        for _, item in ipairs(validItems) do
            local added = exports.ox_inventory:AddItem(src, item.name, item.quantity)
            if not added then
                allItemsAdded = false
                break
            end
        end
        if allItemsAdded then
            TriggerClientEvent('asc_shops:client:buyResponse', src, true, 'Achat réussi')
        else
            if paymentType == 'cash' then
                exports.ox_inventory:AddItem(src, 'money', totalPrice)
            elseif paymentType == 'bank' then
                Player.Functions.AddMoney('bank', totalPrice)
            elseif paymentType == 'black_money' then
                exports.ox_inventory:AddItem(src, 'black_money', totalPrice)
            end
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Erreur lors de l\'achat')
        end
    end
    checkLimitsAndRegister(1)
end)

-- Événement pour vendre plusieurs items
RegisterNetEvent('asc_shops:server:sellItems', function(items, shopName, paymentType, type)
    local src = source

    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then 
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Joueur non trouvé')
        return 
    end

    local shop = Config.Shops[shopName]
    if not shop then 
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Magasin non trouvé')
        return 
    end

    -- Vérifier si le shop est accessible pour ce joueur
    if not IsShopAccessibleServer(shop, Player.PlayerData.job) then
        TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Ce magasin n\'est pas accessible actuellement')
        return
    end

    -- Calculer le prix total
    local totalPrice = 0
    local validItems = {}

    for _, cartItem in ipairs(items) do
        local shopItem = nil
        for _, item in ipairs(shop.items) do
            if item.name == cartItem.name then
                shopItem = item
                break
            end
        end

        if not shopItem then
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Item non disponible')
            return
        end

        -- Vérifier si le joueur a l'item
        local hasItem = exports.ox_inventory:Search(src, 'count', cartItem.name)
        if not hasItem or hasItem < cartItem.quantity then
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Vous n\'avez pas assez de cet item')
            return
        end

        local itemPrice = shopItem.price * cartItem.quantity
        totalPrice = totalPrice + itemPrice

        table.insert(validItems, {
            name = cartItem.name,
            quantity = cartItem.quantity,
            price = itemPrice
        })
    end

    -- Procéder à la vente
    for _, item in ipairs(validItems) do
        if exports.ox_inventory:RemoveItem(src, item.name, item.quantity) then
            -- Donner l'argent
            if paymentType == 'cash' then
                exports.ox_inventory:AddItem(src, 'money', item.price)
            elseif paymentType == 'bank' then
                Player.Functions.AddMoney('bank', item.price)
            elseif paymentType == 'black_money' then
                exports.ox_inventory:AddItem(src, 'black_money', item.price)
            end
        else
            TriggerClientEvent('asc_shops:client:buyResponse', src, false, 'Erreur lors de la vente')
            return
        end
    end

    TriggerClientEvent('asc_shops:client:buyResponse', src, true, 'Vente réussie')
end)

-- Notifier tous les clients de rafraîchir les shops quand un joueur change de service
RegisterNetEvent('pc_shops:playerDutyChanged', function()
    TriggerClientEvent('pc_shops:refreshShops', -1)
end)

-- Callback pour vérifier si le shop doit être fermé (au moins un job de shopClosedOnDuty est en service)
RegisterNetEvent('pc_shops:checkShopClosed', function(shopName)
    local src = source
    local shop = Config.Shops[shopName]
    local isClosed = false
    if shop and shop.shopClosedOnDuty then
        local players = QBCore.Functions.GetQBPlayers()
        for _, player in pairs(players) do
            local job = player.PlayerData.job
            if job and shop.shopClosedOnDuty[job.name] and job.onduty then
                isClosed = true
                break
            end
        end
    end
    TriggerClientEvent('pc_shops:checkShopClosedResult', src, isClosed, shopName)
end) 