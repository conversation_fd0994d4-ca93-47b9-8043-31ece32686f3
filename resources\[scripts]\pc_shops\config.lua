-- CONFIG À FAIRE --

Config = {}

Config.Debug = true

-- Configuration globale pour la vérification du service de job
Config.EnableJobServiceCheck = true -- Active/désactive la vérification du service de job pour tous les shops

-- Configuration des shops
Config.Shops = {
    -- ['Exemple'] = {
    --     label = 'Exemple',
    --     type = 'both',                                                   -- 'buy' pour acheter, 'sell' pour vendre, 'both' pour les deux
    --     coords = {
    --         vector3(24.36, -1346.28, 28.5),                              -- Pour un shop sans peds
    --         vector3(25.54, -1344.03, 29.5)
    --     },
    --     peds = {                                                         -- Pour un shop avec peds
    --         {
    --             enabled = true,
    --             model = "a_m_m_business_01",
    --             coords = vec4(24.36, -1346.28, 28.5, 260.82),
    --             scenario = "WORLD_HUMAN_CLIPBOARD"
    --         },
    --         {
    --             enabled = true,
    --             model = "a_f_y_business_02",
    --             coords = vec4(25.54, -1344.03, 29.5, 61.27),
    --             scenario = "WORLD_HUMAN_CLIPBOARD"
    --         }
    --     },
    --     blip = {
    --         enabled = true,
    --         sprite = 59,
    --         color = 2,
    --         scale = 0.7,
    --         label = "Magasin 1"
    --     },
    -- items = {
    --     { name = 'water', price = 100, maxPer24h = 10 },
    --     { name = 'burger', price = 100, maxPer24h = 5 },
    -- },
    --     payment = {                                              -- Type de paiement autorisé
    --         cash = true,      -- Accepte l'argent liquide
    --         bank = true,      -- Accepte les paiements par carte
    --         black_money = true -- Accepte l'argent sale
    --     },
    --     jobs = {                                              -- Jobs qui peuvent acheter/vendre
    --         ['police'] = 0,
    --         ['mechanic'] = 0
    --     },
    -- shopClosedOnDuty = {
    --     ['police'] = true,
    -- },
    -- },

    ['Depanneur'] = {
        label = 'Dépanneur',
        type = 'buy',
        peds = {
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(24.41, -1346.59, 28.5, 275.8),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(-3039.62, 584.3, 6.91, 11.92),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(-3243.08, 1000.04, 11.83, 354.08),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(1728.19, 6415.87, 34.04, 246.08),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(1697.29, 4923.45, 41.06, 322.52),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(1959.61, 3740.87, 31.34, 294.86),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(549.16, 2670.39, 41.16, 96.22),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(2677.21, 3279.82, 54.24, 334.62),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(2556.26, 380.87, 107.62, 357.97),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(372.7, 327.21, 102.57, 256.99),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(1134.32, -983.04, 45.42, 277.76),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(-1221.43, -907.94, 11.33, 34.96),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(-1486.69, -377.59, 39.16, 134.66),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(-2966.36, 391.56, 14.04, 86.36),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(1165.31, 2710.78, 37.16, 179.23),
            },
            {
                enabled = true,
                model = "mp_m_shopkeep_01",
                coords = vec4(1392.0, 3606.08, 33.98, 197.25),
            },
        },

        blip = {
            enabled = true,
            sprite = 59,
            color = 2,
            scale = 0.8,
            label = "Dépanneur"
        },

        items = {
            { name = 'water', price = 100 },
            { name = 'burger', price = 100 },
        },

        payment = {
            cash = true,
            bank = true,
            black_money = false
        },
    },
    
    ['clothestore'] = {
        label = 'Magasin de vêtement',
        type = 'buy',
        peds = {
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(73.98, -1392.83, 28.38, 268.65),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(1695.29, 4822.98, 41.06, 96.22),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(-708.62, -152.39, 37.42, 118.12),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(-1193.8, -766.93, 16.32, 212.34),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(427.02, -806.3, 28.49, 90.89),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(-164.68, -302.18, 38.73, 250.8),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(-823.0, -1072.24, 10.33, 209.65),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(-1449.2, -238.31, 48.81, 46.74),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords =  vec4(5.69, 6511.34, 30.88, 39.76),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(612.84, 2763.48, 41.09, 273.84),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(1196.72, 2711.68, 37.22, 179.52),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(-3169.65, 1042.72, 19.86, 64.72),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(-1102.45, 2711.74, 18.11, 217.3),
            },
            {
                enabled = true,
                model = "ig_screen_writer",
                coords = vec4(126.74, -224.71, 53.56, 69.98),
            },
        },
   
        items = {
            { name = 'outfit_bag', price = 500 },
        },

        payment = {
            cash = true,
            bank = true,
            black_money = false
        },

    },
}

-- Configuration de l'interface
Config.UI = {
    title = "Magasin",
    currency = "$",
    maxAmount = 5000,
    maxItems = 5000,
}