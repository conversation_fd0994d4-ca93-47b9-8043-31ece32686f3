return {
    timeOut = 2700000,
    minimumPolice = 0,
    notEnoughPoliceNotify = true,
    reward = {
        minAmount = 1,
        maxAmount = 2,
        items = {
            [1] = {name = 'rolex', min = 1, max = 4},
            [2] = {name = 'diamond_ring', min = 1, max = 4},
            [3] = {name = 'goldchain', min = 1, max = 4},
            [4] = {name = '10kgoldchain', min = 1, max = 2},
        },
    },

    allowedWeapons = {
        [`weapon_smg`] = true,
        [`weapon_combatpdw`] = true,
        [`weapon_gusenberg`] = true,
    
        [`weapon_pumpshotgun`] = true,
        [`weapon_pumpshotgun_mk2`] = true,
        [`weapon_sawnoffshotgun`] = true,
        [`weapon_assaultshotgun`] = true,
        [`weapon_bullpupshotgun`] = true,
        [`weapon_musket`] = true,
        [`weapon_heavyshotgun`] = true,
        [`weapon_dbshotgun`] = true,
        [`weapon_autoshotgun`] = true,
        [`weapon_combatshotgun`] = true,
    
        [`weapon_assaultrifle`] = true,
        [`weapon_assaultrifle_mk2`] = true,
        [`weapon_carbinerifle`] = true,
        [`weapon_carbinerifle_mk2`] = true,
        [`weapon_advancedrifle`] = true,
        [`weapon_specialcarbine`] = true,
        [`weapon_specialcarbine_mk2`] = true,
        [`weapon_bullpuprifle`] = true,
        [`weapon_bullpuprifle_mk2`] = true,
        [`weapon_compactrifle`] = true,
        [`weapon_militaryrifle`] = true,
        [`weapon_heavyrifle`] = true,
        [`weapon_tacticalrifle`] = true,
    },
}
